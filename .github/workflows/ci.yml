name: 小概率项目 CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置 Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          backend/package-lock.json
          manage/package-lock.json
    
    - name: 安装前端依赖
      run: |
        cd frontend
        npm ci
    
    - name: 安装后端依赖
      run: |
        cd backend
        npm ci
    
    - name: 安装管理后台依赖
      run: |
        cd manage
        npm ci
    
    - name: 运行代码质量检查
      run: |
        cd frontend
        npm run lint
    
    - name: 运行TypeScript类型检查
      run: |
        cd frontend
        npm run type-check
    
    - name: 运行单元测试
      run: |
        cd frontend
        npm run test:unit
    
    - name: 运行测试覆盖率
      run: |
        cd frontend
        npm run test:coverage
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: 前端覆盖率
    
    - name: 构建前端
      run: |
        cd frontend
        npm run build
    
    - name: 构建管理后台
      run: |
        cd manage
        npm run build
    
    - name: 启动后端服务
      run: |
        cd backend
        npm start &
        sleep 10
      env:
        NODE_ENV: test
    
    - name: 安装 Playwright
      run: |
        cd frontend
        npx playwright install
    
    - name: 运行E2E测试
      run: |
        cd frontend
        npm run test:e2e
    
    - name: 上传E2E测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-report
        path: frontend/playwright-report/
    
    - name: 上传测试截图
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: test-screenshots
        path: frontend/test-results/

  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          manage/package-lock.json
    
    - name: 安装依赖并构建前端
      run: |
        cd frontend
        npm ci
        npm run build
    
    - name: 安装依赖并构建管理后台
      run: |
        cd manage
        npm ci
        npm run build
    
    - name: 上传构建产物
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: |
          frontend/dist
          manage/dist

  deploy:
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 下载构建产物
      uses: actions/download-artifact@v3
      with:
        name: build-files
    
    - name: 部署到生产环境
      run: |
        echo "🚀 部署到生产环境..."
        # 这里可以添加实际的部署脚本
        # 例如：部署到服务器、CDN等
        
    - name: 发送部署通知
      run: |
        echo "✅ 部署完成！"
        # 这里可以添加通知逻辑
        # 例如：发送邮件、Slack通知等 