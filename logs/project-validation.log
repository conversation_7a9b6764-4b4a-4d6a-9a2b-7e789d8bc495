2025-07-17 09:37:15 [INFO] 开始项目结构验证
2025-07-17 09:37:15 [PASS] 项目规则文件存在
2025-07-17 09:37:15 [PASS] 根目录文件检查通过
2025-07-17 09:37:15 [PASS] 目录存在: frontend
2025-07-17 09:37:15 [PASS] 目录存在: backend
2025-07-17 09:37:15 [PASS] 目录存在: scripts
2025-07-17 09:37:15 [PASS] 目录存在: tests
2025-07-17 09:37:15 [PASS] 目录存在: docs
2025-07-17 09:37:15 [PASS] 目录存在: prototypes
2025-07-17 09:37:15 [PASS] 所有必需目录都存在
2025-07-17 09:37:15 [WARN] scripts目录中的非脚本文件: scripts/README.md
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/auto-recovery.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/check-services.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/check.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/complete-fix.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/complete-restart.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/dev-start.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/dev-status.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/dev-stop.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/immediate-fix.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/install-devmatch.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/install-service-manager.sh
2025-07-17 09:37:15 [WARN] scripts目录中的非脚本文件: scripts/package-lock.json
2025-07-17 09:37:15 [WARN] scripts目录中的非脚本文件: scripts/package.json
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/project-validator.sh
2025-07-17 09:37:15 [WARN] scripts目录中的非脚本文件: scripts/service-manager.js
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/service-manager.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/start-all-services.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/start-services.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/start.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/stop-services.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/stop.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/test-project-publish-button.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/test-project-publish.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/test.sh
2025-07-17 09:37:15 [PASS] 脚本文件正确归类: scripts/ui-restoration-test.sh
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/e2e-auth-test.js
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/final-test.js
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/frontend-auth-test-report.json
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/strict-test-flow.js
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/test-frontend-auth.js
2025-07-17 09:37:15 [PASS] 测试文件正确归类: tests/test-services.js
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/AUTHENTICATION_TEST_REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/DEVMATCH-SERVICE-GUIDE.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/PROJECT-ORGANIZATION-SUMMARY.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/PROJECT-PUBLISH-BUTTON-FIX-REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/PROJECT-PUBLISH-COMPLETION-REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/PROJECT-PUBLISH-FINAL-REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/README-UI-TESTING-INTEGRATION.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/SYSTEM_READY_REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/TESTING.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/UI-ANALYSIS-REPORT.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/dao.md
2025-07-17 09:37:15 [PASS] 文档文件正确归类: docs/统一服务管理解决方案.md
2025-07-17 09:37:15 [PASS] 原型图文件正确归类: prototypes/academy.html
2025-07-17 09:37:15 [PASS] 原型图文件正确归类: prototypes/account-settings.html
2025-07-17 09:37:15 [PASS] 原型图文件正确归类: prototypes/advice.html
2025-07-17 09:37:15 [PASS] 原型图文件正确归类: prototypes/auth.html
2025-07-17 09:37:15 [PASS] 原型图文件正确归类: prototypes/homepage.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/investment.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/profile.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/project-detail.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/project-publish.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/projects-list-v2-mix.html
2025-07-17 09:37:16 [PASS] 原型图文件正确归类: prototypes/projects-list.html
2025-07-17 09:37:16 [WARN] 脚本文件命名不符合kebab-case: README.md
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: auto-recovery.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: check-services.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: check.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: complete-fix.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: complete-restart.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: dev-start.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: dev-status.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: dev-stop.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: immediate-fix.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: install-devmatch.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: install-service-manager.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: package-lock.json
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: package.json
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: project-validator.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: service-manager.js
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: service-manager.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: start-all-services.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: start-services.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: start.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: stop-services.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: stop.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: test-project-publish-button.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: test-project-publish.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: test.sh
2025-07-17 09:37:16 [PASS] 脚本文件命名符合规范: ui-restoration-test.sh
2025-07-17 09:37:16 [WARN] 有 1 个文件命名不符合规范
2025-07-17 09:37:16 [PASS] 根目录无日志文件
================================
项目结构验证报告
生成时间: 2025-07-17 09:37:16
项目路径: /Users/<USER>/Downloads/match
================================
总检查项: 92
通过: 86
失败: 0
警告: 6
================================

