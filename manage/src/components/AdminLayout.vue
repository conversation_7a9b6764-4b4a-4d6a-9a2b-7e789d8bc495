<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <h2>小概率管理后台</h2>
      </div>
      <nav class="sidebar-nav">
        <router-link to="/dashboard" class="nav-item">
          <span class="nav-icon">📊</span>
          首页统计
        </router-link>
        <router-link to="/review" class="nav-item">
          <span class="nav-icon">⏳</span>
          待审核项目
          <span v-if="pendingCount > 0" class="badge">{{ pendingCount }}</span>
        </router-link>
        <router-link to="/projects" class="nav-item">
          <span class="nav-icon">📝</span>
          项目管理
        </router-link>
        <router-link to="/users" class="nav-item">
          <span class="nav-icon">👥</span>
          用户管理
        </router-link>
        <router-link to="/dao" class="nav-item">
          <span class="nav-icon">🏛️</span>
          DAO治理
        </router-link>
        <router-link to="/investment" class="nav-item">
          <span class="nav-icon">💰</span>
          投资管理
        </router-link>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="header">
        <div class="header-left">
          <h1>{{ pageTitle }}</h1>
        </div>
        <div class="header-right">
          <span class="admin-name">{{ authStore.admin?.name }}</span>
          <button @click="handleLogout" class="btn btn-secondary">退出登录</button>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="content">
        <slot />
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import api from '../utils/api.js'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const pendingCount = ref(0)

// 页面标题映射
const pageTitle = computed(() => {
  const titleMap = {
    'Dashboard': '数据统计',
    'Review': '待审核项目',
    'Projects': '项目管理',
    'Users': '用户管理',
    'ProjectDetail': '项目详情',
    'DAOManagement': 'DAO治理管理'
  }
  return titleMap[route.name] || '管理后台'
})

// 获取待审核项目数量
const getPendingCount = async () => {
  try {
    const response = await api.get('/admin/review/pending?pageSize=1')
    if (response.success) {
      pendingCount.value = response.data.total
    }
  } catch (error) {
    console.error('获取待审核项目数量失败:', error)
  }
}

// 退出登录
const handleLogout = () => {
  authStore.logout()
  router.push('/login')
}

onMounted(() => {
  getPendingCount()
  // 每30秒更新一次待审核数量
  setInterval(getPendingCount, 30000)
})
</script>

<style scoped>
.admin-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 250px;
  background: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #34495e;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
}

.nav-item:hover {
  background-color: #34495e;
  color: white;
}

.nav-item.router-link-active {
  background-color: #3498db;
  color: white;
}

.nav-icon {
  margin-right: 10px;
  font-size: 16px;
}

.badge {
  background-color: #e74c3c;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 11px;
  margin-left: auto;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  background: white;
  padding: 0 30px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.admin-name {
  color: #666;
  font-weight: 500;
}

.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-layout {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .sidebar-nav {
    display: flex;
    padding: 10px 0;
    overflow-x: auto;
  }
  
  .nav-item {
    white-space: nowrap;
    padding: 8px 16px;
  }
  
  .content {
    padding: 20px;
  }
}
</style> 