<template>
  <AdminLayout>
    <div v-if="project" class="project-detail">
      <div class="detail-header">
        <button @click="$router.go(-1)" class="btn btn-secondary">← 返回</button>
        <div class="header-actions">
          <span :class="['status-badge', `status-${project.reviewStatus}`]">
            {{ getStatusName(project.reviewStatus) }}
          </span>
          <div v-if="project.reviewStatus === 'pending'" class="review-actions">
            <button @click="approveProject" class="btn btn-success mr-2">通过审核</button>
            <button @click="showRejectModal = true" class="btn btn-danger">拒绝项目</button>
          </div>
        </div>
      </div>

      <div class="detail-content">
        <div class="card">
          <h2>{{ project.title }}</h2>
          <div class="project-meta">
            <span>📅 {{ formatDate(project.createdAt) }}</span>
            <span>👤 {{ project.owner.name }}</span>
            <span>🏷️ {{ getCategoryName(project.category) }}</span>
            <span v-if="project.durationMonths">⏱️ {{ project.durationMonths }}个月</span>
          </div>
          <p class="project-description">{{ project.description }}</p>
        </div>

        <div class="card">
          <h3>项目详细信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>时间周期</label>
              <span>{{ project.durationMonths ? project.durationMonths + '个月' : project.timeframe || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>团队规模</label>
              <span>{{ project.teamSize }}人</span>
            </div>
            <div class="info-item">
              <label>紧急程度</label>
              <span>{{ getUrgencyName(project.urgency) }}</span>
            </div>
            <div class="info-item">
              <label>工作方式</label>
              <span>{{ project.remote ? '远程' : '现场' }}</span>
            </div>
          </div>
        </div>

        <div class="card">
          <h3>人员需求</h3>
          <div class="requirements-list">
            <div v-for="(req, index) in project.requirements" :key="index" class="requirement-item">
              <div class="requirement-card">
                <div class="requirement-header">
                  <span class="role-name">{{ req.role || '技能需求' }}</span>
                  <span class="cooperation-type">{{ getCooperationType(req.cooperation) }}</span>
                </div>
                <div class="requirement-body">
                  <div class="skills">
                    <strong>技能要求：</strong>
                    <span v-if="Array.isArray(req.skillName)">
                      <span v-for="(skill, skillIndex) in req.skillName" :key="skillIndex" class="skill-tag">
                        {{ skill }}
                      </span>
                    </span>
                    <span v-else class="skill-tag">{{ req.skillName }}</span>
                  </div>
                  <div class="salary-info" v-if="req.salaryAmount">
                    <strong>薪酬：</strong>{{ req.salaryAmount }}
                  </div>
                  <div class="equity-info" v-if="req.equityAmount">
                    <strong>股权：</strong>{{ req.equityAmount }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目图片 -->
        <div class="card" v-if="project.mainImage || (project.images && project.images.length > 0)">
          <h3>项目图片</h3>
          <div class="project-images">
            <img v-if="project.mainImage" :src="project.mainImage" alt="主图" class="main-image">
            <div v-if="project.images && project.images.length > 0" class="additional-images">
              <img v-for="(image, index) in project.images" :key="index" :src="image" alt="项目图片" class="project-image">
            </div>
          </div>
        </div>

        <!-- 项目亮点 -->
        <div class="card" v-if="project.highlights">
          <h3>项目亮点</h3>
          <p>{{ project.highlights }}</p>
        </div>

        <!-- 联系方式 -->
        <div class="card" v-if="project.contactInfo">
          <h3>联系方式</h3>
          <p>{{ project.contactInfo }}</p>
        </div>

        <!-- 工作安排 -->
        <div class="card">
          <h3>工作安排</h3>
          <div class="info-grid">
            <div class="info-item" v-if="project.workLocation">
              <label>工作地点</label>
              <span>{{ getWorkLocationName(project.workLocation) }}</span>
            </div>
            <div class="info-item" v-if="project.workArrangement">
              <label>工作安排</label>
              <span>{{ getWorkArrangementName(project.workArrangement) }}</span>
            </div>
            <div class="info-item" v-if="project.location">
              <label>具体地址</label>
              <span>{{ project.location }}</span>
            </div>
            <div class="info-item" v-if="project.expectedStartDate">
              <label>期望开始时间</label>
              <span>{{ formatDate(project.expectedStartDate) }}</span>
            </div>
          </div>
        </div>

        <!-- 项目进度 -->
        <div class="card" v-if="project.progress || project.currentMilestone">
          <h3>项目进度</h3>
          <div class="info-grid">
            <div class="info-item" v-if="project.progress">
              <label>完成进度</label>
              <span>{{ project.progress }}%</span>
            </div>
            <div class="info-item" v-if="project.currentMilestone">
              <label>当前阶段</label>
              <span>{{ getMilestoneName(project.currentMilestone) }}</span>
            </div>
          </div>
          <div v-if="project.milestoneDescription" class="milestone-desc">
            <strong>阶段描述：</strong>{{ project.milestoneDescription }}
          </div>
        </div>

        <!-- 项目链接 -->
        <div class="card" v-if="project.links && project.links.length > 0">
          <h3>项目链接</h3>
          <div class="project-links">
            <a v-for="(link, index) in project.links" :key="index" :href="link.url" target="_blank" class="project-link">
              {{ link.name || link.url }}
            </a>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="card">
          <h3>审核信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <label>审核状态</label>
              <span class="status-badge" :class="project.reviewStatus">
                {{ getStatusName(project.reviewStatus) }}
              </span>
            </div>
            <div class="info-item">
              <label>发布时间</label>
              <span>{{ formatDate(project.createdAt) }}</span>
            </div>
            <div class="info-item" v-if="project.reviewTime">
              <label>审核时间</label>
              <span>{{ formatDate(project.reviewTime) }}</span>
            </div>
            <div class="info-item" v-if="project.rejectReason">
              <label>拒绝原因</label>
              <span class="reject-reason">{{ project.rejectReason }}</span>
            </div>
          </div>
        </div>

        <div class="card">
          <h3>发起人信息</h3>
          <div class="owner-info">
            <div><strong>姓名：</strong>{{ project.owner.name }}</div>
            <div><strong>邮箱：</strong>{{ project.owner.email }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 拒绝模态框 -->
    <div v-if="showRejectModal" class="modal-overlay" @click="showRejectModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>拒绝项目</h3>
          <button @click="showRejectModal = false" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">拒绝原因 <span class="required">*</span></label>
            <textarea 
              v-model="rejectReason"
              class="form-input"
              rows="4"
              placeholder="请详细说明拒绝原因..."
              required
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="showRejectModal = false" class="btn btn-secondary mr-2">取消</button>
          <button 
            @click="rejectProject"
            class="btn btn-danger"
            :disabled="!rejectReason.trim()"
          >
            确认拒绝
          </button>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AdminLayout from '../components/AdminLayout.vue'
import api from '../utils/api.js'

const route = useRoute()
const router = useRouter()

const project = ref(null)
const showRejectModal = ref(false)
const rejectReason = ref('')

const loadProject = async () => {
  try {
    const response = await api.get(`/projects/${route.params.id}`)
    if (response.success) {
      project.value = response.data
    }
  } catch (error) {
    console.error('加载项目详情失败:', error)
  }
}

const approveProject = async () => {
  try {
    const response = await api.post(`/admin/review/${project.value.id}/approve`)
    if (response.success) {
      alert('项目审核通过！')
      router.push('/review')
    }
  } catch (error) {
    console.error('审核通过失败:', error)
    alert('操作失败，请重试')
  }
}

const rejectProject = async () => {
  if (!rejectReason.value.trim()) return
  
  try {
    const response = await api.post(`/admin/review/${project.value.id}/reject`, {
      reason: rejectReason.value
    })
    if (response.success) {
      alert('项目已拒绝，系统消息已发送给用户')
      router.push('/review')
    }
  } catch (error) {
    console.error('拒绝项目失败:', error)
    alert('操作失败，请重试')
  }
}

const getCategoryName = (category) => {
  const categoryMap = {
    'web': 'Web开发',
    'mobile': '移动应用',
    'ai': 'AI/机器学习',
    'blockchain': '区块链',
    'game': '游戏开发',
    'design': '设计',
    'other': '其他'
  }
  return categoryMap[category] || category
}

const getStatusName = (status) => {
  const statusMap = {
    'pending': '待审核',
    'approved': '已通过',
    'rejected': '已拒绝'
  }
  return statusMap[status] || status
}

const getUrgencyName = (urgency) => {
  const urgencyMap = {
    'low': '不紧急',
    'medium': '一般',
    'high': '紧急',
    'urgent': '非常紧急'
  }
  return urgencyMap[urgency] || urgency
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const getCooperationType = (cooperation) => {
  const cooperationMap = {
    'individual': '个人',
    'team': '团队',
    'company': '公司'
  }
  return cooperationMap[cooperation] || cooperation
}

const getWorkLocationName = (workLocation) => {
  const locationMap = {
    'remote': '远程',
    'onsite': '现场'
  }
  return locationMap[workLocation] || workLocation
}

const getWorkArrangementName = (workArrangement) => {
  const arrangementMap = {
    'fulltime': '全职',
    'parttime': '兼职',
    'contract': '合同',
    'everyday': '每一天',
    'weekends': '仅周末',
    'evenings_weekends': '晚上下班后+周末',
    'fullTime': '全职',
    'partTime': '兼职',
    'intern': '实习'
  }
  return arrangementMap[workArrangement] || workArrangement
}

const getMilestoneName = (currentMilestone) => {
  const milestoneMap = {
    'initial': '初始阶段',
    'development': '开发阶段',
    'testing': '测试阶段',
    'deployment': '部署阶段',
    'maintenance': '维护阶段'
  }
  return milestoneMap[currentMilestone] || currentMilestone
}

onMounted(() => {
  loadProject()
})
</script>

<style scoped>
.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.review-actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-content h2 {
  margin: 0 0 16px 0;
  color: #333;
}

.detail-content h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.project-meta {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.project-meta span {
  font-size: 14px;
  color: #666;
}

.project-description {
  margin: 0;
  color: #555;
  line-height: 1.6;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-weight: 500;
  color: #666;
  font-size: 14px;
}

.info-item span {
  color: #333;
}

.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.requirement-item {
  display: flex;
  gap: 16px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.requirement-card {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.role-name {
  font-weight: 500;
  color: #333;
}

.cooperation-type {
  color: #666;
  font-size: 12px;
}

.requirement-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skills {
  display: flex;
  gap: 8px;
}

.skill-tag {
  background-color: #e3f2fd;
  border-radius: 4px;
  padding: 2px 6px;
  color: #007bff;
  font-size: 12px;
}

.salary-info {
  color: #666;
  font-size: 12px;
}

.equity-info {
  color: #666;
  font-size: 12px;
}

.owner-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px 24px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #eee;
}

.required {
  color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .project-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .requirement-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .owner-info {
    gap: 4px;
  }
}

.project-images {
  display: flex;
  gap: 16px;
}

.main-image {
  width: 300px;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

.additional-images {
  display: flex;
  gap: 8px;
}

.project-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}

.milestone-desc {
  color: #666;
  font-size: 12px;
  margin-top: 8px;
}

.project-links {
  display: flex;
  gap: 8px;
}

.project-link {
  color: #007bff;
  text-decoration: none;
}

.status-badge.pending {
  background-color: #f1f3f4;
  color: #333;
}

.status-badge.approved {
  background-color: #dff0d8;
  color: #3c763d;
}

.status-badge.rejected {
  background-color: #f2dede;
  color: #a94442;
}

.reject-reason {
  color: #a94442;
  font-size: 12px;
}
</style> 