<template>
  <div class="investment-management">
    <div class="page-header">
      <h1>投资管理</h1>
      <div class="stats">
        <div class="stat-card">
          <div class="stat-number">{{ investorStats.total }}</div>
          <div class="stat-label">投资人申请</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ businessStats.total }}</div>
          <div class="stat-label">创业计划</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ pendingCount }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs">
      <button 
        @click="activeTab = 'investor'" 
        :class="{ active: activeTab === 'investor' }"
        class="tab-btn"
      >
        投资人申请 ({{ investorApplications.length }})
      </button>
      <button 
        @click="activeTab = 'business'" 
        :class="{ active: activeTab === 'business' }"
        class="tab-btn"
      >
        创业计划 ({{ businessApplications.length }})
      </button>
    </div>

    <!-- 投资人申请列表 -->
    <div v-if="activeTab === 'investor'" class="tab-content">
      <div class="filter-bar">
        <select v-model="investorFilter" @change="loadInvestorApplications">
          <option value="">全部状态</option>
          <option value="pending">待审核</option>
          <option value="approved">已通过</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>

      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="investorApplications.length === 0" class="empty">暂无投资人申请</div>
      <div v-else class="applications-list">
        <div v-for="app in investorApplications" :key="app.id" class="application-card">
          <div class="card-header">
            <div class="applicant-info">
              <h3>{{ app.name }}</h3>
              <p>{{ app.contact }}</p>
              <span class="time">{{ formatDate(app.createdAt) }}</span>
            </div>
            <span class="status-badge" :class="app.status">
              {{ getStatusText(app.status) }}
            </span>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <label>投资经验：</label>
              <span>{{ getExperienceText(app.experience) }}</span>
            </div>
            <div class="info-item" v-if="app.budget">
              <label>投资预算：</label>
              <span>{{ app.budget }}万</span>
            </div>
            <div class="info-item" v-if="app.industries">
              <label>感兴趣行业：</label>
              <span>{{ getIndustriesText(app.industries) }}</span>
            </div>
            <div class="info-item" v-if="app.description">
              <label>申请说明：</label>
              <p>{{ app.description }}</p>
            </div>
          </div>

          <div v-if="app.status === 'pending'" class="card-actions">
            <button @click="reviewApplication('investor', app.id, 'approved')" class="btn btn-success">
              通过
            </button>
            <button @click="reviewApplication('investor', app.id, 'rejected')" class="btn btn-danger">
              拒绝
            </button>
          </div>

          <div v-if="app.reviewNote" class="review-note">
            <strong>审核备注：</strong>{{ app.reviewNote }}
          </div>
        </div>
      </div>
    </div>

    <!-- 创业计划列表 -->
    <div v-if="activeTab === 'business'" class="tab-content">
      <div class="filter-bar">
        <select v-model="businessFilter" @change="loadBusinessApplications">
          <option value="">全部状态</option>
          <option value="pending">待审核</option>
          <option value="approved">已通过</option>
          <option value="rejected">已拒绝</option>
        </select>
      </div>

      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="businessApplications.length === 0" class="empty">暂无创业计划</div>
      <div v-else class="applications-list">
        <div v-for="app in businessApplications" :key="app.id" class="application-card">
          <div class="card-header">
            <div class="applicant-info">
              <h3>{{ app.projectName }}</h3>
              <p>{{ app.contact }}</p>
              <span class="time">{{ formatDate(app.createdAt) }}</span>
            </div>
            <span class="status-badge" :class="app.status">
              {{ getStatusText(app.status) }}
            </span>
          </div>
          
          <div class="card-content">
            <div class="info-item">
              <label>项目简介：</label>
              <p>{{ app.summary }}</p>
            </div>
            <div class="info-item">
              <label>所属行业：</label>
              <span>{{ getIndustryText(app.industry) }}</span>
            </div>
            <div class="info-item">
              <label>项目阶段：</label>
              <span>{{ getStageText(app.stage) }}</span>
            </div>
            <div class="info-item" v-if="app.funding">
              <label>融资需求：</label>
              <span>{{ app.funding }}万</span>
            </div>
            <div class="info-item" v-if="app.details">
              <label>详细计划：</label>
              <p>{{ app.details }}</p>
            </div>
          </div>

          <div v-if="app.status === 'pending'" class="card-actions">
            <button @click="reviewApplication('business', app.id, 'approved')" class="btn btn-success">
              通过
            </button>
            <button @click="reviewApplication('business', app.id, 'rejected')" class="btn btn-danger">
              拒绝
            </button>
          </div>

          <div v-if="app.reviewNote" class="review-note">
            <strong>审核备注：</strong>{{ app.reviewNote }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import api from '../utils/api.js'

export default {
  name: 'InvestmentManagement',
  setup() {
    const activeTab = ref('investor')
    const loading = ref(false)
    const investorApplications = ref([])
    const businessApplications = ref([])
    const investorFilter = ref('')
    const businessFilter = ref('')

    // 统计数据
    const investorStats = reactive({ total: 0, pending: 0, approved: 0, rejected: 0 })
    const businessStats = reactive({ total: 0, pending: 0, approved: 0, rejected: 0 })

    const pendingCount = computed(() => investorStats.pending + businessStats.pending)

    // 加载投资人申请
    const loadInvestorApplications = async () => {
      loading.value = true
      try {
        const params = new URLSearchParams()
        if (investorFilter.value) {
          params.append('status', investorFilter.value)
        }
        
        const response = await api.get(`/investment/investor-applications?${params}`)
        
        if (response.success) {
          investorApplications.value = response.data.applications
          // 更新统计
          investorStats.total = response.data.applications.length
          investorStats.pending = response.data.applications.filter(app => app.status === 'pending').length
          investorStats.approved = response.data.applications.filter(app => app.status === 'approved').length
          investorStats.rejected = response.data.applications.filter(app => app.status === 'rejected').length
        }
      } catch (error) {
        console.error('加载投资人申请失败:', error)
        alert('加载投资人申请失败')
      } finally {
        loading.value = false
      }
    }

    // 加载创业计划申请
    const loadBusinessApplications = async () => {
      loading.value = true
      try {
        const params = new URLSearchParams()
        if (businessFilter.value) {
          params.append('status', businessFilter.value)
        }
        
        const response = await api.get(`/investment/business-plans?${params}`)
        
        if (response.success) {
          businessApplications.value = response.data.applications
          // 更新统计
          businessStats.total = response.data.applications.length
          businessStats.pending = response.data.applications.filter(app => app.status === 'pending').length
          businessStats.approved = response.data.applications.filter(app => app.status === 'approved').length
          businessStats.rejected = response.data.applications.filter(app => app.status === 'rejected').length
        }
      } catch (error) {
        console.error('加载创业计划失败:', error)
        alert('加载创业计划失败')
      } finally {
        loading.value = false
      }
    }

    // 审核申请
    const reviewApplication = async (type, id, status) => {
      const action = status === 'approved' ? '通过' : '拒绝'
      const reviewNote = prompt(`请输入${action}理由（可选）：`)
      
      if (reviewNote === null) return // 用户取消
      
      try {
        const endpoint = type === 'investor' 
          ? `/investment/investor-applications/${id}/review`
          : `/investment/business-plans/${id}/review`
        
        const response = await api.put(endpoint, { status, reviewNote })
        
        if (response.success) {
          alert(`${action}成功`)
          if (type === 'investor') {
            await loadInvestorApplications()
          } else {
            await loadBusinessApplications()
          }
        } else {
          alert(response.message || `${action}失败`)
        }
      } catch (error) {
        console.error(`${action}失败:`, error)
        alert(`${action}失败`)
      }
    }

    // 工具函数
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || status
    }

    const getExperienceText = (experience) => {
      const expMap = {
        'beginner': '新手投资者（0-2年）',
        'intermediate': '有经验投资者（2-5年）',
        'expert': '资深投资者（5年以上）',
        'professional': '专业投资机构'
      }
      return expMap[experience] || experience
    }

    const getIndustriesText = (industries) => {
      try {
        const parsed = JSON.parse(industries)
        const industryMap = {
          'mobile': '移动应用',
          'web': 'Web开发',
          'ai': 'AI/机器学习',
          'blockchain': '区块链',
          'fintech': '金融科技',
          'education': '教育培训',
          'health': '健康生活',
          'ecommerce': '电商平台',
          'other': '其他'
        }
        return parsed.map(industry => industryMap[industry] || industry).join('、')
      } catch {
        return industries
      }
    }

    const getIndustryText = (industry) => {
      const industryMap = {
        'mobile': '移动应用',
        'web': 'Web开发',
        'ai': 'AI/机器学习',
        'blockchain': '区块链',
        'fintech': '金融科技',
        'education': '教育培训',
        'health': '健康生活',
        'ecommerce': '电商平台',
        'other': '其他'
      }
      return industryMap[industry] || industry
    }

    const getStageText = (stage) => {
      const stageMap = {
        'idea': '创意阶段',
        'prototype': '原型开发',
        'mvp': 'MVP阶段',
        'growth': '成长阶段',
        'mature': '成熟阶段'
      }
      return stageMap[stage] || stage
    }

    onMounted(async () => {
      await loadInvestorApplications()
      await loadBusinessApplications()
    })

    return {
      activeTab,
      loading,
      investorApplications,
      businessApplications,
      investorFilter,
      businessFilter,
      investorStats,
      businessStats,
      pendingCount,
      loadInvestorApplications,
      loadBusinessApplications,
      reviewApplication,
      formatDate,
      getStatusText,
      getExperienceText,
      getIndustriesText,
      getIndustryText,
      getStageText
    }
  }
}
</script>

<style scoped>
.investment-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  text-align: center;
  min-width: 100px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #4f46e5;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

.tab-btn {
  padding: 12px 24px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn.active {
  color: #4f46e5;
  border-bottom-color: #4f46e5;
}

.tab-btn:hover {
  color: #4f46e5;
}

.filter-bar {
  margin-bottom: 20px;
}

.filter-bar select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.applications-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.application-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.applicant-info h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  color: #333;
}

.applicant-info p {
  margin: 0 0 4px 0;
  color: #666;
}

.time {
  font-size: 12px;
  color: #999;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.pending {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.approved {
  background: #d1fae5;
  color: #059669;
}

.status-badge.rejected {
  background: #fee2e2;
  color: #dc2626;
}

.card-content {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.info-item label {
  font-weight: 600;
  color: #374151;
  min-width: 100px;
  margin-right: 8px;
}

.info-item p {
  margin: 0;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.review-note {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 14px;
  color: #4b5563;
}
</style>
