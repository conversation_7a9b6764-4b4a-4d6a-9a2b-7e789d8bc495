<template>
  <AdminLayout>
    <div class="users-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <div class="search-filters">
          <input
            v-model="searchQuery"
            @input="loadUsers"
            type="text"
            placeholder="搜索用户名或邮箱..."
            class="search-input"
          />
          <select v-model="filterRole" @change="loadUsers" class="filter-select">
            <option value="">全部角色</option>
            <option value="user">普通用户</option>
            <option value="admin">管理员</option>
          </select>
          <select v-model="filterDaoStatus" @change="loadUsers" class="filter-select">
            <option value="">全部状态</option>
            <option value="dao">DAO成员</option>
            <option value="non-dao">非DAO成员</option>
          </select>
        </div>
        <button @click="loadUsers" class="btn btn-primary" :disabled="loading">
          {{ loading ? '加载中...' : '刷新' }}
        </button>
      </div>
    </div>

    <!-- 用户表格 -->
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th>用户名</th>
            <th>邮箱</th>
            <th>角色</th>
            <th>DAO状态</th>
            <th>地区</th>
            <th>发布项目</th>
            <th>注册时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in users" :key="user.id">
            <td>
              <div class="user-info">
                <img v-if="user.avatar" :src="user.avatar" :alt="user.name" class="user-avatar">
                <div v-else class="user-avatar-placeholder">{{ user.name[0] }}</div>
                <span class="user-name">{{ user.name }}</span>
              </div>
            </td>
            <td>{{ user.email }}</td>
            <td>{{ getRoleName(user.role) }}</td>
            <td>
              <span :class="['status-badge', user.isDaoMember ? 'status-approved' : 'status-pending']">
                {{ user.isDaoMember ? 'DAO成员' : '非成员' }}
              </span>
            </td>
            <td>{{ user.location || '未填写' }}</td>
            <td>{{ user.projectCount || 0 }}</td>
            <td>{{ formatDate(user.createdAt) }}</td>
            <td>
              <div class="action-buttons">
                <button @click="viewUser(user)" class="btn btn-sm btn-primary">详情</button>
                <button
                  v-if="!user.isDaoMember"
                  @click="addToDaoMember(user)"
                  class="btn btn-sm btn-success"
                >
                  加入DAO
                </button>
                <button
                  v-else
                  @click="removeFromDaoMember(user)"
                  class="btn btn-sm btn-danger"
                >
                  移除DAO
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 空状态 -->
    <div v-if="users.length === 0 && !loading" class="empty-state">
      <div class="empty-icon">👥</div>
      <h3>暂无用户</h3>
      <p>还没有任何用户</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 用户详情模态框 -->
    <div v-if="showUserDetail" class="modal-overlay" @click="closeUserDetail">
      <div class="modal-content large" @click.stop>
        <div class="modal-header">
          <h3>用户详情</h3>
          <button @click="closeUserDetail" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedUser" class="user-detail">
            <!-- 基本信息 -->
            <div class="detail-section">
              <h4>基本信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>姓名</label>
                  <span>{{ selectedUser.name }}</span>
                </div>
                <div class="detail-item">
                  <label>邮箱</label>
                  <span>{{ selectedUser.email }}</span>
                </div>
                <div class="detail-item">
                  <label>手机</label>
                  <span>{{ selectedUser.phone || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>微信</label>
                  <span>{{ selectedUser.wechat || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>地区</label>
                  <span>{{ selectedUser.location || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>角色</label>
                  <span>{{ getRoleName(selectedUser.role) }}</span>
                </div>
              </div>
            </div>

            <!-- 个人简介 -->
            <div class="detail-section" v-if="selectedUser.bio">
              <h4>个人简介</h4>
              <p>{{ selectedUser.bio }}</p>
            </div>

            <!-- 技能标签 -->
            <div class="detail-section" v-if="selectedUser.skills && selectedUser.skills.length > 0">
              <h4>技能标签</h4>
              <div class="skills-tags">
                <span v-for="skill in selectedUser.skills" :key="skill" class="skill-tag">
                  {{ skill }}
                </span>
              </div>
            </div>

            <!-- 工作信息 -->
            <div class="detail-section">
              <h4>工作信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>公司</label>
                  <span>{{ selectedUser.company || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>职位</label>
                  <span>{{ selectedUser.position || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>工作经验</label>
                  <span>{{ selectedUser.experience || '未填写' }}</span>
                </div>
                <div class="detail-item">
                  <label>学历</label>
                  <span>{{ selectedUser.education || '未填写' }}</span>
                </div>
                <div class="detail-item" v-if="selectedUser.hourlyRate && selectedUser.hourlyRate > 0">
                  <label>时薪</label>
                  <span>{{ selectedUser.hourlyRate }}元/小时</span>
                </div>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="detail-section" v-if="selectedUser.github || selectedUser.website">
              <h4>联系方式</h4>
              <div class="contact-links">
                <a v-if="selectedUser.github" :href="selectedUser.github" target="_blank" class="contact-link">
                  🔗 GitHub
                </a>
                <a v-if="selectedUser.website" :href="selectedUser.website" target="_blank" class="contact-link">
                  🌐 个人网站
                </a>
              </div>
            </div>

            <!-- 项目统计 -->
            <div class="detail-section">
              <h4>项目统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-number">{{ selectedUser.projectCount }}</div>
                  <div class="stat-label">发布项目</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ selectedUser.applicationCount }}</div>
                  <div class="stat-label">申请项目</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ selectedUser.completedProjects || 0 }}</div>
                  <div class="stat-label">完成项目</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ selectedUser.rating || '-' }}</div>
                  <div class="stat-label">评分</div>
                </div>
              </div>
            </div>

            <!-- 账户信息 -->
            <div class="detail-section">
              <h4>账户信息</h4>
              <div class="detail-grid">
                <div class="detail-item">
                  <label>注册时间</label>
                  <span>{{ formatDate(selectedUser.createdAt) }}</span>
                </div>
                <div class="detail-item">
                  <label>最后登录</label>
                  <span>{{ formatDate(selectedUser.lastLoginAt) }}</span>
                </div>
                <div class="detail-item">
                  <label>账户状态</label>
                  <span :class="['status-badge', selectedUser.status === 'active' ? 'status-approved' : 'status-rejected']">
                    {{ selectedUser.status === 'active' ? '正常' : '禁用' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeUserDetail" class="btn btn-secondary">关闭</button>
        </div>
      </div>
    </div>
  </AdminLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AdminLayout from '../components/AdminLayout.vue'
import api from '../utils/api.js'

const users = ref([])
const loading = ref(false)
const showUserDetail = ref(false)
const selectedUser = ref(null)
const searchQuery = ref('')
const filterRole = ref('')
const filterDaoStatus = ref('')

const loadUsers = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams()
    if (searchQuery.value) {
      params.append('search', searchQuery.value)
    }
    if (filterRole.value) {
      params.append('role', filterRole.value)
    }

    const response = await api.get(`/admin/users?${params}`)
    if (response.success) {
      let filteredUsers = response.data.users

      // 前端筛选DAO状态
      if (filterDaoStatus.value === 'dao') {
        filteredUsers = filteredUsers.filter(user => user.isDaoMember)
      } else if (filterDaoStatus.value === 'non-dao') {
        filteredUsers = filteredUsers.filter(user => !user.isDaoMember)
      }

      users.value = filteredUsers
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const viewUser = (user) => {
  selectedUser.value = user
  showUserDetail.value = true
}

const closeUserDetail = () => {
  showUserDetail.value = false
  selectedUser.value = null
}

// DAO成员管理
const addToDaoMember = async (user) => {
  if (!confirm(`确认将 ${user.username || user.email} 加入DAO成员？`)) return

  try {
    const response = await api.post(`/admin/dao-members/${user.id}`)
    if (response.success) {
      alert('用户已加入DAO成员')
      await loadUsers()
    } else {
      alert(response.message || '操作失败')
    }
  } catch (error) {
    console.error('加入DAO成员失败:', error)
    alert('操作失败')
  }
}

const removeFromDaoMember = async (user) => {
  if (!confirm(`确认将 ${user.username || user.email} 从DAO成员中移除？`)) return

  try {
    const response = await api.delete(`/admin/dao-members/${user.id}`)
    if (response.success) {
      alert('用户已从DAO成员中移除')
      await loadUsers()
    } else {
      alert(response.message || '操作失败')
    }
  } catch (error) {
    console.error('移除DAO成员失败:', error)
    alert('操作失败')
  }
}

const toggleUserStatus = async (userId, currentStatus) => {
  const action = currentStatus === 'active' ? '禁用' : '启用'
  if (!confirm(`确认要${action}这个用户吗？`)) return
  
  try {
    console.log('切换用户状态:', { userId, currentStatus, action })
    const response = await api.post(`/admin/users/${userId}/toggle-status`)
    console.log('切换用户状态响应:', response)
    
    if (response.success) {
      // 更新本地用户状态
      const userIndex = users.value.findIndex(u => u.id === userId)
      if (userIndex !== -1) {
        users.value[userIndex].status = currentStatus === 'active' ? 'inactive' : 'active'
      }
      alert(`用户已${action}`)
    } else {
      alert(response.message || `${action}失败`)
    }
  } catch (error) {
    console.error('更新用户状态失败:', error)
    alert(`${action}失败: ${error.response?.data?.message || error.message}`)
  }
}

const getRoleName = (role) => {
  const roleMap = {
    'developer': '开发者',
    'client': '客户',
    'admin': '管理员'
  }
  return roleMap[role] || role
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.users-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar-placeholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
}

.user-name {
  font-weight: 500;
}

.rating {
  color: #ff9500;
  font-size: 14px;
}

.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.empty-state, .loading-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.empty-state p {
  margin: 0;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 用户详情样式 */
.detail-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-section h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.detail-item span {
  color: #333;
}

.skills-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.skill-tag {
  background: #f1f3f4;
  color: #333;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.contact-links {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.contact-link {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #007bff;
  text-decoration: none;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.contact-link:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .users-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
