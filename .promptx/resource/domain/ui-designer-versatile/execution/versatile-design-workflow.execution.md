<execution>
  <constraint>
    ## 多样化设计的客观限制
    - **技术实现约束**：不同风格对CSS/HTML技术能力要求差异巨大
    - **性能影响评估**：复杂视觉效果与加载速度的权衡关系
    - **跨平台兼容性**：历史风格在现代浏览器中的表现一致性
    - **可访问性标准**：所有风格必须符合WCAG 2.1 AA级别要求
    - **开发成本控制**：复杂风格实现的时间和资源投入限制
  </constraint>

  <rule>
    ## 多样化设计强制规则
    - **风格研究优先**：每个项目必须进行深入的风格历史和文化研究
    - **用户验证必须**：所有风格选择必须经过目标用户群体验证
    - **技术可行性确认**：设计稿必须经过技术实现可行性评估
    - **渐进增强原则**：从基础功能开始，逐步添加风格特色
    - **文化敏感性检查**：所有文化相关元素必须经过文化顾问审查
  </rule>

  <guideline>
    ## 多样化设计指导原则
    - **用户需求驱动**：风格选择以用户需求和使用场景为核心
    - **品牌一致性维护**：在多样化中保持品牌核心DNA的连续性
    - **创新与传统平衡**：既要尊重传统又要有所创新突破
    - **可持续性考虑**：设计方案要考虑长期维护和演化的可能性
    - **协作开放性**：鼓励与不同文化背景的设计师协作
  </guideline>

  <process>
    ## 🎨 多样化设计完整工作流程

    ### Phase 1: 项目分析与风格定位 (20分钟)

    ```mermaid
    flowchart TD
        A[项目启动] --> B[需求分析]
        B --> C[用户画像]
        B --> D[品牌定位]
        B --> E[行业特性]
        
        C --> F[文化背景分析]
        D --> G[品牌美学基因]
        E --> H[行业美学规范]
        
        F --> I[风格候选池]
        G --> I
        H --> I
        
        I --> J[风格优先级排序]
        J --> K[初步风格方向]
    ```

    #### 关键输出
    - **用户画像报告**：年龄、文化、教育、收入、美学偏好分析
    - **品牌美学DNA**：品牌核心视觉基因和可扩展边界
    - **风格候选清单**：3-5个备选风格方向及选择理由
    - **技术约束清单**：当前技术栈对各风格的支持程度

    ### Phase 2: 深度风格研究 (30分钟)

    ```mermaid
    graph LR
        A[选定风格] --> B[历史研究]
        A --> C[文化分析]
        A --> D[当代表现]
        
        B --> E[经典案例收集]
        C --> F[文化元素提取]
        D --> G[现代适配方法]
        
        E --> H[设计语言解构]
        F --> H
        G --> H
        
        H --> I[适配策略制定]
    ```

    #### 研究维度矩阵
    | 风格类别 | 色彩特征 | 字体选择 | 布局原则 | 装饰元素 | 现代适配 |
    |----------|----------|----------|----------|----------|----------|
    | 古典主义 | 大地色系 | 衬线字体 | 对称均衡 | 柱式装饰 | 简化线条 |
    | 日式禅意 | 单色渐变 | 细体字型 | 留白哲学 | 自然纹理 | 微动效果 |
    | 赛博朋克 | 霓虹色彩 | 等宽字体 | 错位重叠 | 故障效果 | CSS滤镜 |
    | 装饰艺术 | 金属色调 | 几何字型 | 放射构图 | 几何图案 | SVG图形 |

    ### Phase 3: 创意概念设计 (40分钟)

    ```mermaid
    flowchart TD
        A[风格研究完成] --> B[情绪板制作]
        B --> C[色彩方案设计]
        B --> D[字体系统规划]
        B --> E[视觉层次设计]
        
        C --> F[核心界面草图]
        D --> F
        E --> F
        
        F --> G[关键页面设计]
        G --> H[交互细节定义]
        H --> I[设计系统建立]
    ```

    #### 创意输出清单
    - **情绪板 (Mood Board)**：风格氛围、色彩情感、质感表现
    - **色彩方案**：主色、辅色、功能色的完整色彩系统
    - **字体系统**：标题、正文、标注的层级字体规范
    - **组件库设计**：按钮、卡片、表单等核心组件的风格化
    - **布局网格**：适应风格特色的网格系统和间距规范

    ### Phase 4: 技术实现规划 (30分钟)

    ```mermaid
    graph TD
        A[设计稿完成] --> B[技术分解]
        B --> C[CSS策略制定]
        B --> D[资源准备]
        B --> E[兼容性方案]
        
        C --> F[关键技术验证]
        D --> F
        E --> F
        
        F --> G[实现优先级]
        G --> H[开发计划制定]
    ```

    #### 技术实现策略
    - **CSS变量系统**：建立主题化的CSS变量体系
    - **响应式适配**：不同屏幕尺寸下的风格保持策略
    - **性能优化方案**：图片优化、动画优化、加载策略
    - **降级兼容预案**：老旧浏览器的优雅降级方案

    ### Phase 5: 原型开发与测试 (45分钟)

    ```mermaid
    flowchart LR
        A[HTML结构搭建] --> B[CSS样式实现]
        B --> C[交互效果添加]
        C --> D[响应式调试]
        D --> E[跨浏览器测试]
        E --> F[可访问性检查]
        F --> G[性能优化]
        G --> H[用户测试]
    ```

    #### 测试验证清单
    - ✅ **功能完整性**：所有设计功能正确实现
    - ✅ **视觉还原度**：设计稿到代码的高度还原
    - ✅ **响应式表现**：各尺寸设备的良好适配
    - ✅ **性能指标**：加载速度、动画流畅度达标
    - ✅ **可访问性合规**：WCAG 2.1 AA级标准符合
    - ✅ **跨浏览器兼容**：主流浏览器表现一致

    ### Phase 6: 交付与文档化 (15分钟)

    ```mermaid
    graph LR
        A[测试通过] --> B[代码整理]
        B --> C[文档编写]
        C --> D[使用说明]
        D --> E[维护指南]
        E --> F[最终交付]
    ```

    #### 交付物清单
    - **完整HTML页面**：可直接运行的完整页面代码
    - **设计系统文档**：组件使用说明和样式指南
    - **风格指南**：设计理念、色彩含义、字体选择说明
    - **技术文档**：代码结构、关键技术点、维护注意事项
    - **演示说明**：设计亮点展示和使用场景介绍

    ## 🌈 风格快速切换系统

    ### CSS主题化架构
    ```css
    :root {
      /* 古典主义主题 */
      --classical-primary: #8B4513;
      --classical-secondary: #D2B48C;
      --classical-accent: #DAA520;
      
      /* 日式禅意主题 */
      --zen-primary: #2F4F4F;
      --zen-secondary: #F5F5DC;
      --zen-accent: #CD853F;
      
      /* 赛博朋克主题 */
      --cyber-primary: #FF0080;
      --cyber-secondary: #00FFFF;
      --cyber-accent: #FFFF00;
    }
    
    .theme-classical {
      --primary: var(--classical-primary);
      --secondary: var(--classical-secondary);
      --accent: var(--classical-accent);
    }
    ```

    ### 动态风格切换
    ```javascript
    // 风格切换函数
    function switchTheme(themeName) {
      document.body.className = `theme-${themeName}`;
      updateAnimations(themeName);
      adjustLayoutDensity(themeName);
    }
    ```
  </process>

  <criteria>
    ## 多样化设计质量评估

    ### 风格准确性评估
    - ✅ **历史准确性**：对传统风格的理解和表现准确度
    - ✅ **文化敏感性**：避免文化挪用，展现文化尊重
    - ✅ **现代适配度**：传统元素的当代化转换效果
    - ✅ **创新融合感**：不同风格元素的和谐组合

    ### 用户体验质量
    - ✅ **直观易懂**：风格选择不影响界面的易用性
    - ✅ **情感共鸣**：风格能够引发目标用户的情感认同
    - ✅ **品牌一致**：多样化风格中保持品牌识别度
    - ✅ **场景适配**：风格与使用场景的匹配度

    ### 技术实现质量
    - ✅ **代码优雅**：实现代码的可读性和可维护性
    - ✅ **性能优秀**：复杂风格不影响页面性能表现
    - ✅ **兼容稳定**：跨平台跨浏览器的稳定表现
    - ✅ **扩展友好**：风格系统的可扩展性和灵活性

    ### 商业价值实现
    - ✅ **目标达成**：设计风格对业务目标的支撑效果
    - ✅ **用户满意**：用户对风格选择的接受度和满意度
    - ✅ **差异化竞争**：独特风格带来的竞争优势
    - ✅ **长期价值**：设计方案的可持续性和发展潜力
  </criteria>
</execution> 