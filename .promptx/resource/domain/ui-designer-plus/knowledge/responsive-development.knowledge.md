# 响应式开发精通知识

## 📱 移动优先设计理念

### 移动优先的设计思维
```css
/* 基础移动样式 */
.container {
    width: 100%;
    padding: 1rem;
    margin: 0 auto;
}

/* 渐进增强到更大屏幕 */
@media (min-width: 768px) {
    .container {
        max-width: 768px;
        padding: 2rem;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}
```

### 触摸友好的设计
```css
/* 触摸目标最小尺寸 */
.touch-target {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
}

.button:active {
    transform: scale(0.95);
    -webkit-tap-highlight-color: transparent;
}
```

## 🖥️ 断点系统设计

### 现代断点策略
```css
:root {
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
}

.responsive-layout {
    display: flex;
    flex-direction: column;
}

@media (min-width: 768px) {
    .responsive-layout {
        flex-direction: row;
    }
}
```

### 容器查询
```css
.card-container {
    container-type: inline-size;
}

@container (min-width: 300px) {
    .card {
        display: grid;
        grid-template-columns: auto 1fr;
        gap: 1rem;
    }
}
```

## 🎨 弹性单位与流体设计

### 现代单位系统
```css
.hero {
    height: calc(100vh - 60px);
    font-size: clamp(2rem, 5vw, 4rem);
    padding: clamp(2rem, 5vw, 6rem);
}

.readable-text {
    max-width: 65ch;
    line-height: 1.6;
}

.fluid-design {
    width: min(100%, 1200px);
    font-size: clamp(1rem, 2.5vw, 2rem);
    margin: calc(50% - 300px);
}
```

## 🖼️ 响应式媒体

### 响应式图片
```css
.responsive-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    object-position: center;
}

.hero-image {
    width: 100%;
    height: 50vh;
}

@media (min-width: 768px) {
    .hero-image {
        height: 70vh;
    }
}
```

### 响应式视频
```css
.video-responsive {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 */
}

.video-responsive iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
```

## 📏 网格与布局系统

### CSS Grid响应式布局
```css
.auto-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(1rem, 3vw, 2rem);
}

.advanced-grid {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .advanced-grid {
        grid-template-columns: 1fr 300px;
    }
}
```

### Flexbox响应式布局
```css
.nav {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

@media (min-width: 768px) {
    .nav {
        flex-direction: row;
        justify-content: space-between;
    }
}

.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.card {
    flex: 1 1 calc(50% - 0.5rem);
    min-width: 250px;
}
```

## 🎯 性能优化

### 响应式性能最佳实践
```css
.performance-optimized {
    transform: translateX(0);
    transition: transform 0.3s ease;
}

.performance-optimized:hover {
    transform: translateX(10px);
}

@font-face {
    font-family: 'CustomFont';
    src: url('font.woff2') format('woff2');
    font-display: swap;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        transition-duration: 0.01ms !important;
    }
}
```
