# HTML/CSS前端实现精通

## 🏗️ HTML语义化精通

### 现代HTML5语义标签
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="页面描述">
    <title>页面标题</title>
</head>
<body>
    <header class="site-header">
        <nav class="main-navigation" aria-label="主导航">
            <ul role="list">
                <li><a href="#home" aria-current="page">首页</a></li>
                <li><a href="#about">关于</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="main-content">
        <section class="hero" aria-labelledby="hero-title">
            <h1 id="hero-title">页面主标题</h1>
            <p class="hero-description">页面描述内容</p>
        </section>
    </main>
    
    <footer class="site-footer">
        <p>&copy; 2024 公司名称</p>
    </footer>
</body>
</html>
```

## 🎨 CSS现代技术精通

### CSS Grid布局精通
```css
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    grid-gap: 2rem;
    align-items: start;
}

.advanced-grid {
    display: grid;
    grid-template-areas: 
        "header header header"
        "sidebar main aside"
        "footer footer footer";
    grid-template-columns: 200px 1fr 200px;
    grid-template-rows: auto 1fr auto;
    min-height: 100vh;
}
```

### Flexbox布局精通
```css
.flex-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.card-layout {
    display: flex;
    flex-direction: column;
    min-height: 300px;
}

.card-content {
    flex: 1 1 auto;
    overflow-y: auto;
}
```

### CSS自定义属性
```css
:root {
    --color-primary: #3b82f6;
    --font-size-base: 1rem;
    --space-4: 1rem;
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition-base: 300ms ease;
}

.component {
    background: var(--color-primary);
    padding: var(--space-4);
    box-shadow: var(--shadow-md);
    transition: var(--transition-base);
}
```

## 🚀 CSS动画与交互

### 过渡动画
```css
.button {
    background: var(--color-primary);
    transition: all 0.3s ease;
}

.button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}
```

### 关键帧动画
```css
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-in {
    animation: fadeInUp 0.6s ease-out;
}
```

## 📱 响应式设计实现

### 媒体查询最佳实践
```css
/* 移动优先 */
.container {
    padding: 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 2rem;
        max-width: 768px;
        margin: 0 auto;
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px;
    }
}
```

### 弹性设计
```css
.responsive-text {
    font-size: clamp(1rem, 2.5vw, 2rem);
}

.fluid-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: clamp(1rem, 3vw, 2rem);
}
```

## 🎯 性能优化

### CSS优化策略
```css
/* 高效选择器 */
.component { }
.component__element { }
.component--modifier { }

/* GPU加速 */
.optimized-animation {
    will-change: transform;
    transform: translateZ(0);
}

/* 减少重排重绘 */
.efficient-change {
    transform: translateX(10px);
    opacity: 0.8;
}
```

### 加载优化
```css
@font-face {
    font-family: 'CustomFont';
    src: url('font.woff2') format('woff2');
    font-display: swap;
}

.lazy-image {
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 25%);
    background-size: 20px 20px;
}
``` 