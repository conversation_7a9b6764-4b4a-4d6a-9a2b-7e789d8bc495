<execution>
  <constraint>
    ## 技术实现约束
    - **浏览器兼容性**：必须支持主流现代浏览器（Chrome 90+、Firefox 88+、Safari 14+、Edge 90+）
    - **响应式要求**：必须适配移动设备、平板和桌面（320px-1920px）
    - **性能标准**：First Contentful Paint < 2s，Cumulative Layout Shift < 0.1
    - **可访问性基准**：符合WCAG 2.1 AA级标准
    - **代码质量**：通过HTML/CSS验证器检查，无语法错误
    - **文件大小限制**：单个CSS文件不超过50KB，HTML结构清晰简洁
  </constraint>

  <rule>
    ## 强制执行规则
    - **语义化优先**：HTML必须使用语义化标签，不得滥用div和span
    - **样式分离**：严禁内联样式，所有样式必须在CSS文件中定义
    - **移动优先**：CSS必须采用移动优先的响应式设计策略
    - **组件化开发**：相同的UI元素必须复用，避免重复代码
    - **命名规范**：CSS类名采用BEM命名法或一致的命名约定
    - **性能优化**：必须优化关键渲染路径，使用高效的CSS选择器
    - **可维护性**：代码必须清晰注释，易于理解和维护
  </rule>

  <guideline>
    ## 实施指导原则
    - **渐进增强**：基础功能优先，高级效果作为增强
    - **优雅降级**：为不支持新特性的浏览器提供备选方案
    - **用户体验优先**：任何视觉效果都不能影响核心功能的使用
    - **可扩展设计**：考虑未来功能扩展的可能性
    - **一致性维护**：保持设计系统的一致性和完整性
    - **测试驱动**：在多设备多浏览器环境下充分测试
  </guideline>

  <process>
    ## 🎨 设计到代码完整工作流

    ### Phase 1: 需求分析与设计规划 (15分钟)
    
    ```mermaid
    flowchart TD
        A[接收设计需求] --> B{分析设计复杂度}
        B -->|简单| C[标准组件设计]
        B -->|中等| D[创新风格设计] 
        B -->|复杂| E[系统级设计]
        
        C --> F[确定技术栈]
        D --> F
        E --> F
        
        F --> G[制定实现计划]
    ```

    **具体操作**：
    1. **理解项目需求**：明确功能目标、用户群体、技术约束
    2. **分析设计风格**：确定视觉风格方向（新拟态、玻璃态、3D等）
    3. **评估技术难度**：识别需要特殊CSS技术的设计元素
    4. **制定实现策略**：选择最适合的布局方法和技术方案

    ### Phase 2: 设计创作阶段 (30分钟)

    ```mermaid
    mindmap
      root((UI设计))
        布局设计
          信息架构
          网格系统
          响应式断点
        视觉设计
          色彩方案
          字体系统
          图标风格
        交互设计
          用户流程
          状态反馈
          动画效果
        技术考虑
          实现可行性
          性能影响
          兼容性评估
    ```

    **设计输出**：
    1. **线框图设计**：确定页面结构和信息层次
    2. **视觉设计稿**：完整的视觉效果呈现
    3. **交互说明**：用户操作流程和状态变化
    4. **技术备注**：特殊效果的实现方案说明

    ### Phase 3: HTML结构搭建 (20分钟)

    ```mermaid
    graph LR
        A[分析设计稿] --> B[规划HTML结构]
        B --> C[选择语义标签]
        C --> D[构建基础框架]
        D --> E[添加内容区块]
        E --> F[完善辅助信息]
    ```

    **HTML编写标准**：
    ```html
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>[页面标题]</title>
        <link rel="stylesheet" href="styles.css">
    </head>
    <body>
        <header class="header">
            <!-- 页头内容 -->
        </header>
        
        <main class="main">
            <section class="hero">
                <!-- 主要内容区 -->
            </section>
        </main>
        
        <footer class="footer">
            <!-- 页脚内容 -->
        </footer>
    </body>
    </html>
    ```

    ### Phase 4: CSS样式实现 (45分钟)

    ```mermaid
    flowchart TD
        A[CSS基础设置] --> B[布局实现]
        B --> C[组件样式]
        C --> D[响应式调整]
        D --> E[动画效果]
        E --> F[优化测试]
    ```

    **CSS实现顺序**：
    
    #### 4.1 基础样式设置
    ```css
    /* CSS重置和基础设置 */
    :root {
        --primary-color: #your-color;
        --font-main: 'Your-Font', sans-serif;
        --spacing-unit: 1rem;
    }
    
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    ```

    #### 4.2 布局系统实现
    ```css
    /* Grid/Flexbox布局 */
    .container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--spacing-unit);
    }
    ```

    #### 4.3 组件样式开发
    - 按照原子设计理论，从小到大实现组件
    - 确保每个组件的独立性和可复用性
    - 考虑组件的不同状态（hover、active、disabled等）

    #### 4.4 响应式适配
    ```css
    /* 移动优先响应式 */
    @media (min-width: 768px) {
        /* 平板样式 */
    }
    
    @media (min-width: 1024px) {
        /* 桌面样式 */
    }
    ```

    ### Phase 5: 测试与优化 (15分钟)

    ```mermaid
    graph TD
        A[功能测试] --> B[兼容性测试]
        B --> C[性能测试]
        C --> D[可访问性测试]
        D --> E{是否通过?}
        E -->|是| F[交付完成]
        E -->|否| G[问题修复]
        G --> A
    ```

    **测试检查清单**：
    - ✅ 各浏览器显示一致性
    - ✅ 不同设备尺寸适配效果
    - ✅ 页面加载性能指标
    - ✅ 键盘导航和屏幕阅读器支持
    - ✅ 代码语法验证通过

    ### Phase 6: 交付与文档 (10分钟)

    **交付内容**：
    1. **完整HTML文件**：可直接在浏览器中运行
    2. **CSS样式文件**：组织良好、注释清晰
    3. **预览截图**：展示在不同设备上的效果
    4. **技术说明**：使用的关键技术和特性说明
    5. **浏览器兼容性报告**：支持的浏览器版本
  </process>

  <criteria>
    ## 质量评价标准

    ### 设计还原度 (25%)
    - ✅ 视觉效果与设计稿高度一致
    - ✅ 色彩、字体、间距精确还原
    - ✅ 交互状态完整实现
    - ✅ 动画效果流畅自然

    ### 代码质量 (25%)
    - ✅ HTML语义化程度高
    - ✅ CSS代码结构清晰
    - ✅ 命名规范一致
    - ✅ 代码注释完善

    ### 性能表现 (20%)
    - ✅ 首屏加载时间 < 2秒
    - ✅ CSS文件大小合理
    - ✅ 动画性能流畅
    - ✅ 内存占用低

    ### 响应式效果 (15%)
    - ✅ 移动设备适配完美
    - ✅ 平板设备显示良好
    - ✅ 桌面设备体验优秀
    - ✅ 跨设备一致性高

    ### 可访问性 (10%)
    - ✅ 键盘导航支持
    - ✅ 屏幕阅读器兼容
    - ✅ 色彩对比度符合标准
    - ✅ 焦点管理合理

    ### 浏览器兼容性 (5%)
    - ✅ 主流浏览器支持
    - ✅ 优雅降级处理
    - ✅ 特性检测完善
    - ✅ Polyfill使用合理
  </criteria>
</execution> 