<execution>
  <constraint>
    ## UI设计质量硬性约束
    - **视觉一致性**：同类元素必须保持统一的视觉风格
    - **信息层次清晰**：重要信息必须具有明显的视觉权重
    - **交互反馈及时**：用户操作必须有即时的视觉反馈
    - **可访问性达标**：色彩对比度必须符合WCAG标准
    - **加载性能**：关键视觉元素必须在2秒内显示
  </constraint>

  <rule>
    ## UI质量强制规则
    - **设计系统遵循**：必须遵循既定的设计系统和组件库
    - **品牌一致性**：所有设计元素必须符合品牌视觉规范
    - **用户体验优先**：任何视觉设计不得影响核心功能使用
    - **响应式设计**：必须在所有主流设备上提供良好体验
    - **代码实现性**：设计方案必须考虑技术实现的可行性
  </rule>

  <guideline>
    ## UI质量指导原则
    - **简洁胜过复杂**：优先选择简洁明了的设计方案
    - **功能决定形式**：视觉设计服务于功能需求
    - **用户认知负荷最小化**：减少用户的学习和理解成本
    - **情感化设计**：通过视觉元素传达品牌情感和价值
    - **持续改进**：基于用户反馈不断优化设计方案
  </guideline>

  <process>
    ## 🎯 UI设计质量保证流程

    ### Stage 1: 设计评估阶段

    ```mermaid
    flowchart TD
        A[设计方案] --> B{可用性评估}
        B -->|通过| C{技术可行性}
        B -->|不通过| D[用户体验优化]
        C -->|可行| E{性能影响评估}
        C -->|不可行| F[技术方案调整]
        E -->|良好| G[进入开发]
        E -->|有问题| H[性能优化设计]
        
        D --> A
        F --> A
        H --> A
    ```

    **评估检查项**：
    - ✅ 用户目标是否清晰表达
    - ✅ 操作流程是否直观易懂
    - ✅ 视觉层次是否合理
    - ✅ 品牌元素是否恰当使用

    ### Stage 2: 实现质量控制

    ```mermaid
    graph LR
        A[HTML结构] --> B[语义化检查]
        B --> C[CSS实现]
        C --> D[样式质量审查]
        D --> E[响应式测试]
        E --> F[兼容性验证]
        F --> G[性能基准测试]
    ```

    **技术质量标准**：
    
    #### HTML质量要求
    ```html
    <!-- 良好的HTML结构示例 -->
    <article class="card">
        <header class="card__header">
            <h2 class="card__title">标题</h2>
        </header>
        <div class="card__content">
            <p class="card__description">描述内容</p>
        </div>
        <footer class="card__footer">
            <button class="btn btn--primary">操作按钮</button>
        </footer>
    </article>
    ```

    #### CSS质量标准
    ```css
    /* 良好的CSS组织示例 */
    .card {
        /* 布局相关 */
        display: flex;
        flex-direction: column;
        
        /* 视觉样式 */
        background: var(--card-bg);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-subtle);
        
        /* 状态管理 */
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-elevated);
    }
    ```

    ### Stage 3: 用户体验验证

    ```mermaid
    mindmap
      root((UX验证))
        易用性测试
          任务完成率
          操作错误率
          学习曲线
        视觉设计评估
          美观度评分
          品牌识别度
          情感反应
        技术体验测试
          加载速度
          交互响应
          设备兼容性
        可访问性检查
          键盘导航
          屏幕阅读器
          色彩对比度
    ```

    ### Stage 4: 持续优化流程

    **数据驱动优化**：
    1. **收集用户反馈**：通过热图、问卷、访谈等方式
    2. **分析使用数据**：页面停留时间、转化率、跳出率
    3. **识别问题区域**：高频问题点和用户困惑区域
    4. **制定优化方案**：基于数据制定具体改进措施
    5. **A/B测试验证**：对比测试优化效果
    6. **迭代改进**：持续优化用户体验

    ## 🔍 设计审查清单

    ### 视觉设计审查
    ```mermaid
    graph TD
        A[视觉审查] --> B[色彩使用]
        A --> C[字体层级]
        A --> D[空间布局]
        A --> E[图标系统]
        
        B --> B1[品牌色彩一致性]
        B --> B2[对比度符合标准]
        B --> B3[色彩心理学应用]
        
        C --> C1[字体大小合理]
        C --> C2[层级关系清晰]
        C --> C3[可读性良好]
        
        D --> D1[信息层次分明]
        D --> D2[留白运用得当]
        D --> D3[对齐方式统一]
        
        E --> E1[风格保持一致]
        E --> E2[含义表达准确]
        E --> E3[尺寸规格统一]
    ```

    ### 交互设计审查
    - **操作反馈**：hover、active、focus状态设计完整
    - **加载状态**：loading、skeleton screen等加载体验
    - **错误处理**：表单验证、网络错误等异常状态设计
    - **引导提示**：新功能引导、空状态提示等
    - **动画效果**：过渡动画自然流畅，不影响性能

    ### 响应式设计审查
    ```css
    /* 响应式设计检查点 */
    
    /* 移动设备 (320px - 767px) */
    @media (max-width: 767px) {
        .container {
            padding: 1rem;
            /* 确保内容可读性 */
        }
        
        .btn {
            min-height: 44px;
            /* 确保触摸目标足够大 */
        }
    }
    
    /* 平板设备 (768px - 1023px) */
    @media (min-width: 768px) and (max-width: 1023px) {
        .grid {
            grid-template-columns: repeat(2, 1fr);
            /* 合理利用屏幕空间 */
        }
    }
    
    /* 桌面设备 (1024px+) */
    @media (min-width: 1024px) {
        .layout {
            max-width: 1200px;
            margin: 0 auto;
            /* 防止内容过宽影响阅读 */
        }
    }
    ```

    ## 📊 质量度量指标

    ### 视觉质量指标
    - **色彩对比度**：文字与背景对比度 ≥ 4.5:1
    - **字体可读性**：正文字号 ≥ 16px，行高 ≥ 1.5
    - **触摸目标**：按钮最小尺寸 44x44px
    - **加载时间**：首屏内容 < 2s，交互元素 < 1s

    ### 用户体验指标
    - **任务完成率**：核心任务完成率 ≥ 95%
    - **错误率**：用户操作错误率 ≤ 5%
    - **满意度**：用户满意度评分 ≥ 4.0/5.0
    - **学习时间**：新用户上手时间 ≤ 5分钟

    ### 技术性能指标
    - **页面大小**：HTML+CSS总大小 ≤ 100KB
    - **渲染性能**：FCP ≤ 2s，LCP ≤ 2.5s
    - **兼容性**：主流浏览器支持率 ≥ 95%
    - **可访问性**：WCAG 2.1 AA级合规率 100%
  </process>

  <criteria>
    ## UI设计质量评分标准

    ### 设计创新性 (20%)
    - ✅ 视觉风格独特有特色 (5%)
    - ✅ 交互方式创新实用 (5%)
    - ✅ 布局设计新颖合理 (5%)
    - ✅ 整体体验突破常规 (5%)

    ### 用户体验 (30%)
    - ✅ 信息架构清晰合理 (8%)
    - ✅ 操作流程直观简单 (8%)
    - ✅ 视觉引导明确有效 (7%)
    - ✅ 反馈机制及时准确 (7%)

    ### 视觉设计 (25%)
    - ✅ 色彩搭配和谐统一 (6%)
    - ✅ 字体选择恰当可读 (6%)
    - ✅ 空间布局美观平衡 (6%)
    - ✅ 品牌元素运用得当 (7%)

    ### 技术实现 (15%)
    - ✅ 代码结构清晰规范 (4%)
    - ✅ 性能优化措施到位 (4%)
    - ✅ 兼容性处理完善 (4%)
    - ✅ 可维护性考虑周全 (3%)

    ### 响应式设计 (10%)
    - ✅ 移动端适配完美 (4%)
    - ✅ 平板端体验良好 (3%)
    - ✅ 桌面端效果优秀 (3%)

    **评分标准**：
    - **优秀 (90-100分)**：所有标准完全达成，有创新亮点
    - **良好 (80-89分)**：主要标准达成，细节有待完善
    - **合格 (70-79分)**：基本标准达成，存在明显改进空间
    - **不合格 (<70分)**：关键标准未达成，需要重新设计
  </criteria>
</execution> 