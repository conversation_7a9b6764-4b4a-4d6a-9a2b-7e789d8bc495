{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-08T09:51:33.185Z", "updatedAt": "2025-07-08T09:51:33.194Z", "resourceCount": 35}, "resources": [{"id": "financial-product-manager", "source": "project", "protocol": "role", "name": "Financial Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/financial-product-manager/financial-product-manager.role.md", "metadata": {"createdAt": "2025-07-08T09:51:33.187Z", "updatedAt": "2025-07-08T09:51:33.187Z", "scannedAt": "2025-07-08T09:51:33.187Z"}}, {"id": "financial-product-thinking", "source": "project", "protocol": "thought", "name": "Financial Product Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/financial-product-manager/thought/financial-product-thinking.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.188Z", "updatedAt": "2025-07-08T09:51:33.188Z", "scannedAt": "2025-07-08T09:51:33.188Z"}}, {"id": "tokenomics-design", "source": "project", "protocol": "thought", "name": "Tokenomics Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/financial-product-manager/thought/tokenomics-design.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.188Z", "updatedAt": "2025-07-08T09:51:33.188Z", "scannedAt": "2025-07-08T09:51:33.188Z"}}, {"id": "tokenomics-implementation", "source": "project", "protocol": "execution", "name": "Tokenomics Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/financial-product-manager/execution/tokenomics-implementation.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.188Z", "updatedAt": "2025-07-08T09:51:33.188Z", "scannedAt": "2025-07-08T09:51:33.188Z"}}, {"id": "financial-product-expertise", "source": "project", "protocol": "knowledge", "name": "Financial Product Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/financial-product-manager/knowledge/financial-product-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.189Z", "updatedAt": "2025-07-08T09:51:33.189Z", "scannedAt": "2025-07-08T09:51:33.188Z"}}, {"id": "logo-designer", "source": "project", "protocol": "role", "name": "Logo Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/logo-designer/logo-designer.role.md", "metadata": {"createdAt": "2025-07-08T09:51:33.189Z", "updatedAt": "2025-07-08T09:51:33.189Z", "scannedAt": "2025-07-08T09:51:33.189Z"}}, {"id": "visual-brand-thinking", "source": "project", "protocol": "thought", "name": "Visual Brand Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/logo-designer/thought/visual-brand-thinking.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.189Z", "updatedAt": "2025-07-08T09:51:33.189Z", "scannedAt": "2025-07-08T09:51:33.189Z"}}, {"id": "brand-design-standards", "source": "project", "protocol": "execution", "name": "Brand Design Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/logo-designer/execution/brand-design-standards.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.189Z", "updatedAt": "2025-07-08T09:51:33.189Z", "scannedAt": "2025-07-08T09:51:33.189Z"}}, {"id": "logo-design-workflow", "source": "project", "protocol": "execution", "name": "Logo Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/logo-designer/execution/logo-design-workflow.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.189Z", "updatedAt": "2025-07-08T09:51:33.189Z", "scannedAt": "2025-07-08T09:51:33.189Z"}}, {"id": "design-principles-theory", "source": "project", "protocol": "knowledge", "name": "Design Principles Theory 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/logo-designer/knowledge/design-principles-theory.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "logo-design-expertise", "source": "project", "protocol": "knowledge", "name": "Logo Design Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/logo-designer/knowledge/logo-design-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "visual-identity-systems", "source": "project", "protocol": "knowledge", "name": "Visual Identity Systems 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/logo-designer/knowledge/visual-identity-systems.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "naming-master", "source": "project", "protocol": "role", "name": "Naming Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/naming-master/naming-master.role.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "creative-naming-thinking", "source": "project", "protocol": "thought", "name": "Creative Naming Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/naming-master/thought/creative-naming-thinking.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "cultural-language-insight", "source": "project", "protocol": "thought", "name": "Cultural Language Insight 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/naming-master/thought/cultural-language-insight.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "market-brand-awareness", "source": "project", "protocol": "thought", "name": "Market Brand Awareness 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/naming-master/thought/market-brand-awareness.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.190Z", "updatedAt": "2025-07-08T09:51:33.190Z", "scannedAt": "2025-07-08T09:51:33.190Z"}}, {"id": "brand-validation-process", "source": "project", "protocol": "execution", "name": "Brand Validation Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/naming-master/execution/brand-validation-process.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "naming-methodology", "source": "project", "protocol": "execution", "name": "Naming Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/naming-master/execution/naming-methodology.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "brand-strategy", "source": "project", "protocol": "knowledge", "name": "Brand Strategy 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/naming-master/knowledge/brand-strategy.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "linguistic-psychology", "source": "project", "protocol": "knowledge", "name": "Linguistic Psychology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/naming-master/knowledge/linguistic-psychology.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "naming-expertise", "source": "project", "protocol": "knowledge", "name": "Naming Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/naming-master/knowledge/naming-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "ui-designer-plus", "source": "project", "protocol": "role", "name": "Ui Designer Plus 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ui-designer-plus/ui-designer-plus.role.md", "metadata": {"createdAt": "2025-07-08T09:51:33.191Z", "updatedAt": "2025-07-08T09:51:33.191Z", "scannedAt": "2025-07-08T09:51:33.191Z"}}, {"id": "creative-ui-thinking", "source": "project", "protocol": "thought", "name": "Creative Ui Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-designer-plus/thought/creative-ui-thinking.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.192Z", "updatedAt": "2025-07-08T09:51:33.192Z", "scannedAt": "2025-07-08T09:51:33.192Z"}}, {"id": "technical-implementation", "source": "project", "protocol": "thought", "name": "Technical Implementation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-designer-plus/thought/technical-implementation.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.192Z", "updatedAt": "2025-07-08T09:51:33.192Z", "scannedAt": "2025-07-08T09:51:33.192Z"}}, {"id": "design-to-code-workflow", "source": "project", "protocol": "execution", "name": "Design To Code Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-designer-plus/execution/design-to-code-workflow.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.192Z", "updatedAt": "2025-07-08T09:51:33.192Z", "scannedAt": "2025-07-08T09:51:33.192Z"}}, {"id": "ui-quality-standards", "source": "project", "protocol": "execution", "name": "Ui Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-designer-plus/execution/ui-quality-standards.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.192Z", "updatedAt": "2025-07-08T09:51:33.192Z", "scannedAt": "2025-07-08T09:51:33.192Z"}}, {"id": "html-css-mastery", "source": "project", "protocol": "knowledge", "name": "Html Css Mastery 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-designer-plus/knowledge/html-css-mastery.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.192Z", "updatedAt": "2025-07-08T09:51:33.192Z", "scannedAt": "2025-07-08T09:51:33.192Z"}}, {"id": "responsive-development", "source": "project", "protocol": "knowledge", "name": "Responsive Development 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-designer-plus/knowledge/responsive-development.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "ui-design-expertise", "source": "project", "protocol": "knowledge", "name": "Ui Design Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-designer-plus/knowledge/ui-design-expertise.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "ui-designer-versatile", "source": "project", "protocol": "role", "name": "Ui Designer Versatile 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/ui-designer-versatile.role.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "aesthetic-adaptation", "source": "project", "protocol": "thought", "name": "Aesthetic Adaptation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/thought/aesthetic-adaptation.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "creative-versatility", "source": "project", "protocol": "thought", "name": "Creative Versatility 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/thought/creative-versatility.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "cultural-sensitivity", "source": "project", "protocol": "thought", "name": "Cultural Sensitivity 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/thought/cultural-sensitivity.thought.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "versatile-design-workflow", "source": "project", "protocol": "execution", "name": "Versatile Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/execution/versatile-design-workflow.execution.md", "metadata": {"createdAt": "2025-07-08T09:51:33.193Z", "updatedAt": "2025-07-08T09:51:33.193Z", "scannedAt": "2025-07-08T09:51:33.193Z"}}, {"id": "design-history-mastery", "source": "project", "protocol": "knowledge", "name": "Design History Mastery 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/ui-designer-versatile/knowledge/design-history-mastery.knowledge.md", "metadata": {"createdAt": "2025-07-08T09:51:33.194Z", "updatedAt": "2025-07-08T09:51:33.194Z", "scannedAt": "2025-07-08T09:51:33.194Z"}}], "stats": {"totalResources": 35, "byProtocol": {"role": 5, "thought": 11, "execution": 8, "knowledge": 11}, "bySource": {"project": 35}}}