# DevMatch DAO治理系统需求文档

## 1. 项目概述

DevMatch DAO（去中心化自治组织）治理系统是一个社区驱动的治理平台，允许DAO成员提出、讨论、投票决定平台的发展方向和重要决策。

### 1.1 核心目标
- 实现社区民主决策
- 提高平台透明度
- 增强用户参与度
- 建立可持续的治理机制

### 1.2 设计原则
- **简化优先**: 提案管理流程简单，一步完成提交
- **身份验证**: 仅DAO成员可投票，非成员可浏览和评论
- **权重统一**: 所有DAO成员投票权重为1，不区分等级
- **扩展性**: 预留未来会员体系扩展空间

## 2. 功能需求

### 2.1 用户权限体系

#### 2.1.1 用户类型
- **未登录用户**: 可浏览提案，无法参与任何操作
- **普通用户**: 可浏览、评论、提交提案，但不能投票
- **DAO成员**: 拥有所有权限，包括投票权

#### 2.1.2 权限矩阵
| 功能 | 未登录 | 普通用户 | DAO成员 |
|------|--------|----------|---------|
| 浏览提案 | ✅ | ✅ | ✅ |
| 提交提案 | ❌ | ✅ | ✅ |
| 评论提案 | ❌ | ✅ | ✅ |
| 投票决策 | ❌ | ❌ | ✅ |
| 查看投票结果 | ✅ | ✅ | ✅ |

### 2.2 提案管理系统

#### 2.2.1 提案生命周期
```
提交 → 审核 → 投票 → 结果确定 → 执行/归档
```

#### 2.2.2 提案状态说明
- **reviewing**: 管理员审核中
- **voting**: 公开投票中（7天期限）
- **approved**: 投票通过，待执行
- **rejected**: 投票未通过
- **executing**: 执行中
- **archived**: 已归档

#### 2.2.3 提案分类
- **平台功能改进**: 新功能、界面优化等
- **社区治理**: DAO规则、投票机制、权限管理
- **商业模式**: 收入模式、定价策略、合作伙伴
- **技术升级**: 架构升级、安全增强、性能优化
- **资源分配**: 预算分配、奖励机制
- **其他提案**: 不属于以上分类的提案

#### 2.2.4 提案字段结构
```javascript
{
  id: "P2024-001",              // 提案编号
  title: "提案标题",            // 标题 (必填，60字符限制)
  category: "feature",          // 分类 (必填)
  content: "详细描述...",       // 详情 (必填，1500字符限制)
  evidence: "支持论据...",      // 论据 (可选，1000字符限制)
  impact: "high_value_low_difficulty", // 影响评估
  status: "voting",             // 当前状态
  creator_id: 1,                // 创建者ID
  created_at: "2024-07-15T10:00:00Z", // 创建时间
  deadline: "2024-07-31T23:59:59Z",   // 截止时间
  vote_stats: {                 // 投票统计
    support: 148,
    oppose: 24,
    abstain: 5,
    total: 177
  },
  comments_count: 62,           // 评论数
  views_count: 435              // 浏览数
}
```

### 2.3 投票系统

#### 2.3.1 投票规则
- **投票权**: 仅DAO成员可投票
- **权重**: 每个DAO成员投票权重为1
- **选项**: 支持(support)、反对(oppose)、弃权(abstain)
- **修改**: 投票期内可修改投票
- **透明**: 投票统计实时公开

#### 2.3.2 通过标准
- 投票期结束后统计
- 支持票数 > 反对票数 = 提案通过
- 平票情况下提案被拒绝

### 2.4 评论系统

#### 2.4.1 评论功能
- 支持多级回复
- 实时发布，无需审核
- 显示评论者身份（DAO成员/普通用户）
- 支持点赞功能

#### 2.4.2 评论管理
- 用户可编辑自己的评论
- 管理员可删除不当评论
- 评论按时间排序显示

### 2.5 DAO成员管理

#### 2.5.1 成员类型
- **dao_member**: 普通DAO成员
- **founding_member**: 创始成员（预留）
- **core_member**: 核心成员（预留）

#### 2.5.2 成员权益
- 投票权（权重为1）
- 提案优先审核
- 参与治理决策
- 查看详细统计数据

## 3. 技术实现

### 3.1 前端实现 (phototype/advice.html)

#### 3.1.1 核心组件
- **DAOGovernance类**: 主要业务逻辑控制器
- **提案表单**: 简化的一步提交表单
- **投票界面**: 支持/反对/弃权按钮
- **身份显示**: DAO成员状态徽章

#### 3.1.2 关键特性
- 响应式设计，支持移动端
- 实时字符计数和验证
- 身份验证和权限控制
- 模拟API调用（可替换为真实API）

#### 3.1.3 样式设计
- 遵循DevMatch设计系统
- 渐变色彩方案
- Neumorphism设计风格
- 交互动画效果

### 3.2 后端实现

#### 3.2.1 数据模型 (backend/src/models/dao.js)
```javascript
// 提案模型
class ProposalModel {
  // 提案CRUD操作
  // 投票处理
  // 评论管理
  // 状态流转
}

// DAO成员模型
class DAOMemberModel {
  // 成员状态管理
  // 权限验证
  // 成员操作记录
}
```

#### 3.2.2 API路由 (backend/src/routes/dao.js)
```javascript
// 提案相关
GET    /api/dao/proposals          // 获取提案列表
GET    /api/dao/proposals/:id      // 获取提案详情
POST   /api/dao/proposals          // 创建提案
PUT    /api/dao/admin/proposals/:id/status // 更新提案状态

// 投票相关
POST   /api/dao/proposals/:id/vote // 投票
GET    /api/dao/proposals/:id/votes // 获取投票统计

// 评论相关
GET    /api/dao/proposals/:id/comments // 获取评论
POST   /api/dao/proposals/:id/comments // 添加评论

// 成员管理
GET    /api/dao/members/me          // 获取当前用户状态
GET    /api/dao/members/status/:userId // 获取指定用户状态
POST   /api/dao/admin/members       // 添加DAO成员
DELETE /api/dao/admin/members/:userId // 移除DAO成员

// 统计数据
GET    /api/dao/stats               // 获取治理统计
```

#### 3.2.3 中间件
- `requireAuth`: 身份验证中间件
- `attachDAOStatus`: DAO状态获取中间件
- 权限验证和错误处理

### 3.3 管理后台实现 (manage/src/views/DAOManagement.vue)

#### 3.3.1 功能模块
- **DAO成员管理**: 添加/移除成员，查看成员列表
- **提案管理**: 审核提案，修改状态，查看详情
- **投票统计**: 投票数据分析，参与度统计
- **数据面板**: 关键指标展示

#### 3.3.2 管理功能
- 批量操作支持
- 数据导出功能
- 实时统计更新
- 权限分级管理

## 4. 数据库设计

### 4.1 表结构设计

#### 4.1.1 提案表 (proposals)
```sql
CREATE TABLE proposals (
  id VARCHAR(20) PRIMARY KEY,          -- P2024-001
  title VARCHAR(60) NOT NULL,          -- 提案标题
  category VARCHAR(20) NOT NULL,       -- 分类
  content TEXT NOT NULL,               -- 详细内容
  evidence TEXT,                       -- 支持论据
  impact VARCHAR(30),                  -- 影响评估
  status VARCHAR(20) DEFAULT 'reviewing', -- 状态
  creator_id INT NOT NULL,             -- 创建者ID
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deadline TIMESTAMP,                  -- 截止时间
  comments_count INT DEFAULT 0,        -- 评论数
  views_count INT DEFAULT 0,           -- 浏览数
  INDEX idx_status (status),
  INDEX idx_creator (creator_id),
  INDEX idx_created (created_at)
);
```

#### 4.1.2 投票表 (dao_votes)
```sql
CREATE TABLE dao_votes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  proposal_id VARCHAR(20) NOT NULL,    -- 提案ID
  user_id INT NOT NULL,                -- 投票用户ID
  vote_type VARCHAR(10) NOT NULL,      -- support/oppose/abstain
  voting_power INT DEFAULT 1,          -- 投票权重
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_vote (proposal_id, user_id),
  INDEX idx_proposal (proposal_id),
  INDEX idx_user (user_id)
);
```

#### 4.1.3 DAO成员表 (dao_members)
```sql
CREATE TABLE dao_members (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL UNIQUE,         -- 用户ID
  membership_type VARCHAR(20) DEFAULT 'dao_member', -- 成员类型
  voting_power INT DEFAULT 1,          -- 投票权重
  status VARCHAR(20) DEFAULT 'active', -- 状态
  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_type (membership_type)
);
```

#### 4.1.4 评论表 (dao_comments)
```sql
CREATE TABLE dao_comments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  proposal_id VARCHAR(20) NOT NULL,    -- 提案ID
  user_id INT NOT NULL,                -- 评论用户ID
  parent_id INT NULL,                  -- 父评论ID
  content TEXT NOT NULL,               -- 评论内容
  likes_count INT DEFAULT 0,           -- 点赞数
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_proposal (proposal_id),
  INDEX idx_user (user_id),
  INDEX idx_parent (parent_id)
);
```

### 4.2 数据关系
- proposals ← dao_votes (一对多)
- proposals ← dao_comments (一对多)
- users ← dao_members (一对一)
- users ← dao_votes (一对多)
- users ← dao_comments (一对多)

## 5. 部署和配置

### 5.1 环境要求
- Node.js 16+
- Vue.js 3
- Express.js 4+
- MySQL 8.0+ (生产环境)

### 5.2 配置文件
```javascript
// backend/.env
DAO_VOTING_PERIOD=7              // 投票期天数
DAO_MIN_SUPPORT_RATE=0.5         // 最低支持率
DAO_PROPOSAL_REVIEW_TIMEOUT=3    // 审核超时天数
```

### 5.3 部署步骤
1. 安装依赖: `npm install`
2. 配置环境变量
3. 初始化数据库表结构
4. 启动后端服务: `npm run start`
5. 启动前端服务: `npm run dev`
6. 启动管理后台: `cd manage && npm run dev`

## 6. 使用指南

### 6.1 用户操作流程

#### 6.1.1 提交提案
1. 访问 `/advice.html` 页面
2. 登录账户
3. 填写提案表单（标题、分类、详情、论据、影响评估）
4. 点击"提交提案"
5. 等待管理员审核

#### 6.1.2 参与投票（DAO成员）
1. 浏览投票中的提案
2. 查看提案详情和讨论
3. 点击"支持"或"反对"按钮
4. 确认投票（可在截止前修改）

#### 6.1.3 参与讨论
1. 在提案页面添加评论
2. 回复其他用户评论
3. 点赞优质评论

### 6.2 管理员操作流程

#### 6.2.1 审核提案
1. 登录管理后台
2. 进入"DAO治理"页面
3. 查看"提案管理"选项卡
4. 审核提案内容
5. 修改状态为"投票中"或"拒绝"

#### 6.2.2 管理DAO成员
1. 进入"DAO成员管理"选项卡
2. 点击"添加成员"
3. 输入用户ID和成员类型
4. 确认添加

### 6.3 API使用示例

#### 6.3.1 获取提案列表
```javascript
// GET /api/dao/proposals?status=voting&category=feature
{
  "success": true,
  "data": [
    {
      "id": "P2024-023",
      "title": "优化项目自动分解引擎",
      "status": "voting",
      "vote_stats": {
        "support": 148,
        "oppose": 24,
        "total": 172
      }
    }
  ]
}
```

#### 6.3.2 提交投票
```javascript
// POST /api/dao/proposals/P2024-023/vote
{
  "vote_type": "support"
}

// Response
{
  "success": true,
  "data": {
    "support": 149,
    "oppose": 24,
    "total": 173
  },
  "message": "投票成功"
}
```

## 7. 扩展计划

### 7.1 第二阶段功能
- 多级投票权重系统
- 提案模板和向导
- 自动化执行机制
- 投票激励系统

### 7.2 集成计划
- 与项目管理系统集成
- 钱包连接和Token治理
- 跨平台通知系统
- 数据分析和报告

### 7.3 性能优化
- 数据库查询优化
- 缓存策略实施
- CDN配置
- 移动端优化

## 8. 风险评估

### 8.1 技术风险
- 投票数据一致性
- 并发投票处理
- 数据库性能瓶颈

### 8.2 治理风险
- 低参与率问题
- 恶意提案攻击
- 权限滥用风险

### 8.3 缓解措施
- 完善的错误处理
- 投票验证机制
- 管理员监督制度
- 定期数据备份

## 9. 测试计划

### 9.1 功能测试
- 提案提交流程测试
- 投票功能测试
- 权限控制测试
- 状态流转测试

### 9.2 性能测试
- 并发投票压力测试
- 大量提案加载测试
- 数据库性能测试

### 9.3 安全测试
- 权限绕过测试
- 投票重复提交测试
- SQL注入测试
- XSS攻击测试

---

**文档版本**: v1.0  
**最后更新**: 2024年7月  
**维护人员**: DevMatch开发团队

此文档详细描述了DevMatch DAO治理系统的完整需求、技术实现和使用说明。系统设计遵循简化原则，确保用户友好性和可扩展性。
