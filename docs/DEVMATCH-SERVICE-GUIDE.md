# DevMatch 服务管理指南

## 🚀 快速开始

### 1. 立即启动服务

```bash
# 方法1：使用服务管理器
./service-manager.sh

# 方法2：使用快速启动脚本（推荐）
./quick-start.sh
```

### 2. 检查服务状态

```bash
./service-manager.sh status
```

### 3. 访问应用

- **前端**: http://localhost:3000
- **后端**: http://localhost:3001
- **测试账号**: <EMAIL> / password123

## 📋 问题解决

### 🔧 登录问题 - 完全解决方案

如果遇到登录超时或网络连接失败，使用以下步骤**彻底解决**：

```bash
# 1. 一键修复所有问题
./service-manager.sh restart

# 2. 如果还有问题，清理并重新启动
./service-manager.sh cleanup
./service-manager.sh

# 3. 验证服务正常
./service-manager.sh status
```

### 🔄 自动恢复机制

为了防止服务故障复发，启用自动恢复：

```bash
# 启动自动恢复监控（后台运行）
./auto-recovery.sh start &

# 查看自动恢复状态
./auto-recovery.sh status

# 查看恢复日志
./auto-recovery.sh log
```

## 🛠️ 服务管理工具

### service-manager.sh - 主要服务管理器

```bash
./service-manager.sh start      # 启动所有服务
./service-manager.sh stop       # 停止所有服务
./service-manager.sh restart    # 重启所有服务
./service-manager.sh status     # 检查服务状态
./service-manager.sh cleanup    # 清理进程冲突
```

**功能特点**：
- ✅ 自动清理端口冲突
- ✅ 智能健康检查
- ✅ 数据库自动初始化
- ✅ 测试用户自动创建
- ✅ 完整的错误处理

### auto-recovery.sh - 自动恢复监控

```bash
./auto-recovery.sh start        # 启动监控
./auto-recovery.sh stop         # 停止监控
./auto-recovery.sh status       # 查看状态
./auto-recovery.sh log          # 查看日志
```

**功能特点**：
- ✅ 30秒间隔健康检查
- ✅ 自动重启故障服务
- ✅ 智能重试机制
- ✅ 完整的日志记录

## 🎯 系统服务化（可选）

### 安装为系统服务

```bash
# 安装DevMatch为系统服务
./install-devmatch.sh

# 创建devmatch命令行工具
devmatch start      # 启动服务
devmatch stop       # 停止服务
devmatch status     # 查看状态
devmatch install    # 安装为开机自启
devmatch logs       # 查看日志
```

### 开机自启动

```bash
# 启用开机自启
devmatch install

# 禁用开机自启
devmatch uninstall
```

## 🔍 故障排查

### 常见问题及解决方案

#### 1. 端口被占用
```bash
# 问题：EADDRINUSE: address already in use
# 解决：自动清理端口冲突
./service-manager.sh cleanup
./service-manager.sh start
```

#### 2. 登录超时
```bash
# 问题：timeout of 10000ms exceeded
# 解决：重启后端服务
./service-manager.sh restart
```

#### 3. 数据库连接失败
```bash
# 问题：数据库连接错误
# 解决：重置数据库
cd backend
npx prisma db push --force-reset
npx prisma db seed
```

#### 4. 测试用户不存在
```bash
# 问题：邮箱或密码错误
# 解决：创建测试用户
cd backend
node create-test-user.js
```

### 日志查看

```bash
# 查看服务日志
tail -f devmatch.log

# 查看错误日志
tail -f devmatch.error.log

# 查看自动恢复日志
tail -f recovery.log
```

## 📊 监控与维护

### 服务状态监控

```bash
# 实时监控服务状态
watch -n 5 './service-manager.sh status'

# 查看详细健康状况
./auto-recovery.sh status
```

### 性能优化

```bash
# 清理日志文件
rm -f *.log

# 重启服务以释放内存
./service-manager.sh restart
```

## 🚨 紧急恢复

如果所有方法都失败，使用紧急恢复流程：

```bash
# 1. 完全停止所有服务
pkill -f "node"
pkill -f "vite"

# 2. 清理所有进程
./service-manager.sh cleanup

# 3. 重新启动
./service-manager.sh start

# 4. 创建测试用户
cd backend && node create-test-user.js

# 5. 验证服务
./service-manager.sh status
```

## 📱 移动端测试

```bash
# 获取本机IP地址
ifconfig | grep "inet " | grep -v 127.0.0.1

# 在移动设备上访问
http://[你的IP]:3000
```

## 🔐 安全配置

### 生产环境部署

```bash
# 设置环境变量
export NODE_ENV=production
export JWT_SECRET=your-secret-key

# 启用HTTPS（需要SSL证书）
# 配置反向代理（Nginx）
```

### 数据库安全

```bash
# 定期备份数据库
npx prisma db push --create-only

# 更新密码加密
cd backend
node scripts/update-passwords.js
```

## 📈 扩展功能

### 集群部署

```bash
# 使用PM2进行进程管理
npm install -g pm2
pm2 start ecosystem.config.js

# 负载均衡
pm2 start app.js -i max
```

### 监控告警

```bash
# 集成监控系统
./auto-recovery.sh start
```

## 🎉 成功指标

当看到以下输出时，表示服务完全正常：

```
✅ 前端服务: 正常 (http://localhost:3000)
✅ 后端服务: 正常 (http://localhost:3001)
✅ 登录API响应正常
🎉 所有服务健康检查通过！
```

## 📞 技术支持

- 查看日志： `./service-manager.sh status`
- 自动恢复： `./auto-recovery.sh start`
- 紧急重启： `./service-manager.sh restart`
- 完全重置： `./service-manager.sh cleanup && ./service-manager.sh start`

---

**🚀 DevMatch 服务管理 - 让开发更简单！** 