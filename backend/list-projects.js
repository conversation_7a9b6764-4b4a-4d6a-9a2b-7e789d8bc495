import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function listProjects() {
  try {
    const projects = await prisma.project.findMany({
      select: {
        id: true,
        title: true,
        reviewStatus: true,
        status: true
      }
    });
    
    console.log('数据库中的项目:');
    projects.forEach(p => {
      console.log(`ID: ${p.id}, 标题: ${p.title}, 审核状态: ${p.reviewStatus}, 项目状态: ${p.status}`);
    });
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

listProjects();