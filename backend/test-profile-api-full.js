import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000/api';

// 测试用户注册
async function testRegister() {
  console.log('📝 测试用户注册...');
  
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123456!',
    confirmPassword: 'Test123456!',
    verificationCode: '123456'
  };
  
  const response = await fetch(`${API_BASE}/auth/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(testUser)
  });
  
  const data = await response.json();
  console.log('注册响应:', data);
  
  if (data.success && data.data.token) {
    console.log('✅ 注册成功，Token:', data.data.token.substring(0, 20) + '...');
    return data.data.token;
  } else {
    console.log('❌ 注册失败，尝试登录现有用户');
    return await testLogin();
  }
}

// 测试用户登录
async function testLogin() {
  console.log('🔐 测试用户登录...');
  
  const response = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Test123456!'
    })
  });
  
  const data = await response.json();
  console.log('登录响应:', data);
  
  if (data.success && data.data.token) {
    console.log('✅ 登录成功，Token:', data.data.token.substring(0, 20) + '...');
    return data.data.token;
  } else {
    console.log('❌ 登录失败');
    return null;
  }
}

// 测试获取用户资料
async function testGetProfile(token) {
  console.log('\n📋 测试获取用户资料...');
  
  const response = await fetch(`${API_BASE}/auth/profile`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  console.log('获取资料响应:', JSON.stringify(data, null, 2));
  
  if (data.success) {
    console.log('✅ 获取用户资料成功');
    return data.data.user;
  } else {
    console.log('❌ 获取用户资料失败');
    return null;
  }
}

// 测试更新用户资料
async function testUpdateProfile(token) {
  console.log('\n💾 测试更新用户资料...');
  
  const updateData = {
    username: '测试用户更新',
    bio: '这是更新后的个人简介',
    location: '北京',
    gender: 'male',
    phone: '13888888888',
    wechat: 'testwechat123',
    qq: '123456789',
    profession: '前端开发工程师',
    experience: '3-5',
    expectedSalary: '15k-20k',
    skills: ['JavaScript', 'Vue.js', 'React', 'Node.js'],
    workType: 'remote',
    workTime: 'fulltime'
  };
  
  const response = await fetch(`${API_BASE}/auth/profile`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  });
  
  const data = await response.json();
  console.log('更新资料响应:', JSON.stringify(data, null, 2));
  
  if (data.success) {
    console.log('✅ 更新用户资料成功');
    return data.data.user;
  } else {
    console.log('❌ 更新用户资料失败');
    return null;
  }
}

// 执行测试
async function runTests() {
  try {
    const token = await testRegister();
    if (!token) {
      console.log('❌ 无法获取token，停止测试');
      return;
    }
    
    await testGetProfile(token);
    await testUpdateProfile(token);
    await testGetProfile(token); // 再次获取以验证更新
    
    console.log('\n🎉 所有测试完成');
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

runTests();