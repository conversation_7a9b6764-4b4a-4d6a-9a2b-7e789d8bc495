import jwt from 'jsonwebtoken';
import { AppError, asyncHandler } from './errorHandler.js';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// JWT认证中间件
export const authenticate = asyncHandler(async (req, res, next) => {
  let token;
  
  // 从请求头获取token
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (!token) {
    throw new AppError('未提供访问令牌', 401, 'NO_TOKEN');
  }
  
  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId || decoded.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatarUrl: true,
        createdAt: true
      }
    });
    
    if (!user) {
      throw new AppError('用户不存在', 401, 'USER_NOT_FOUND');
    }
    
    // 将用户信息添加到请求对象
    req.user = user;
    next();
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
    } else if (error.name === 'TokenExpiredError') {
      throw new AppError('访问令牌已过期', 401, 'TOKEN_EXPIRED');
    }
    throw error;
  }
});

// 可选认证中间件（不强制登录）
export const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;
  
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId || decoded.id },
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          avatarUrl: true,
          createdAt: true
        }
      });
      
      if (user) {
        req.user = user;
      }
    } catch (error) {
      // 可选认证失败时不抛出错误，只是不设置user
      req.logger?.warn('可选认证失败', { error: error.message });
    }
  }
  
  next();
});

// 角色授权中间件
export const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      throw new AppError('需要登录才能访问', 401, 'UNAUTHORIZED');
    }
    
    if (!roles.includes(req.user.role)) {
      throw new AppError('没有权限访问此资源', 403, 'FORBIDDEN');
    }
    
    next();
  };
};

// 资源所有者验证中间件
export const ownerOnly = (resourceIdField = 'id') => {
  return asyncHandler(async (req, res, next) => {
    if (!req.user) {
      throw new AppError('需要登录才能访问', 401, 'UNAUTHORIZED');
    }
    
    const resourceId = parseInt(req.params[resourceIdField]);
    
    // 管理员可以访问所有资源
    if (req.user.role === 'admin') {
      return next();
    }
    
    // 检查资源是否属于当前用户
    // 这里需要根据具体的资源类型来实现检查逻辑
    // 目前先简单检查用户ID
    if (req.user.id !== resourceId) {
      throw new AppError('只能访问自己的资源', 403, 'FORBIDDEN');
    }
    
    next();
  });
};

// 项目创建者验证中间件
export const projectOwnerOnly = asyncHandler(async (req, res, next) => {
  if (!req.user) {
    throw new AppError('需要登录才能访问', 401, 'UNAUTHORIZED');
  }
  
  const projectId = parseInt(req.params.id || req.params.projectId);
  
  // 管理员可以访问所有项目
  if (req.user.role === 'admin') {
    return next();
  }
  
  // 检查项目是否属于当前用户
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    select: { creatorId: true }
  });
  
  if (!project) {
    throw new AppError('项目不存在', 404, 'PROJECT_NOT_FOUND');
  }
  
  if (project.creatorId !== req.user.id) {
    throw new AppError('只有项目创建者可以进行此操作', 403, 'FORBIDDEN');
  }
  
  next();
});

// 团队成员验证中间件
export const teamMemberOnly = asyncHandler(async (req, res, next) => {
  if (!req.user) {
    throw new AppError('需要登录才能访问', 401, 'UNAUTHORIZED');
  }
  
  const projectId = parseInt(req.params.id || req.params.projectId);
  
  // 管理员可以访问所有项目
  if (req.user.role === 'admin') {
    return next();
  }
  
  // 检查用户是否是项目成员或创建者
  const membership = await prisma.project.findFirst({
    where: {
      id: projectId,
      OR: [
        { creatorId: req.user.id },
        {
          teamMembers: {
            some: {
              userId: req.user.id,
              status: 'active'
            }
          }
        }
      ]
    }
  });
  
  if (!membership) {
    throw new AppError('只有项目成员可以访问', 403, 'FORBIDDEN');
  }
  
  next();
}); 