import { randomUUID } from 'crypto';
import logger from '../utils/logger.js';

// 请求日志中间件
export const requestLogger = (req, res, next) => {
  // 生成请求ID
  req.requestId = randomUUID();
  
  // 记录请求开始时间
  const startTime = Date.now();
  
  // 创建子logger，包含请求ID
  req.logger = logger.child({ requestId: req.requestId });
  
  // 记录请求信息
  req.logger.info('请求开始', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.method !== 'GET' ? filterSensitiveData(req.body) : undefined
  });
  
  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    req.logger.info('请求完成', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || 0
    });
  });
  
  next();
};

// 过滤敏感数据
const filterSensitiveData = (data) => {
  if (!data || typeof data !== 'object') return data;
  
  const sensitiveFields = ['password', 'passwordHash', 'token', 'secret'];
  const filtered = { ...data };
  
  sensitiveFields.forEach(field => {
    if (filtered[field]) {
      filtered[field] = '[FILTERED]';
    }
  });
  
  return filtered;
}; 