import logger from '../utils/logger.js';

// 自定义错误类
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 全局错误处理中间件
export const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // 记录错误日志
  const logMeta = {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    requestId: req.requestId
  };

  logger.error('API错误', {
    error: error.message,
    stack: err.stack,
    ...logMeta
  });

  // Prisma错误处理
  if (err.code === 'P2002') {
    error = new AppError('数据已存在，请检查唯一字段', 400, 'DUPLICATE_ERROR');
  } else if (err.code === 'P2025') {
    error = new AppError('记录不存在', 404, 'NOT_FOUND');
  } else if (err.code && err.code.startsWith('P')) {
    error = new AppError('数据库操作失败', 500, 'DATABASE_ERROR');
  }

  // JWT错误处理
  if (err.name === 'JsonWebTokenError') {
    error = new AppError('无效的令牌', 401, 'INVALID_TOKEN');
  } else if (err.name === 'TokenExpiredError') {
    error = new AppError('令牌已过期', 401, 'TOKEN_EXPIRED');
  }

  // 验证错误处理
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(error => error.message).join(', ');
    error = new AppError(message, 400, 'VALIDATION_ERROR');
  }

  // MongoDB/Mongoose CastError
  if (err.name === 'CastError') {
    error = new AppError('无效的ID格式', 400, 'INVALID_ID');
  }

  // 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = new AppError('文件大小超出限制', 400, 'FILE_TOO_LARGE');
  }

  // 设置默认错误
  if (!error.isOperational) {
    error = new AppError('服务器内部错误', 500, 'INTERNAL_ERROR');
  }

  // 构造错误响应
  const errorResponse = {
    success: false,
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    }
  };

  // 开发环境返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = err.stack;
    errorResponse.error.details = err;
  }

  res.status(error.statusCode).json(errorResponse);
};

// 异步错误捕获包装器
export const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}; 