import Joi from 'joi';

// 用户注册验证
export const validateRegistration = (data) => {
  const schema = Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': '请输入有效的邮箱地址',
        'any.required': '邮箱是必填项'
      }),
    
    password: Joi.string()
      .min(8)
      .max(128)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$'))
      .required()
      .messages({
        'string.min': '密码至少8个字符',
        'string.max': '密码不能超过128个字符',
        'string.pattern.base': '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊符号(@$!%*?&)',
        'any.required': '密码是必填项'
      }),
    
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': '确认密码与密码不匹配',
        'any.required': '确认密码是必填项'
      })
  });

  return schema.validate(data);
};

// 用户登录验证
export const validateLogin = (data) => {
  const schema = Joi.object({
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': '请输入有效的邮箱地址',
        'any.required': '邮箱是必填项'
      }),
    
    password: Joi.string()
      .required()
      .messages({
        'any.required': '密码是必填项'
      })
  });

  return schema.validate(data);
};

// 修改密码验证
export const validatePasswordChange = (data) => {
  const schema = Joi.object({
    currentPassword: Joi.string()
      .required()
      .messages({
        'any.required': '当前密码是必填项'
      }),
    
    newPassword: Joi.string()
      .min(8)
      .max(128)
      .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$'))
      .required()
      .messages({
        'string.min': '新密码至少8个字符',
        'string.max': '新密码不能超过128个字符',
        'string.pattern.base': '新密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊符号(@$!%*?&)',
        'any.required': '新密码是必填项'
      }),
    
    confirmNewPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': '确认新密码与新密码不匹配',
        'any.required': '确认新密码是必填项'
      })
  });

  return schema.validate(data);
};

// 项目创建验证
export const validateProjectCreation = (data) => {
  const schema = Joi.object({
    title: Joi.string()
      .min(5)
      .max(200)
      .required()
      .messages({
        'string.min': '项目标题至少5个字符',
        'string.max': '项目标题不能超过200个字符',
        'any.required': '项目标题是必填项'
      }),
    
    description: Joi.string()
      .min(20)
      .max(5000)
      .required()
      .messages({
        'string.min': '项目描述至少20个字符',
        'string.max': '项目描述不能超过5000个字符',
        'any.required': '项目描述是必填项'
      }),
    
    category: Joi.string()
      .valid(
        'web', 'mobile', 'desktop', 'ai', 'blockchain', 
        'game', 'tool', 'ecommerce', 'social', 'education', 'other'
      )
      .required()
      .messages({
        'any.only': '请选择有效的项目分类',
        'any.required': '项目分类是必填项'
      }),
    
    teamSize: Joi.number()
      .integer()
      .min(1)
      .max(20)
      .required()
      .messages({
        'number.base': '团队规模必须是数字',
        'number.integer': '团队规模必须是整数',
        'number.min': '团队规模至少1人',
        'number.max': '团队规模不能超过20人',
        'any.required': '团队规模是必填项'
      }),
    
    durationMonths: Joi.number()
      .integer()
      .min(1)
      .max(24)
      .optional()
      .messages({
        'number.base': '项目周期必须是数字',
        'number.integer': '项目周期必须是整数',
        'number.min': '项目周期至少1个月',
        'number.max': '项目周期不能超过24个月'
      }),
    
    workType: Joi.string()
      .valid('remote', 'onsite', 'hybrid')
      .optional()
      .messages({
        'any.only': '工作方式必须是remote、onsite或hybrid'
      }),
    
    compensationType: Joi.string()
      .valid('salary', 'equity', 'salary_equity', 'revenue_share', 'none')
      .optional()
      .messages({
        'any.only': '请选择有效的薪酬类型'
      }),
    
    budgetRange: Joi.string()
      .max(50)
      .optional()
      .messages({
        'string.max': '预算范围不能超过50个字符'
      }),
    
    prdDocumentUrl: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': '请输入有效的PRD文档链接'
      }),
    
    websiteUrl: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': '请输入有效的网站链接'
      }),
    
    appUrl: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': '请输入有效的应用链接'
      }),
    
    requirements: Joi.array()
      .items(
        Joi.object({
          skillId: Joi.number().integer().required(),
          level: Joi.string().valid('beginner', 'skilled', 'senior', 'expert').required(),
          isRequired: Joi.boolean().default(true)
        })
      )
      .min(1)
      .required()
      .messages({
        'array.min': '至少需要指定一个技能需求',
        'any.required': '技能需求是必填项'
      })
  });

  return schema.validate(data);
};

// 申请项目验证
export const validateApplicationSubmission = (data) => {
  const schema = Joi.object({
    projectId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': '项目ID必须是数字',
        'number.integer': '项目ID必须是整数',
        'any.required': '项目ID是必填项'
      }),
    
    message: Joi.string()
      .min(10)
      .max(1000)
      .optional()
      .messages({
        'string.min': '申请消息至少10个字符',
        'string.max': '申请消息不能超过1000个字符'
      })
  });

  return schema.validate(data);
};

// 消息发送验证
export const validateMessageSending = (data) => {
  const schema = Joi.object({
    receiverId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': '接收者ID必须是数字',
        'number.integer': '接收者ID必须是整数',
        'any.required': '接收者ID是必填项'
      }),
    
    subject: Joi.string()
      .min(1)
      .max(200)
      .required()
      .messages({
        'string.min': '消息主题不能为空',
        'string.max': '消息主题不能超过200个字符',
        'any.required': '消息主题是必填项'
      }),
    
    content: Joi.string()
      .min(1)
      .max(5000)
      .required()
      .messages({
        'string.min': '消息内容不能为空',
        'string.max': '消息内容不能超过5000个字符',
        'any.required': '消息内容是必填项'
      }),
    
    projectId: Joi.number()
      .integer()
      .optional()
      .messages({
        'number.base': '项目ID必须是数字',
        'number.integer': '项目ID必须是整数'
      }),
    
    messageType: Joi.string()
      .valid('invitation', 'discussion', 'system')
      .default('discussion')
      .messages({
        'any.only': '消息类型必须是invitation、discussion或system'
      })
  });

  return schema.validate(data);
};

// 技能验证
export const validateSkill = (data) => {
  const schema = Joi.object({
    name: Joi.string()
      .min(1)
      .max(50)
      .required()
      .messages({
        'string.min': '技能名称不能为空',
        'string.max': '技能名称不能超过50个字符',
        'any.required': '技能名称是必填项'
      }),
    
    category: Joi.string()
      .valid(
        'frontend', 'backend', 'mobile', 'devops', 'ai', 
        'design', 'product', 'marketing', 'other'
      )
      .required()
      .messages({
        'any.only': '请选择有效的技能分类',
        'any.required': '技能分类是必填项'
      })
  });

  return schema.validate(data);
};

// 用户技能验证
export const validateUserSkill = (data) => {
  const schema = Joi.object({
    skillId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': '技能ID必须是数字',
        'number.integer': '技能ID必须是整数',
        'any.required': '技能ID是必填项'
      }),
    
    level: Joi.string()
      .valid('beginner', 'skilled', 'senior', 'expert')
      .required()
      .messages({
        'any.only': '技能水平必须是beginner、skilled、senior或expert',
        'any.required': '技能水平是必填项'
      })
  });

  return schema.validate(data);
}; 