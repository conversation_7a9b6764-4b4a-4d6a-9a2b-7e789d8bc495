import nodemailer from 'nodemailer';

// 创建邮件传输器
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: 'smtp.163.com',
    port: 587,
    secure: false, // STARTTLS
    auth: {
      user: '<EMAIL>',
      pass: 'UQgufmJkEHYcBbH7'
    }
  });
};

// 发送邮件的通用函数
export const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: '"小概率" <<EMAIL>>',
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text
    };
    
    const info = await transporter.sendMail(mailOptions);
    console.log('邮件发送成功:', info.messageId);
    return info;
  } catch (error) {
    console.error('邮件发送失败:', error);
    throw new Error('邮件发送失败');
  }
};

// 生成密码重置邮件HTML模板（基于提供的模板）
export const generatePasswordResetHTML = (resetUrl, userEmail) => {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>密码重置 - 小概率</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background-color: #f8fafc;
          margin: 0;
          padding: 20px;
          color: #1e293b;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          border-radius: 12px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 40px 30px;
          text-align: center;
          color: white;
        }
        .header h1 {
          margin: 0;
          font-size: 32px;
          font-weight: 700;
          letter-spacing: -1px;
        }
        .content {
          padding: 40px 30px;
        }
        .content h2 {
          color: #1e293b;
          font-size: 24px;
          margin-bottom: 20px;
        }
        .content p {
          line-height: 1.6;
          margin-bottom: 20px;
          color: #475569;
        }
        .reset-button {
          display: inline-block;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          text-decoration: none;
          padding: 16px 32px;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          text-align: center;
          margin: 20px 0;
        }
        .reset-button:hover {
          opacity: 0.9;
        }
        .security-info {
          background: #f1f5f9;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
        }
        .security-info h3 {
          color: #334155;
          margin-top: 0;
          margin-bottom: 10px;
        }
        .security-info ul {
          margin: 0;
          padding-left: 20px;
        }
        .security-info li {
          margin-bottom: 8px;
          color: #64748b;
        }
        .footer {
          background: #f8fafc;
          padding: 20px 30px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        .footer p {
          margin: 0;
          color: #94a3b8;
          font-size: 14px;
        }
        .warning {
          color: #dc2626;
          font-weight: 600;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>小概率</h1>
        </div>
        
        <div class="content">
          <p>尊敬的用户：</p>
          
          <p>我们收到重置您小概率账户密码的请求。请点击下方按钮设置新密码：</p>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" class="reset-button">立即重置密码</a>
          </p>
          
          <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
          <p style="word-break: break-all; color: #8b5cf6; font-family: monospace;">${resetUrl}</p>
          
          <p style="margin-bottom: 20px;">此链接将在 <strong>15分钟</strong> 后失效，请尽快完成操作。</p>
          
          <p>如果您没有请求重置密码，请忽略此邮件。您的账户安全未受影响。</p>
          
          <div class="security-info">
            <h3>安全提示：</h3>
            <ul>
              <li>请勿将此邮件或重置链接分享给任何人</li>
              <li>小概率团队不会通过邮件或电话索要您的密码</li>
              <li>为保障账户安全，建议定期更换密码</li>
            </ul>
          </div>
          
          <p style="margin-top: 30px;">感谢您使用小概率平台！</p>
          <p>小概率团队 敬上</p>
        </div>
        
        <div class="footer">
          <p>此邮件由系统自动发送，请勿回复。</p>
          <p>如有疑问，请联系客服：<EMAIL></p>
        </div>
      </div>
    </body>
    </html>
  `;
};

// 发送密码重置邮件
export const sendPasswordResetEmail = async (email, resetUrl) => {
  const html = generatePasswordResetHTML(resetUrl, email);
  
  return await sendEmail({
    to: email,
    subject: '小概率 - 重置您的密码',
    html,
    text: `尊敬的用户：我们收到重置您小概率账户密码的请求。请访问以下链接重置您的密码：${resetUrl}\n\n此链接将在15分钟内有效。如果您没有发起此请求，请忽略此邮件。`
  });
};