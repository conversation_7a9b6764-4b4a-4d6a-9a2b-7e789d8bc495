import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// 生成安全随机字符串
export const generateSecureToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

// 生成JWT令牌
export const generateJWTToken = (payload, expiresIn = '1h') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

// 验证JWT令牌
export const verifyJWTToken = (token) => {
  try {
    return jwt.verify(token, process.env.JWT_SECRET);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

// 生成密码重置令牌
export const generatePasswordResetToken = (email, userId) => {
  const payload = {
    email,
    userId,
    type: 'password_reset',
    timestamp: Date.now()
  };
  
  // 生成15分钟有效期的令牌
  return generateJWTToken(payload, '15m');
};

// 验证密码重置令牌
export const verifyPasswordResetToken = (token) => {
  try {
    const decoded = verifyJWTToken(token);
    
    // 检查令牌类型
    if (decoded.type !== 'password_reset') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
};

// 生成安全的数据库令牌
export const generateDatabaseToken = () => {
  return generateSecureToken(64);
};