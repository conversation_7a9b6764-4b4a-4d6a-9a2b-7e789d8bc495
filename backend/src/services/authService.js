import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { AppError } from '../middleware/errorHandler.js';
import prisma from '../config/database.js';
import { generatePasswordResetToken, generateDatabaseToken } from '../utils/tokenUtils.js';
import { sendPasswordResetEmail } from '../utils/emailService.js';

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { id: userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// 用户注册
export const registerUser = async (userData) => {
  const { email, password } = userData;
  
  // 检查邮箱是否已存在
  const existingEmail = await prisma.user.findUnique({
    where: { email }
  });
  
  if (existingEmail) {
    throw new AppError('邮箱已存在', 400, 'EMAIL_EXISTS');
  }
  
  // 密码加密
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(password, saltRounds);
  
  // 创建用户
  const user = await prisma.user.create({
    data: {
      email,
      passwordHash,
      role: 'developer'
    },
    select: {
      id: true,
      username: true,
      email: true,
      role: true,
      avatarUrl: true,
      createdAt: true
    }
  });
  
  // 生成token
  const token = generateToken(user.id);
  
  return {
    user,
    token
  };
};

// 用户登录
export const loginUser = async (loginData) => {
  const { email, password } = loginData;
  
  // 查找用户（包含密码）
  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      username: true,
      email: true,
      passwordHash: true,
      role: true,
      avatarUrl: true,
      createdAt: true
    }
  });
  
  if (!user) {
    throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
  }
  
  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
  
  if (!isPasswordValid) {
    throw new AppError('邮箱或密码错误', 401, 'INVALID_CREDENTIALS');
  }
  
  // 移除密码字段
  const { passwordHash, ...userWithoutPassword } = user;
  
  // 生成token
  const token = generateToken(user.id);
  
  return {
    user: userWithoutPassword,
    token
  };
};

// 获取用户信息
export const getCurrentUser = async (userId) => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      username: true,
      email: true,
      role: true,
      avatarUrl: true,
      bio: true,
      location: true,
      githubUrl: true,
      bilibiliUrl: true,
      portfolioUrl: true,
      gender: true,
      phone: true,
      wechat: true,
      profession: true,
      workType: true,
      workTime: true,
      cooperationMode: true,
      createdAt: true,
      updatedAt: true,
      userSkills: {
        include: {
          skill: true
        }
      },
      projects: {
        select: {
          id: true,
          title: true,
          status: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      },
      _count: {
        select: {
          projects: true,
          applications: true,
          teamMembers: true
        }
      }
    }
  });
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }
  
  return user;
};

// 更新用户信息
export const updateUserProfile = async (userId, updateData) => {
  const {
    username,
    bio,
    location,
    githubUrl,
    bilibiliUrl,
    portfolioUrl,
    avatarUrl,
    // 个人信息字段
    gender,
    phone,
    wechat,
    profession,
    // 工作偏好字段
    workType,
    workTime,
    cooperationMode
  } = updateData;
  
  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      username,
      bio,
      location,
      githubUrl,
      bilibiliUrl,
      portfolioUrl,
      avatarUrl,
      gender,
      phone,
      wechat,
      profession,
      workType,
      workTime,
      cooperationMode
    },
    select: {
      id: true,
      username: true,
      email: true,
      role: true,
      avatarUrl: true,
      bio: true,
      location: true,
      githubUrl: true,
      bilibiliUrl: true,
      portfolioUrl: true,
      gender: true,
      phone: true,
      wechat: true,
      profession: true,
      workType: true,
      workTime: true,
      cooperationMode: true,
      updatedAt: true
    }
  });
  
  return user;
};

// 修改密码
export const changePassword = async (userId, passwordData) => {
  const { currentPassword, newPassword } = passwordData;
  
  // 获取用户当前密码
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { passwordHash: true }
  });
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }
  
  // 验证当前密码
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash);
  
  if (!isCurrentPasswordValid) {
    throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
  }
  
  // 加密新密码
  const saltRounds = 12;
  const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);
  
  // 更新密码
  await prisma.user.update({
    where: { id: userId },
    data: { passwordHash: newPasswordHash }
  });
  
  return { message: '密码修改成功' };
};

// 验证token
export const verifyToken = async (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatarUrl: true,
        createdAt: true
      }
    });
    
    if (!user) {
      throw new AppError('用户不存在', 401, 'USER_NOT_FOUND');
    }
    
    return user;
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new AppError('无效的访问令牌', 401, 'INVALID_TOKEN');
    } else if (error.name === 'TokenExpiredError') {
      throw new AppError('访问令牌已过期', 401, 'TOKEN_EXPIRED');
    }
    throw error;
  }
};

// 忘记密码 - 发送重置邮件
export const forgotPassword = async (email) => {
  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email },
    select: {
      id: true,
      email: true,
      username: true
    }
  });
  
  if (!user) {
    throw new AppError('该邮箱未注册', 404, 'EMAIL_NOT_FOUND');
  }
  
  // 检查是否存在未过期的重置令牌
  const existingToken = await prisma.passwordResetToken.findFirst({
    where: {
      userId: user.id,
      isUsed: false,
      expiresAt: {
        gt: new Date()
      }
    }
  });
  
  if (existingToken) {
    throw new AppError('重置邮件已发送，请稍后再试', 429, 'RESET_EMAIL_SENT');
  }
  
  // 生成重置令牌
  const resetToken = generatePasswordResetToken(user.email, user.id);
  const dbToken = generateDatabaseToken();
  
  // 计算过期时间（15分钟）
  const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
  
  // 保存令牌到数据库
  await prisma.passwordResetToken.create({
    data: {
      email: user.email,
      token: dbToken,
      userId: user.id,
      expiresAt
    }
  });
  
  // 生成重置链接
  const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/forgot-password?token=${resetToken}`;
  
  // 发送邮件
  await sendPasswordResetEmail(user.email, resetUrl);
  
  return {
    message: '密码重置邮件已发送，请检查您的邮箱'
  };
};

// 重置密码
export const resetPassword = async (resetData) => {
  const { token, password } = resetData;
  
  try {
    // 验证JWT令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'password_reset') {
      throw new AppError('无效的重置令牌', 400, 'INVALID_TOKEN');
    }
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true
      }
    });
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 验证邮箱匹配
    if (user.email !== decoded.email) {
      throw new AppError('无效的重置令牌', 400, 'INVALID_TOKEN');
    }
    
    // 检查数据库中是否有有效的重置令牌
    const dbToken = await prisma.passwordResetToken.findFirst({
      where: {
        userId: user.id,
        email: user.email,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });
    
    if (!dbToken) {
      throw new AppError('重置令牌已过期或无效', 400, 'TOKEN_EXPIRED');
    }
    
    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // 使用事务更新密码并标记令牌为已使用
    await prisma.$transaction([
      prisma.user.update({
        where: { id: user.id },
        data: { passwordHash }
      }),
      prisma.passwordResetToken.update({
        where: { id: dbToken.id },
        data: { isUsed: true }
      })
    ]);
    
    return {
      message: '密码重置成功'
    };
    
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      throw new AppError('重置令牌已过期或无效', 400, 'INVALID_TOKEN');
    }
    throw error;
  }
}; 