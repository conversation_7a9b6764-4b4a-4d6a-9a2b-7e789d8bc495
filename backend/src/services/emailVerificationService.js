import { PrismaClient } from '@prisma/client';
import nodemailer from 'nodemailer';
import crypto from 'crypto';

const prisma = new PrismaClient();

// 邮件发送配置
const createTransporter = () => {
  return nodemailer.createTransport({
    host: 'smtp.163.com',
    port: 465,
    secure: true,
    auth: {
      user: '<EMAIL>',
      pass: 'UQgufmJkEHYcBbH7'
    },
    tls: {
      rejectUnauthorized: false
    },
    pool: false,
    debug: false,
    logger: false
  });
};

// 生成6位数字验证码
const generateVerificationCode = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// 发送邮箱验证码
export const sendEmailVerificationCode = async (email, type = 'registration') => {
  try {
    // 检查是否存在未过期的验证码
    const existingCode = await prisma.emailVerification.findFirst({
      where: {
        email,
        type,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (existingCode) {
      // 如果验证码是在30秒内发送的，则拒绝重新发送
      const thirtySecondsAgo = new Date(Date.now() - 30 * 1000);
      if (existingCode.createdAt > thirtySecondsAgo) {
        throw new Error('验证码发送过于频繁，请稍后再试');
      }
      
      // 如果超过30秒，删除旧的验证码
      await prisma.emailVerification.delete({
        where: {
          id: existingCode.id
        }
      });
    }

    // 生成验证码
    const code = generateVerificationCode();
    
    // 验证码有效期为10分钟
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    // 保存验证码到数据库
    await prisma.emailVerification.create({
      data: {
        email,
        code,
        type,
        expiresAt
      }
    });

    // 发送真实邮件
    const transporter = createTransporter();
    
    const typeMap = {
      registration: '注册验证',
      password_reset: '密码重置',
      email_verification: '邮箱验证'
    };

    const subject = `小概率 - ${typeMap[type]}验证码`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
          <h1 style="color: white; margin: 0; font-size: 32px; font-weight: 800;">小概率</h1>
          <p style="color: #e0e7ff; margin: 10px 0 0 0; font-size: 16px;">让成功成为必然</p>
        </div>
        
        <div style="background: #f8fafc; padding: 30px; border-radius: 12px; border: 1px solid #e2e8f0;">
          <h2 style="color: #1e293b; margin-top: 0; font-size: 24px;">您的验证码</h2>
          <p style="color: #64748b; line-height: 1.6; margin-bottom: 25px;">
            您正在进行${typeMap[type]}操作，请使用以下验证码完成验证：
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; text-align: center; margin: 25px 0; border: 2px solid #8b5cf6;">
            <div style="font-size: 36px; font-weight: bold; color: #8b5cf6; letter-spacing: 8px; font-family: 'Courier New', monospace;">
              ${code}
            </div>
          </div>
          
          <p style="color: #ef4444; font-size: 14px; text-align: center; margin-bottom: 20px;">
            ⚠️ 验证码有效期为10分钟，请及时使用
          </p>
          
          <div style="background: #fef3c7; padding: 15px; border-radius: 8px; border: 1px solid #fbbf24;">
            <p style="color: #92400e; font-size: 14px; margin: 0;">
              <strong>安全提醒：</strong>请勿将验证码透露给他人，如非本人操作，请忽略此邮件。
            </p>
          </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #64748b; font-size: 14px;">
          <p>此邮件由系统自动发送，请勿回复</p>
          <p>© 2024 小概率平台. 保留所有权利</p>
        </div>
      </div>
    `;

    await transporter.sendMail({
      from: {
        name: '小概率',
        address: '<EMAIL>'
      },
      to: email,
      subject,
      html
    });

    console.log(`✅ 验证码已发送至 ${email}，验证码：${code}`);
    
    return {
      success: true,
      message: '验证码已发送，请查收邮件'
    };

  } catch (error) {
    console.error('发送验证码失败:', error);
    throw new Error(error.message || '发送验证码失败');
  }
};

// 验证邮箱验证码（不标记为已使用）- 用于密码重置流程
export const verifyEmailCodeForReset = async (email, code, type = 'password_reset') => {
  try {
    // 查找验证码
    const verification = await prisma.emailVerification.findFirst({
      where: {
        email,
        code,
        type,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (!verification) {
      throw new Error('验证码无效或已过期');
    }

    console.log(`✅ 邮箱验证码验证成功（未标记使用）：${email}`);
    
    return {
      success: true,
      message: '验证码验证成功',
      verificationId: verification.id
    };

  } catch (error) {
    console.error('验证码验证失败:', error);
    throw new Error(error.message || '验证码验证失败');
  }
};

// 验证邮箱验证码
export const verifyEmailCode = async (email, code, type = 'registration') => {
  try {
    // 查找验证码
    const verification = await prisma.emailVerification.findFirst({
      where: {
        email,
        code,
        type,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });

    if (!verification) {
      throw new Error('验证码无效或已过期');
    }

    // 标记验证码为已使用
    await prisma.emailVerification.update({
      where: {
        id: verification.id
      },
      data: {
        isUsed: true
      }
    });

    console.log(`✅ 邮箱验证码验证成功：${email}`);
    
    return {
      success: true,
      message: '验证码验证成功'
    };

  } catch (error) {
    console.error('验证码验证失败:', error);
    throw new Error(error.message || '验证码验证失败');
  }
};

// 清理过期验证码
export const cleanupExpiredCodes = async () => {
  try {
    const result = await prisma.emailVerification.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    console.log(`🗑️ 清理了 ${result.count} 个过期验证码`);
    return result.count;
  } catch (error) {
    console.error('清理过期验证码失败:', error);
    throw error;
  }
};

export default {
  sendEmailVerificationCode,
  verifyEmailCode,
  verifyEmailCodeForReset,
  cleanupExpiredCodes
};