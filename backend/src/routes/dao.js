import express from 'express';
import { ProposalModel, DAOMemberModel } from '../models/dao.js';

const router = express.Router();

// 初始化模型实例 - 使用单例确保数据一致性
const proposalModel = new ProposalModel();
const daoMemberModel = new DAOMemberModel();

// 中间件：验证用户登录
const requireAuth = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ success: false, message: '请先登录' });
  }
  
  // 模拟token验证，实际项目中应使用JWT验证
  req.user = { id: 1, username: '测试用户' }; // 模拟用户信息
  next();
};

// 中间件：获取用户DAO状态
const attachDAOStatus = async (req, res, next) => {
  if (req.user) {
    const daoStatus = await daoMemberModel.getUserDAOStatus(req.user.id);
    req.user.daoStatus = daoStatus.data;
  }
  next();
};

// ===================== 提案相关API =====================

// 获取提案列表
router.get('/proposals', async (req, res) => {
  try {
    const filters = {
      status: req.query.status,
      category: req.query.category,
      sort: req.query.sort,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10
    };

    const result = await proposalModel.getProposals(filters);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取提案列表失败',
      error: error.message
    });
  }
});

// 获取提案详情
router.get('/proposals/:id', async (req, res) => {
  try {
    const result = await proposalModel.getProposal(req.params.id);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取提案详情失败',
      error: error.message
    });
  }
});

// 创建提案
router.post('/proposals', requireAuth, async (req, res) => {
  try {
    const { title, category, content, evidence, impact } = req.body;

    // 验证必填字段
    if (!title || !category || !content) {
      return res.status(400).json({
        success: false,
        message: '标题、分类和详情为必填字段'
      });
    }

    const proposalData = {
      title: title.trim(),
      category,
      content: content.trim(),
      evidence: evidence?.trim() || '',
      impact: impact || 'other'
    };

    const result = await proposalModel.createProposal(proposalData, req.user.id);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建提案失败',
      error: error.message
    });
  }
});

// ===================== 投票相关API =====================

// 投票
router.post('/proposals/:id/vote', requireAuth, attachDAOStatus, async (req, res) => {
  try {
    const { vote_type } = req.body;

    if (!['support', 'oppose', 'abstain'].includes(vote_type)) {
      return res.status(400).json({
        success: false,
        message: '无效的投票类型'
      });
    }

    const result = await proposalModel.vote(
      req.params.id,
      req.user.id,
      vote_type,
      req.user.daoStatus
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '投票失败',
      error: error.message
    });
  }
});

// 获取投票统计
router.get('/proposals/:id/votes', async (req, res) => {
  try {
    const proposalResult = await proposalModel.getProposal(req.params.id);
    
    if (!proposalResult.success) {
      return res.status(404).json(proposalResult);
    }

    res.json({
      success: true,
      data: proposalResult.data.vote_stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取投票统计失败',
      error: error.message
    });
  }
});

// ===================== 评论相关API =====================

// 获取提案评论
router.get('/proposals/:id/comments', async (req, res) => {
  try {
    const result = await proposalModel.getComments(req.params.id);
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取评论失败',
      error: error.message
    });
  }
});

// 添加评论
router.post('/proposals/:id/comments', requireAuth, async (req, res) => {
  try {
    const { content, parent_id } = req.body;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: '评论内容不能为空'
      });
    }

    const result = await proposalModel.addComment(
      req.params.id,
      req.user.id,
      content.trim(),
      parent_id
    );

    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '添加评论失败',
      error: error.message
    });
  }
});

// ===================== DAO成员管理API =====================

// 获取用户DAO状态
router.get('/members/status/:userId', async (req, res) => {
  try {
    const result = await daoMemberModel.getUserDAOStatus(parseInt(req.params.userId));
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取DAO状态失败',
      error: error.message
    });
  }
});

// 获取当前用户DAO状态
router.get('/members/me', requireAuth, attachDAOStatus, async (req, res) => {
  try {
    res.json({
      success: true,
      data: req.user.daoStatus
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户状态失败',
      error: error.message
    });
  }
});

// ===================== 管理员API =====================

// 更新提案状态（管理员功能）
router.put('/admin/proposals/:id/status', requireAuth, async (req, res) => {
  try {
    // TODO: 添加管理员权限验证
    const { status } = req.body;

    const validStatuses = ['reviewing', 'voting', 'approved', 'rejected', 'executing', 'archived'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const result = await proposalModel.updateProposalStatus(
      req.params.id,
      status,
      req.user.id
    );

    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新提案状态失败',
      error: error.message
    });
  }
});

// 添加DAO成员（管理员功能）
router.post('/admin/members', requireAuth, async (req, res) => {
  try {
    // TODO: 添加管理员权限验证
    const { user_id, membership_type = 'dao_member' } = req.body;

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: '用户ID是必需的'
      });
    }

    const result = await daoMemberModel.addDAOMember(user_id, membership_type);
    res.status(201).json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '添加DAO成员失败',
      error: error.message
    });
  }
});

// 移除DAO成员（管理员功能）
router.delete('/admin/members/:userId', requireAuth, async (req, res) => {
  try {
    // TODO: 添加管理员权限验证
    const result = await daoMemberModel.removeDAOMember(parseInt(req.params.userId));
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '移除DAO成员失败',
      error: error.message
    });
  }
});

// 获取所有DAO成员（管理员功能）
router.get('/admin/members', requireAuth, async (req, res) => {
  try {
    // TODO: 添加管理员权限验证
    const result = await daoMemberModel.getAllDAOMembers();
    res.json(result);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取DAO成员列表失败',
      error: error.message
    });
  }
});

// ===================== 统计API =====================

// 获取DAO治理统计数据
router.get('/stats', async (req, res) => {
  try {
    const proposalsResult = await proposalModel.getProposals();
    const membersResult = await daoMemberModel.getAllDAOMembers();

    const proposals = proposalsResult.data;
    const members = membersResult.data;

    const stats = {
      total_proposals: proposals.length,
      active_proposals: proposals.filter(p => p.status === 'voting').length,
      approved_proposals: proposals.filter(p => p.status === 'approved').length,
      total_dao_members: members.length,
      total_votes: proposals.reduce((sum, p) => sum + p.vote_stats.total, 0),
      this_week_proposals: proposals.filter(p => {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return new Date(p.created_at) > oneWeekAgo;
      }).length
    };

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

export default router; 