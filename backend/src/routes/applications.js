import express from 'express';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

/**
 * @swagger
 * /api/applications:
 *   get:
 *     summary: 获取申请列表
 *     tags: [申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 申请状态
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [sent, received]
 *         description: 申请类型（发出的/收到的）
 *     responses:
 *       200:
 *         description: 获取申请列表成功
 *       401:
 *         description: 未登录
 */
router.get('/', authenticate, async (req, res) => {
  res.status(200).json({
    success: true,
    message: '获取申请列表成功',
    data: {
      applications: []
    }
  });
});

/**
 * @swagger
 * /api/applications:
 *   post:
 *     summary: 提交申请
 *     tags: [申请]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectId
 *             properties:
 *               projectId:
 *                 type: integer
 *                 description: 项目ID
 *               message:
 *                 type: string
 *                 description: 申请消息
 *     responses:
 *       201:
 *         description: 申请提交成功
 *       401:
 *         description: 未登录
 */
router.post('/', authenticate, async (req, res) => {
  res.status(201).json({
    success: true,
    message: '申请提交成功',
    data: {
      application: {}
    }
  });
});

/**
 * @swagger
 * /api/applications/{id}:
 *   put:
 *     summary: 更新申请状态
 *     tags: [申请]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [accepted, rejected]
 *                 description: 申请状态
 *     responses:
 *       200:
 *         description: 申请状态更新成功
 *       401:
 *         description: 未登录
 *       403:
 *         description: 权限不足
 */
router.put('/:id', authenticate, async (req, res) => {
  res.status(200).json({
    success: true,
    message: '申请状态更新成功',
    data: {
      application: {}
    }
  });
});

export default router; 