import express from 'express';
import {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  verifyToken,
  logout,
  forgotPassword,
  resetPassword,
  uploadAvatar
} from '../controllers/authController.js';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: 用户注册
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *               - confirmPassword
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: 邮箱地址
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: 密码（至少8个字符，必须包含大写字母、小写字母、数字和特殊符号）
 *               confirmPassword:
 *                 type: string
 *                 description: 确认密码
 *     responses:
 *       201:
 *         description: 注册成功
 *       400:
 *         description: 请求数据验证失败
 */
router.post('/register', register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: 用户登录
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: 邮箱地址
 *               password:
 *                 type: string
 *                 description: 密码
 *     responses:
 *       200:
 *         description: 登录成功
 *       401:
 *         description: 邮箱或密码错误
 */
router.post('/login', login);

/**
 * @swagger
 * /api/auth/verify:
 *   post:
 *     summary: 验证访问令牌
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *             properties:
 *               token:
 *                 type: string
 *                 description: JWT访问令牌
 *     responses:
 *       200:
 *         description: Token验证成功
 *       401:
 *         description: Token无效或已过期
 */
router.post('/verify', verifyToken);

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: 获取当前用户信息
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取用户信息成功
 *       401:
 *         description: 未登录或令牌无效
 */
router.get('/profile', authenticate, getProfile);

/**
 * @swagger
 * /api/auth/profile:
 *   put:
 *     summary: 更新用户资料
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               bio:
 *                 type: string
 *                 description: 个人简介
 *               location:
 *                 type: string
 *                 description: 所在地区
 *               githubUrl:
 *                 type: string
 *                 format: uri
 *                 description: GitHub链接
 *               bilibiliUrl:
 *                 type: string
 *                 format: uri
 *                 description: B站链接
 *               portfolioUrl:
 *                 type: string
 *                 format: uri
 *                 description: 作品集链接
 *               avatarUrl:
 *                 type: string
 *                 format: uri
 *                 description: 头像链接
 *     responses:
 *       200:
 *         description: 资料更新成功
 *       401:
 *         description: 未登录或令牌无效
 */
router.put('/profile', authenticate, updateProfile);

/**
 * @swagger
 * /api/auth/upload-avatar:
 *   post:
 *     summary: 上传用户头像
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               avatar:
 *                 type: string
 *                 format: binary
 *                 description: 头像文件（支持JPG、PNG格式，不超过2MB）
 *     responses:
 *       200:
 *         description: 头像上传成功
 *       400:
 *         description: 文件格式不支持或文件过大
 *       401:
 *         description: 未登录或令牌无效
 */
router.post('/upload-avatar', authenticate, uploadAvatar);

/**
 * @swagger
 * /api/auth/password:
 *   put:
 *     summary: 修改密码
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *               - confirmNewPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: 当前密码
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *                 description: 新密码
 *               confirmNewPassword:
 *                 type: string
 *                 description: 确认新密码
 *     responses:
 *       200:
 *         description: 密码修改成功
 *       400:
 *         description: 当前密码错误或新密码验证失败
 *       401:
 *         description: 未登录或令牌无效
 */
router.put('/password', authenticate, changePassword);

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: 用户退出登录
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 退出登录成功
 *       401:
 *         description: 未登录或令牌无效
 */
router.post('/logout', authenticate, logout);

/**
 * @swagger
 * /api/auth/forgot-password:
 *   post:
 *     summary: 忘记密码
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: 注册邮箱地址
 *     responses:
 *       200:
 *         description: 密码重置邮件发送成功
 *       404:
 *         description: 该邮箱未注册
 *       429:
 *         description: 重置邮件发送过于频繁
 */
router.post('/forgot-password', forgotPassword);

/**
 * @swagger
 * /api/auth/reset-password:
 *   post:
 *     summary: 重置密码
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *             properties:
 *               token:
 *                 type: string
 *                 description: 重置令牌
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 description: 新密码（至少8个字符，必须包含大写字母、小写字母、数字和特殊符号）
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       400:
 *         description: 重置令牌无效或已过期
 *       404:
 *         description: 用户不存在
 */
router.post('/reset-password', resetPassword);

export default router; 