import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticate } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/investment/investor-application:
 *   post:
 *     summary: 提交投资人申请
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - contact
 *               - experience
 *             properties:
 *               name:
 *                 type: string
 *                 description: 申请人姓名
 *               contact:
 *                 type: string
 *                 description: 联系方式
 *               experience:
 *                 type: string
 *                 description: 投资经验
 *               budget:
 *                 type: string
 *                 description: 投资预算范围
 *               industries:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 感兴趣的行业
 *               description:
 *                 type: string
 *                 description: 申请说明
 *     responses:
 *       201:
 *         description: 申请提交成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/investor-application', authenticate, async (req, res) => {
  try {
    const { name, contact, experience, budget, industries, description } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!name || !contact || !experience) {
      return res.status(400).json({
        success: false,
        message: '姓名、联系方式和投资经验为必填字段'
      });
    }

    // 检查是否已经提交过申请
    const existingApplication = await prisma.investorApplication.findFirst({
      where: {
        userId: userId,
        status: { in: ['pending', 'approved'] }
      }
    });

    if (existingApplication) {
      return res.status(400).json({
        success: false,
        message: '您已经提交过投资人申请，请等待审核结果'
      });
    }

    // 创建申请记录
    const application = await prisma.investorApplication.create({
      data: {
        userId,
        name,
        contact,
        experience,
        budget: budget || null,
        industries: industries ? JSON.stringify(industries) : null,
        description: description || null,
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: '投资人申请提交成功',
      data: application
    });
  } catch (error) {
    console.error('提交投资人申请失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/investment/business-plan:
 *   post:
 *     summary: 提交创业计划
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - projectName
 *               - summary
 *               - industry
 *               - stage
 *               - contact
 *             properties:
 *               projectName:
 *                 type: string
 *                 description: 项目名称
 *               summary:
 *                 type: string
 *                 description: 项目简介
 *               industry:
 *                 type: string
 *                 description: 所属行业
 *               funding:
 *                 type: string
 *                 description: 融资需求
 *               stage:
 *                 type: string
 *                 description: 项目阶段
 *               contact:
 *                 type: string
 *                 description: 联系方式
 *               details:
 *                 type: string
 *                 description: 详细计划
 *     responses:
 *       201:
 *         description: 计划提交成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 */
router.post('/business-plan', authenticate, async (req, res) => {
  try {
    const { projectName, summary, industry, funding, stage, contact, details } = req.body;
    const userId = req.user.id;

    // 验证必填字段
    if (!projectName || !summary || !industry || !stage || !contact) {
      return res.status(400).json({
        success: false,
        message: '项目名称、项目简介、所属行业、项目阶段和联系方式为必填字段'
      });
    }

    // 创建申请记录
    const application = await prisma.businessPlanApplication.create({
      data: {
        userId,
        projectName,
        summary,
        industry,
        funding: funding || null,
        stage,
        contact,
        details: details || null,
        status: 'pending'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: '创业计划提交成功',
      data: application
    });
  } catch (error) {
    console.error('提交创业计划失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/investment/investor-applications:
 *   get:
 *     summary: 获取投资人申请列表（管理员）
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 申请状态筛选
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/investor-applications', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { status, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.investorApplication.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              createdAt: true
            }
          },
          reviewer: {
            select: {
              id: true,
              username: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: parseInt(limit)
      }),
      prisma.investorApplication.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        applications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取投资人申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/investment/business-plans:
 *   get:
 *     summary: 获取创业计划申请列表（管理员）
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 申请状态筛选
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/business-plans', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { status, page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};
    if (status) {
      where.status = status;
    }

    const [applications, total] = await Promise.all([
      prisma.businessPlanApplication.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              createdAt: true
            }
          },
          reviewer: {
            select: {
              id: true,
              username: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: parseInt(limit)
      }),
      prisma.businessPlanApplication.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        applications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取创业计划申请列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/investment/investor-applications/{id}/review:
 *   put:
 *     summary: 审核投资人申请
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [approved, rejected]
 *                 description: 审核结果
 *               reviewNote:
 *                 type: string
 *                 description: 审核备注
 *     responses:
 *       200:
 *         description: 审核成功
 */
router.put('/investor-applications/:id/review', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { id } = req.params;
    const { status, reviewNote } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的审核状态'
      });
    }

    const application = await prisma.investorApplication.update({
      where: { id: parseInt(id) },
      data: {
        status,
        reviewNote: reviewNote || null,
        reviewedBy: req.user.id,
        reviewedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: '审核完成',
      data: application
    });
  } catch (error) {
    console.error('审核投资人申请失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/investment/business-plans/{id}/review:
 *   put:
 *     summary: 审核创业计划申请
 *     tags: [Investment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 申请ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [approved, rejected]
 *                 description: 审核结果
 *               reviewNote:
 *                 type: string
 *                 description: 审核备注
 *     responses:
 *       200:
 *         description: 审核成功
 */
router.put('/business-plans/:id/review', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { id } = req.params;
    const { status, reviewNote } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的审核状态'
      });
    }

    const application = await prisma.businessPlanApplication.update({
      where: { id: parseInt(id) },
      data: {
        status,
        reviewNote: reviewNote || null,
        reviewedBy: req.user.id,
        reviewedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: '审核完成',
      data: application
    });
  } catch (error) {
    console.error('审核创业计划申请失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

export default router;
