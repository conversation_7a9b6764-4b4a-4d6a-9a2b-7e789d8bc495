import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { authenticate } from '../middleware/auth.js';
import logger from '../utils/logger.js';

const router = express.Router();
const prisma = new PrismaClient();

// 管理员登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 验证输入
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找管理员用户（这里简化处理，实际项目中应该有专门的admin表）
    const admin = await prisma.user.findFirst({
      where: {
        OR: [
          { email: username },
          { username: username }
        ],
        role: 'admin'
      }
    });

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: '管理员不存在'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, admin.passwordHash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '密码错误'
      });
    }

    // 生成JWT令牌
    const token = jwt.sign(
      { 
        userId: admin.id, 
        email: admin.email,
        role: admin.role 
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    );

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          role: admin.role
        },
        token
      }
    });

    logger.info(`管理员登录成功: ${admin.email}`);
  } catch (error) {
    logger.error('管理员登录失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 验证管理员身份
router.get('/verify', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          username: req.user.username,
          email: req.user.email,
          role: req.user.role
        }
      }
    });
  } catch (error) {
    logger.error('管理员身份验证失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 管理面板统计数据
router.get('/dashboard', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 获取统计数据
    const [
      totalUsers,
      totalProjects,
      pendingProjects,
      approvedProjects,
      rejectedProjects,
      totalApplications
    ] = await Promise.all([
      prisma.user.count(),
      prisma.project.count(),
      prisma.project.count({ where: { reviewStatus: 'pending' } }),
      prisma.project.count({ where: { reviewStatus: 'approved' } }),
      prisma.project.count({ where: { reviewStatus: 'rejected' } }),
      prisma.application.count()
    ]);

    const approvalRate = (approvedProjects + rejectedProjects) > 0 
      ? Math.round((approvedProjects / (approvedProjects + rejectedProjects)) * 100)
      : 0;

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers,
          todayNewUsers: 0, // TODO: 需要实现今日新增用户计算
          totalProjects,
          todayNewProjects: 0, // TODO: 需要实现今日新增项目计算
          pendingProjects,
          approvedProjects,
          rejectedProjects,
          approvalRate
        }
      }
    });
  } catch (error) {
    logger.error('获取管理面板数据失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取待审核项目
router.get('/review/pending', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, pageSize = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(pageSize);

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where: { reviewStatus: 'pending' },
        include: {
          creator: {
            select: { id: true, username: true, email: true }
          },
          projectRequirements: {
            include: { skill: true }
          }
        },
        skip,
        take: parseInt(pageSize),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.project.count({ where: { reviewStatus: 'pending' } })
    ]);

    // Transform projects to match frontend expectations
    const transformedProjects = projects.map(project => ({
      ...project,
      owner: {
        id: project.creator.id,
        name: project.creator.username,
        email: project.creator.email
      },
      requirements: project.projectRequirements.map(req => ({
        skillName: req.skill.name,
        level: req.level,
        isRequired: req.isRequired
      }))
    }));

    res.json({
      success: true,
      data: {
        projects: transformedProjects,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          pages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('获取待审核项目失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 审核项目
router.put('/review/:projectId', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { projectId } = req.params;
    const { status, feedback } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的审核状态'
      });
    }

    const project = await prisma.project.update({
      where: { id: parseInt(projectId) },
      data: {
        status,
        feedback,
        reviewedAt: new Date(),
        reviewedBy: req.user.id
      },
      include: {
        owner: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    res.json({
      success: true,
      message: '项目审核成功',
      data: project
    });

    logger.info(`项目审核完成: ${projectId}, 状态: ${status}, 审核人: ${req.user.email}`);
  } catch (error) {
    logger.error('项目审核失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 快速审核通过项目
router.post('/review/:projectId/approve', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { projectId } = req.params;

    const project = await prisma.project.update({
      where: { id: parseInt(projectId) },
      data: {
        reviewStatus: 'approved',
        status: 'recruiting',
        reviewedAt: new Date(),
        reviewerId: req.user.id
      },
      include: {
        creator: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    res.json({
      success: true,
      message: '项目审核通过',
      data: project
    });

    logger.info(`项目审核通过: ${projectId}, 审核人: ${req.user.email}`);
  } catch (error) {
    logger.error('项目审核通过失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 快速拒绝项目
router.post('/review/:projectId/reject', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { projectId } = req.params;
    const { reason } = req.body;

    if (!reason || !reason.trim()) {
      return res.status(400).json({
        success: false,
        message: '请提供拒绝原因'
      });
    }

    const project = await prisma.project.update({
      where: { id: parseInt(projectId) },
      data: {
        reviewStatus: 'rejected',
        status: 'rejected',
        reviewMessage: reason,
        reviewedAt: new Date(),
        reviewerId: req.user.id
      },
      include: {
        creator: {
          select: { id: true, username: true, email: true }
        }
      }
    });

    res.json({
      success: true,
      message: '项目已拒绝',
      data: project
    });

    logger.info(`项目拒绝: ${projectId}, 原因: ${reason}, 审核人: ${req.user.email}`);
  } catch (error) {
    logger.error('项目拒绝失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取所有项目
router.get('/projects', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, pageSize = 10, status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(pageSize);

    const where = status ? { status } : {};

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        include: {
          creator: {
            select: { id: true, username: true, email: true }
          },
          projectRequirements: {
            include: { skill: true }
          }
        },
        skip,
        take: parseInt(pageSize),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.project.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        projects,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          pages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('获取项目列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取所有用户
router.get('/users', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, pageSize = 10 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(pageSize);

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          avatarUrl: true,
          bio: true,
          githubUrl: true,
          location: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              projects: true,
              applications: true
            }
          }
        },
        skip,
        take: parseInt(pageSize),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count()
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          pages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// ===================== 精选项目管理 =====================

// 获取所有项目列表（后台管理用）
router.get('/projects/all', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, pageSize = 10, reviewStatus, projectStatus } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(pageSize);

    const where = {};
    if (reviewStatus && reviewStatus !== 'all') {
      where.reviewStatus = reviewStatus;
    }
    if (projectStatus && projectStatus !== 'all') {
      where.status = projectStatus;
    }

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        include: {
          creator: {
            select: { 
              id: true, 
              username: true, 
              email: true 
            }
          },
          teamMembers: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  email: true
                }
              }
            }
          }
        },
        skip,
        take: parseInt(pageSize),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.project.count({ where })
    ]);

    // 格式化项目数据以匹配前端需求
    const formattedProjects = projects.map(project => {
      // 计算团队规模：现有团队成员 + 发起人 = 总人数
      const teamSize = (project.teamMembers?.length || 0) + 1; // +1 为发起人
      
      // 模式类型/工作安排中文转换
      const getWorkArrangementText = (arrangement) => {
        const map = {
          'fullTime': '全职',
          'partTime': '兼职', 
          'contract': '合同工',
          'intern': '实习',
          'freemium': '免费模式',
          'remote': '远程工作',
          'onsite': '现场工作',
          'hybrid': '混合工作'
        };
        return map[arrangement] || arrangement;
      };
      
      return {
        id: project.id,
        title: project.title,
        summary: project.summary || '',
        category: project.category,
        status: project.status,
        reviewStatus: project.reviewStatus,
        featured: project.featured || false,
        budget: project.budgetRange || '面议',
        teamSize: teamSize, // 正确的团队规模
        workArrangement: getWorkArrangementText(project.workArrangement),
        workType: getWorkArrangementText(project.workType),
        recentProgress: project.recentProgress,
        currentStage: project.status === 'recruiting' ? '招募阶段' : 
                     project.status === 'development' ? '开发阶段' : 
                     project.status === 'testing' ? '测试阶段' : '未知阶段',
        createdAt: project.createdAt,
        owner: {
          id: project.creator.id,
          name: project.creator.username,
          email: project.creator.email
        }
      };
    });

    res.json({
      success: true,
      data: {
        projects: formattedProjects,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          pages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });
  } catch (error) {
    logger.error('获取项目列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 设置项目为精选
router.post('/projects/:id/feature', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    
    const project = await prisma.project.update({
      where: { 
        id: projectId,
        reviewStatus: 'approved' // 只有审核通过的项目才能设为精选
      },
      data: {
        featured: true
      }
    });

    res.json({
      success: true,
      message: '项目已设为精选',
      data: {
        projectId: project.id,
        featured: project.featured
      }
    });

    logger.info(`项目设为精选: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('设置精选项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在或未审核通过' : '设置失败'
    });
  }
});

// 取消项目精选
router.post('/projects/:id/unfeature', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        featured: false
      }
    });

    res.json({
      success: true,
      message: '已取消项目精选',
      data: {
        projectId: project.id,
        featured: project.featured
      }
    });

    logger.info(`取消项目精选: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('取消精选项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在' : '操作失败'
    });
  }
});

// 切换项目精选状态（统一接口）
router.post('/projects/:id/toggle-featured', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    const { featured } = req.body;
    
    const whereCondition = { id: projectId };
    // 只有审核通过的项目才能设为精选
    if (featured) {
      whereCondition.reviewStatus = 'approved';
    }
    
    const project = await prisma.project.update({
      where: whereCondition,
      data: {
        featured: !!featured
      }
    });

    res.json({
      success: true,
      message: featured ? '项目已设为精选' : '已取消项目精选',
      data: {
        projectId: project.id,
        featured: project.featured
      }
    });

    logger.info(`${featured ? '设置' : '取消'}项目精选: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('切换精选项目状态失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在或未审核通过' : '操作失败'
    });
  }
});

// 下架项目
router.post('/projects/:id/hide', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        status: 'hidden'
      }
    });

    res.json({
      success: true,
      message: '项目已下架',
      data: {
        projectId: project.id,
        status: project.status
      }
    });

    logger.info(`项目下架: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('下架项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在' : '下架失败'
    });
  }
});

// 恢复项目
router.post('/projects/:id/restore', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        status: 'recruiting'
      }
    });

    res.json({
      success: true,
      message: '项目已恢复',
      data: {
        projectId: project.id,
        status: project.status
      }
    });

    logger.info(`项目恢复: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('恢复项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在' : '恢复失败'
    });
  }
});

// 删除项目
router.delete('/projects/:id', authenticate, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const projectId = parseInt(req.params.id);
    
    await prisma.project.delete({
      where: { id: projectId }
    });

    res.json({
      success: true,
      message: '项目已删除'
    });

    logger.info(`项目删除: ${projectId}, 操作人: ${req.user.email}`);
  } catch (error) {
    logger.error('删除项目失败:', error);
    res.status(500).json({
      success: false,
      message: error.code === 'P2025' ? '项目不存在' : '删除失败'
    });
  }
});

/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: 获取用户列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: role
 *         schema:
 *           type: string
 *         description: 角色筛选
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 状态筛选
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/users', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, limit = 20, search, role, status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // 构建查询条件
    const where = {};

    if (search) {
      where.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (role) {
      where.role = role;
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          username: true,
          email: true,
          role: true,
          avatar: true,
          location: true,
          bio: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              projects: true,
              sentMessages: true,
              receivedMessages: true
            }
          },
          daoMembership: {
            select: {
              id: true,
              joinedAt: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: parseInt(limit)
      }),
      prisma.user.count({ where })
    ]);

    // 处理用户数据
    const processedUsers = users.map(user => ({
      ...user,
      isDaoMember: !!user.daoMembership,
      daoJoinedAt: user.daoMembership?.joinedAt || null,
      projectCount: user._count.projects,
      messageCount: user._count.sentMessages + user._count.receivedMessages
    }));

    res.json({
      success: true,
      data: {
        users: processedUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{id}:
 *   put:
 *     summary: 更新用户信息
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *                 description: 用户名
 *               email:
 *                 type: string
 *                 description: 邮箱
 *               role:
 *                 type: string
 *                 enum: [user, admin]
 *                 description: 角色
 *               location:
 *                 type: string
 *                 description: 位置
 *               bio:
 *                 type: string
 *                 description: 个人简介
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/users/:id', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { id } = req.params;
    const { username, email, role, location, bio } = req.body;

    // 验证邮箱唯一性
    if (email) {
      const existingUser = await prisma.user.findFirst({
        where: {
          email,
          id: { not: parseInt(id) }
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被其他用户使用'
        });
      }
    }

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        username: username || undefined,
        email: email || undefined,
        role: role || undefined,
        location: location || undefined,
        bio: bio || undefined
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatar: true,
        location: true,
        bio: true,
        createdAt: true,
        updatedAt: true
      }
    });

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/admin/dao-members:
 *   get:
 *     summary: 获取DAO成员列表
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/dao-members', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [members, total] = await Promise.all([
      prisma.daoMember.findMany({
        include: {
          user: {
            select: {
              id: true,
              username: true,
              email: true,
              avatar: true,
              location: true,
              createdAt: true
            }
          }
        },
        orderBy: {
          joinedAt: 'desc'
        },
        skip,
        take: parseInt(limit)
      }),
      prisma.daoMember.count()
    ]);

    res.json({
      success: true,
      data: {
        members,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });
  } catch (error) {
    console.error('获取DAO成员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/admin/dao-members/{userId}:
 *   post:
 *     summary: 添加DAO成员
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     responses:
 *       201:
 *         description: 添加成功
 */
router.post('/dao-members/:userId', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { userId } = req.params;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查是否已经是DAO成员
    const existingMember = await prisma.daoMember.findUnique({
      where: { userId: parseInt(userId) }
    });

    if (existingMember) {
      return res.status(400).json({
        success: false,
        message: '用户已经是DAO成员'
      });
    }

    // 添加DAO成员
    const member = await prisma.daoMember.create({
      data: {
        userId: parseInt(userId),
        joinedAt: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            email: true,
            avatar: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'DAO成员添加成功',
      data: member
    });
  } catch (error) {
    console.error('添加DAO成员失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/admin/dao-members/{userId}:
 *   delete:
 *     summary: 移除DAO成员
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 用户ID
 *     responses:
 *       200:
 *         description: 移除成功
 */
router.delete('/dao-members/:userId', authenticate, async (req, res) => {
  try {
    // 检查管理员权限
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    const { userId } = req.params;

    // 检查是否是DAO成员
    const member = await prisma.daoMember.findUnique({
      where: { userId: parseInt(userId) }
    });

    if (!member) {
      return res.status(404).json({
        success: false,
        message: '用户不是DAO成员'
      });
    }

    // 移除DAO成员
    await prisma.daoMember.delete({
      where: { userId: parseInt(userId) }
    });

    res.json({
      success: true,
      message: 'DAO成员移除成功'
    });
  } catch (error) {
    console.error('移除DAO成员失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

export default router;