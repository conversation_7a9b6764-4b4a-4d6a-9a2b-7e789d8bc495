import express from 'express';
import { authenticate } from '../middleware/auth.js';
import { PrismaClient } from '@prisma/client';

const router = express.Router();
const prisma = new PrismaClient();

/**
 * @swagger
 * /api/messages:
 *   get:
 *     summary: 获取消息列表
 *     tags: [消息]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [sent, received]
 *         description: 消息类型（发送的/接收的）
 *       - in: query
 *         name: isRead
 *         schema:
 *           type: boolean
 *         description: 是否已读
 *     responses:
 *       200:
 *         description: 获取消息列表成功
 *       401:
 *         description: 未登录
 */
router.get('/', authenticate, async (req, res) => {
  try {
    const { type, isRead } = req.query;
    const userId = req.user.id;

    const where = {};

    if (type === 'sent') {
      where.senderId = userId;
    } else if (type === 'received') {
      where.receiverId = userId;
    } else {
      // 默认获取接收的消息
      where.receiverId = userId;
    }

    if (isRead !== undefined) {
      where.isRead = isRead === 'true';
    }

    const messages = await prisma.message.findMany({
      where,
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            email: true,
            avatarUrl: true
          }
        },
        receiver: {
          select: {
            id: true,
            username: true,
            email: true,
            avatarUrl: true
          }
        },
        project: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      message: '获取消息列表成功',
      data: {
        messages,
        total: messages.length,
        unreadCount: messages.filter(m => !m.isRead && m.receiverId === userId).length
      }
    });
  } catch (error) {
    console.error('获取消息列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/messages/{id}:
 *   get:
 *     summary: 获取消息详情
 *     tags: [消息]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 消息ID
 *     responses:
 *       200:
 *         description: 获取消息详情成功
 *       401:
 *         description: 未登录
 *       404:
 *         description: 消息不存在
 */
router.get('/:id', authenticate, async (req, res) => {
  res.status(200).json({
    success: true,
    message: '获取消息详情成功',
    data: {
      message: {}
    }
  });
});

/**
 * @swagger
 * /api/messages:
 *   post:
 *     summary: 发送消息
 *     tags: [消息]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - receiverId
 *               - subject
 *               - content
 *             properties:
 *               receiverId:
 *                 type: integer
 *                 description: 接收者ID
 *               subject:
 *                 type: string
 *                 description: 消息主题
 *               content:
 *                 type: string
 *                 description: 消息内容
 *               projectId:
 *                 type: integer
 *                 description: 关联项目ID
 *               messageType:
 *                 type: string
 *                 enum: [invitation, discussion, system]
 *                 description: 消息类型
 *     responses:
 *       201:
 *         description: 消息发送成功
 *       401:
 *         description: 未登录
 */
router.post('/', authenticate, async (req, res) => {
  try {
    const { receiverId, subject, content, projectId, messageType = 'discussion' } = req.body;
    const senderId = req.user.id;

    // 验证必填字段
    if (!receiverId || !subject || !content) {
      return res.status(400).json({
        success: false,
        message: '接收者、主题和内容为必填字段'
      });
    }

    // 验证接收者是否存在
    const receiver = await prisma.user.findUnique({
      where: { id: parseInt(receiverId) }
    });

    if (!receiver) {
      return res.status(404).json({
        success: false,
        message: '接收者不存在'
      });
    }

    // 如果指定了项目ID，验证项目是否存在
    if (projectId) {
      const project = await prisma.project.findUnique({
        where: { id: parseInt(projectId) }
      });

      if (!project) {
        return res.status(404).json({
          success: false,
          message: '项目不存在'
        });
      }
    }

    // 创建消息
    const message = await prisma.message.create({
      data: {
        senderId,
        receiverId: parseInt(receiverId),
        subject,
        content,
        projectId: projectId ? parseInt(projectId) : null,
        messageType,
        isRead: false
      },
      include: {
        sender: {
          select: {
            id: true,
            username: true,
            email: true,
            avatarUrl: true
          }
        },
        receiver: {
          select: {
            id: true,
            username: true,
            email: true,
            avatarUrl: true
          }
        },
        project: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: '消息发送成功',
      data: {
        message
      }
    });
  } catch (error) {
    console.error('发送消息失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

/**
 * @swagger
 * /api/messages/{id}/read:
 *   put:
 *     summary: 标记消息为已读
 *     tags: [消息]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 消息ID
 *     responses:
 *       200:
 *         description: 消息已标记为已读
 *       401:
 *         description: 未登录
 *       404:
 *         description: 消息不存在
 */
router.put('/:id/read', authenticate, async (req, res) => {
  res.status(200).json({
    success: true,
    message: '消息已标记为已读'
  });
});

export default router; 