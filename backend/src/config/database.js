import { PrismaClient } from '@prisma/client';
import logger from '../utils/logger.js';

// 创建Prisma客户端实例
const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event', 
      level: 'error',
    },
    {
      emit: 'event',
      level: 'info',
    },
    {
      emit: 'event',
      level: 'warn',
    },
  ],
  errorFormat: 'pretty',
});

// 数据库日志事件监听
if (process.env.NODE_ENV === 'development') {
  prisma.$on('query', (e) => {
    logger.debug('数据库查询', {
      query: e.query,
      params: e.params,
      duration: `${e.duration}ms`,
      target: e.target
    });
  });
}

prisma.$on('error', (e) => {
  logger.error('数据库错误', {
    message: e.message,
    target: e.target
  });
});

prisma.$on('warn', (e) => {
  logger.warn('数据库警告', {
    message: e.message,
    target: e.target
  });
});

// 数据库连接测试
const connectDatabase = async () => {
  try {
    await prisma.$connect();
    logger.info('✅ 数据库连接成功');
    
    // 测试数据库连接
    await prisma.$queryRaw`SELECT 1`;
    logger.info('✅ 数据库查询测试成功');
    
  } catch (error) {
    logger.error('❌ 数据库连接失败:', error);
    process.exit(1);
  }
};

// 优雅关闭数据库连接
const disconnectDatabase = async () => {
  try {
    await prisma.$disconnect();
    logger.info('🔌 数据库连接已关闭');
  } catch (error) {
    logger.error('❌ 数据库断开连接失败:', error);
  }
};

// 监听进程退出事件
process.on('beforeExit', disconnectDatabase);
process.on('SIGINT', disconnectDatabase);
process.on('SIGTERM', disconnectDatabase);

export { prisma, connectDatabase, disconnectDatabase };
export default prisma; 