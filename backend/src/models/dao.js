// DAO治理数据模型

// 全局实例，确保数据持久化
let globalProposalInstance = null;

class ProposalModel {
  constructor() {
    // 如果全局实例存在，返回它
    if (globalProposalInstance) {
      return globalProposalInstance;
    }
    
    // 模拟数据库，实际项目中应使用真实数据库
    this.proposals = [
      {
        id: 'MUP0001',
        title: '优化项目自动分解引擎 - 让复杂项目更易协作',
        category: 'feature',
        content: '建议平台增加AI驱动的复杂项目智能分解功能，能够将500小时量级的项目自动拆解为适合多人协作的任务模块，提高协作效率，降低项目管理复杂度。具体包括：1. 智能任务分解算法 2. 依赖关系识别 3. 工作量评估 4. 团队技能匹配',
        evidence: '参考Github Copilot项目，大型项目自动分解可提升30%协作效率。现有用户调研显示，73%的用户希望有项目自动分解功能。',
        impact: 'high_value_high_difficulty',
        status: '投票中',
        creator_id: 1,
        created_at: '2024-07-15T10:00:00.000Z',
        updated_at: '2024-07-15T10:00:00.000Z',
        deadline: '2024-07-31T23:59:59.000Z',
        vote_stats: {
          support: 148,
          oppose: 24,
          abstain: 5,
          total: 177
        },
        comments_count: 62,
        views_count: 435
      },
      {
        id: 'MUP0002',
        title: '新增项目视频进度展示功能',
        category: 'feature',
        content: '建议增加短视频录制与上传功能，让开发者每周可上传1分钟的项目进度视频，更生动展示项目进展，提升项目透明度和团队凝聚力。',
        evidence: '视频展示比文字描述更直观，可提升50%的项目关注度。参考类似平台数据显示，有视频展示的项目成功率提升25%。',
        impact: 'medium_value_low_difficulty',
        status: '已通过',
        creator_id: 2,
        created_at: '2024-06-01T10:00:00.000Z',
        updated_at: '2024-06-15T15:30:00.000Z',
        deadline: '2024-06-15T23:59:59.000Z',
        vote_stats: {
          support: 89,
          oppose: 11,
          abstain: 3,
          total: 103
        },
        comments_count: 34,
        views_count: 267,
        implementation_progress: 30
      }
    ];

    this.votes = [];
    this.comments = [];
    
    // 设置全局实例
    globalProposalInstance = this;
  }

  // 获取提案列表（前端API - 只返回审核通过的提案）
  async getProposalsForFrontend(filters = {}) {
    // 只返回投票中、已通过、未通过的提案（即审核通过的提案）
    let filteredProposals = this.proposals.filter(p => 
      ['投票中', '已通过', '未通过'].includes(p.status)
    );

    // 按状态筛选
    if (filters.status && filters.status !== 'all') {
      filteredProposals = filteredProposals.filter(p => p.status === filters.status);
    }

    // 按分类筛选
    if (filters.category && filters.category !== 'all') {
      filteredProposals = filteredProposals.filter(p => p.category === filters.category);
    }

    // 排序
    if (filters.sort) {
      switch (filters.sort) {
        case 'latest':
          filteredProposals.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          break;
        case 'deadline':
          filteredProposals.sort((a, b) => new Date(a.deadline) - new Date(b.deadline));
          break;
        case 'votes':
          filteredProposals.sort((a, b) => b.vote_stats.total - a.vote_stats.total);
          break;
        case 'comments':
          filteredProposals.sort((a, b) => b.comments_count - a.comments_count);
          break;
      }
    }

    return {
      success: true,
      data: filteredProposals,
      pagination: {
        total: filteredProposals.length,
        page: filters.page || 1,
        limit: filters.limit || 10
      }
    };
  }

  // 获取提案列表（管理员API - 返回所有提案）
  async getProposals(filters = {}) {
    let filteredProposals = [...this.proposals];

    // 按状态筛选
    if (filters.status && filters.status !== 'all') {
      filteredProposals = filteredProposals.filter(p => p.status === filters.status);
    }

    // 按分类筛选
    if (filters.category && filters.category !== 'all') {
      filteredProposals = filteredProposals.filter(p => p.category === filters.category);
    }

    // 排序
    if (filters.sort) {
      switch (filters.sort) {
        case 'latest':
          filteredProposals.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          break;
        case 'deadline':
          filteredProposals.sort((a, b) => new Date(a.deadline) - new Date(b.deadline));
          break;
        case 'votes':
          filteredProposals.sort((a, b) => b.vote_stats.total - a.vote_stats.total);
          break;
        case 'comments':
          filteredProposals.sort((a, b) => b.comments_count - a.comments_count);
          break;
      }
    }

    return {
      success: true,
      data: filteredProposals,
      pagination: {
        total: filteredProposals.length,
        page: filters.page || 1,
        limit: filters.limit || 10
      }
    };
  }

  // 获取单个提案详情
  async getProposal(id) {
    const proposal = this.proposals.find(p => p.id === id);
    if (!proposal) {
      return { success: false, message: '提案不存在' };
    }

    // 增加查看次数
    proposal.views_count += 1;

    return { success: true, data: proposal };
  }

  // 创建提案
  async createProposal(data, userId) {
    // 生成MUP编号 (Match User Proposal)
    // 找到最大的现有编号并递增
    const existingNumbers = this.proposals
      .map(p => p.id)
      .filter(id => id.startsWith('MUP'))
      .map(id => parseInt(id.replace('MUP', '')))
      .filter(num => !isNaN(num));
    
    const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
    const proposalId = 'MUP' + String(nextNumber).padStart(4, '0');
    
    const newProposal = {
      id: proposalId,
      title: data.title,
      category: data.category,
      content: data.content,
      evidence: data.evidence || '',
      impact: data.impact || 'other',
      status: '审核中', // 新提案默认为审核中状态
      creator_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      deadline: null, // 审核中时无截止时间
      vote_stats: {
        support: 0,
        oppose: 0,
        abstain: 0,
        total: 0
      },
      comments_count: 0,
      views_count: 0
    };

    this.proposals.unshift(newProposal);

    return {
      success: true,
      data: newProposal,
      message: '提案创建成功，等待管理员审核'
    };
  }

  // 投票
  async vote(proposalId, userId, voteType, userDaoStatus) {
    // 检查DAO成员身份
    if (!userDaoStatus.isDaoMember) {
      return { success: false, message: '只有DAO成员才能投票' };
    }

    const proposal = this.proposals.find(p => p.id === proposalId);
    if (!proposal) {
      return { success: false, message: '提案不存在' };
    }

    if (proposal.status !== '投票中') {
      return { success: false, message: '该提案当前不在投票状态' };
    }

    // 检查是否已投票
    const existingVote = this.votes.find(v => v.proposal_id === proposalId && v.user_id === userId);
    
    if (existingVote) {
      // 更新投票
      proposal.vote_stats[existingVote.vote_type] -= 1;
      proposal.vote_stats[voteType] += 1;
      existingVote.vote_type = voteType;
      existingVote.updated_at = new Date().toISOString();
    } else {
      // 新投票
      const vote = {
        id: this.votes.length + 1,
        proposal_id: proposalId,
        user_id: userId,
        vote_type: voteType,
        voting_power: 1, // 统一权重为1
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      this.votes.push(vote);
      proposal.vote_stats[voteType] += 1;
      proposal.vote_stats.total += 1;
    }

    // 检查是否需要自动结束投票
    await this.checkVotingResult(proposalId);

    return {
      success: true,
      data: proposal.vote_stats,
      message: '投票成功'
    };
  }

  // 检查投票结果并自动判定
  async checkVotingResult(proposalId) {
    const proposal = this.proposals.find(p => p.id === proposalId);
    if (!proposal || proposal.status !== '投票中') {
      return;
    }

    // 获取DAO成员总数
    const daoMemberModel = new DAOMemberModel();
    const membersResult = await daoMemberModel.getAllDAOMembers();
    const totalMembers = membersResult.data.length;

    // 投票规则：投票总人数超过DAO所有成员的五分之一，并且赞成票大于反对票
    const minVotersRequired = Math.ceil(totalMembers / 5); // 五分之一成员
    const totalVotes = proposal.vote_stats.total;
    const supportVotes = proposal.vote_stats.support;
    const opposeVotes = proposal.vote_stats.oppose;

    // 检查投票是否达到最低参与要求
    if (totalVotes >= minVotersRequired) {
      // 检查是否通过
      if (supportVotes > opposeVotes) {
        proposal.status = '已通过';
        proposal.deadline = null;
        proposal.updated_at = new Date().toISOString();
      } else {
        proposal.status = '未通过';
        proposal.deadline = null;
        proposal.updated_at = new Date().toISOString();
      }
    }

    // 检查投票期限，如果超过deadline自动标记结果
    if (proposal.deadline && new Date() >= new Date(proposal.deadline)) {
      if (totalVotes < minVotersRequired || supportVotes <= opposeVotes) {
        proposal.status = '未通过';
        proposal.deadline = null;
        proposal.updated_at = new Date().toISOString();
        console.log(`提案 ${proposal.proposalNumber || proposal.id} 投票期限已到，自动标记为未通过`);
      } else {
        proposal.status = '已通过';
        proposal.deadline = null;
        proposal.updated_at = new Date().toISOString();
        console.log(`提案 ${proposal.proposalNumber || proposal.id} 投票期限已到，自动标记为已通过`);
      }
    }
  }

  // 定时检查所有投票中的提案是否需要自动结束
  async checkAllVotingDeadlines() {
    const votingProposals = this.proposals.filter(p => p.status === '投票中' && p.deadline);
    
    for (const proposal of votingProposals) {
      await this.checkVotingResult(proposal.id);
    }
  }

  // 添加评论
  async addComment(proposalId, userId, content, parentId = null) {
    const proposal = this.proposals.find(p => p.id === proposalId);
    if (!proposal) {
      return { success: false, message: '提案不存在' };
    }

    const comment = {
      id: this.comments.length + 1,
      proposal_id: proposalId,
      user_id: userId,
      parent_id: parentId,
      content: content,
      likes_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    this.comments.push(comment);
    proposal.comments_count += 1;

    return {
      success: true,
      data: comment,
      message: '评论添加成功'
    };
  }

  // 获取提案评论
  async getComments(proposalId) {
    const comments = this.comments
      .filter(c => c.proposal_id === proposalId)
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

    return {
      success: true,
      data: comments
    };
  }

  // 更新提案状态（管理员功能）
  async updateProposalStatus(proposalId, action, adminId) {
    const proposal = this.proposals.find(p => p.id === proposalId);
    if (!proposal) {
      return { success: false, message: '提案不存在' };
    }

    // 只有审核中的提案可以操作
    if (proposal.status !== '审核中') {
      return { success: false, message: '只有审核中的提案可以进行审核操作' };
    }

    if (action === '审核通过') {
      // 审核通过时生成提案编号并自动流转到投票中
      if (!proposal.proposalNumber) {
        // 生成MUP编号 (Match User Proposal)
        const existingNumbers = this.proposals
          .filter(p => p.proposalNumber && p.proposalNumber.startsWith('MUP'))
          .map(p => parseInt(p.proposalNumber.replace('MUP', '')))
          .filter(num => !isNaN(num));
        
        const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
        proposal.proposalNumber = 'MUP' + String(nextNumber).padStart(4, '0');
      }
      
      proposal.status = '投票中';
      // 设置15天倒计时，从当天开始计算，第15天的00:00分截止
      const startDate = new Date();
      const deadlineDate = new Date(startDate);
      deadlineDate.setDate(deadlineDate.getDate() + 15);
      deadlineDate.setHours(0, 0, 0, 0); // 设置为00:00:00
      proposal.deadline = deadlineDate.toISOString();
      proposal.updated_at = new Date().toISOString();
      
      return {
        success: true,
        data: proposal,
        message: `提案审核通过，编号：${proposal.proposalNumber}，已自动进入投票阶段，投票截止时间：${deadlineDate.toLocaleDateString('zh-CN')}`
      };
    } else if (action === '审核拒绝') {
      // 审核拒绝
      proposal.status = '审核失败';
      proposal.deadline = null;
      proposal.updated_at = new Date().toISOString();
      
      return {
        success: true,
        data: proposal,
        message: '提案已被拒绝'
      };
    } else {
      return { success: false, message: '无效的操作类型，只能选择审核通过或审核拒绝' };
    }
  }

  // 生成测试数据
  static async generateTestData() {
    // 获取现有实例或创建新的
    const instance = new ProposalModel()
    
    // 清空现有测试数据
    instance.proposals = instance.proposals.filter(p => p.creator_id !== 1)
    
    const testProposals = [
      {
        title: '引入AI代码评审助手功能',
        category: 'feature',
        content: '为了提高代码质量和开发效率，建议在平台集成AI代码评审功能，自动识别潜在问题、优化建议和最佳实践推荐。该功能将基于大语言模型，能够检测常见的代码问题，如内存泄漏、性能瓶颈、安全漏洞等，并提供具体的修改建议。同时支持多种编程语言，包括JavaScript、Python、Java、Go等主流技术栈。',
        evidence: '根据GitHub Copilot的使用数据显示，AI辅助编程可以提高开发效率30%以上。同时，静态代码分析工具如SonarQube已被广泛应用于企业级项目中，证明了自动化代码评审的价值。',
        impact: 'high_value_low_difficulty',
        status: '投票中',
        creator_id: 1,
        deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        title: '优化项目匹配算法提升成功率',
        category: 'technology',
        content: '当前匹配算法存在技能匹配不够精准的问题，建议引入多维度评估体系，包括技术栈熟练度、项目经验、协作偏好、时区匹配等因素。通过机器学习算法分析历史成功项目的特征，建立更精准的匹配模型。同时引入用户反馈机制，持续优化算法效果。',
        evidence: '目前项目匹配成功率约为65%，通过引入多维度评估和机器学习优化，预计可提升到85%以上。参考LinkedIn的人才匹配算法和GitHub的项目推荐系统经验。',
        impact: 'high_value_high_difficulty',
        status: '已通过',
        creator_id: 1,
        deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        title: '建立资源交换市场标准化评估体系',
        category: 'business',
        content: '为解决资源交换市场的价值评估难题，建议平台建立一套时间/技能/价值换算标准体系，提供第三方价值评估服务。制定统一的技能等级标准，建立时间价值计算模型，引入信用评价机制，确保交换的公平性和透明度。',
        evidence: '参考时间银行（Time Banking）的成功案例，以及技能共享平台如Upwork的定价机制。通过标准化评估可以降低交易摩擦成本30%以上。',
        impact: 'medium_value_low_difficulty',
        status: '审核中',
        creator_id: 1,
        deadline: null
      },
      {
        title: '实施去中心化身份验证系统',
        category: 'technology',
        content: '基于区块链技术实施DID（去中心化身份）系统，让用户完全控制自己的身份数据和隐私。支持多种认证方式，包括技能证书、项目经历、教育背景等，并通过智能合约确保数据的真实性和不可篡改性。',
        evidence: '微软、IBM等大厂已开始布局DID技术。Web3领域对身份主权的需求日益增长，去中心化身份将成为未来趋势。',
        impact: 'high_value_high_difficulty',
        status: '审核失败',
        creator_id: 1,
        deadline: null
      },
      {
        title: '增加项目进度实时协作功能',
        category: 'feature',
        content: '在项目管理模块中增加实时协作功能，包括在线白板、视频会议集成、屏幕共享、代码实时编辑等。支持多人同时编辑文档，实时同步项目进度，提供团队沟通的一站式解决方案。',
        evidence: 'Figma、Notion等协作工具的成功证明了实时协作的市场需求。疫情后远程工作成为趋势，协作工具需求增长200%以上。',
        impact: 'high_value_low_difficulty',
        status: '已通过',
        creator_id: 1,
        deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        title: 'DAO治理代币经济模型设计',
        category: 'community',
        content: '设计平台治理代币经济模型，通过代币激励用户参与社区建设。制定代币发行机制、分配规则、投票权重计算方法，建立可持续的社区自治体系。包括质押挖矿、流动性挖矿、治理挖矿等多种激励机制。',
        evidence: 'Compound、Uniswap等DeFi项目的治理代币模型已被验证有效。代币激励可以显著提高社区参与度和平台活跃度。',
        impact: 'high_value_high_difficulty',
        status: '未通过',
        creator_id: 1,
        deadline: null
      }
    ]

    const createdProposals = []
    
    for (const proposalData of testProposals) {
      try {
        const result = await instance.createProposal(proposalData, proposalData.creator_id)
        const proposal = result.data
        
        // 更新状态为指定状态（保留原始状态设置逻辑）
        proposal.status = proposalData.status
        
        // 如果是投票中或已通过状态，需要确保有合理的投票数据和编号
        if (proposal.status === '投票中' || proposal.status === '已通过') {
          // 为投票中和已通过的提案生成编号
          if (!proposal.proposalNumber) {
            const existingNumbers = instance.proposals
              .filter(p => p.proposalNumber && p.proposalNumber.startsWith('MUP'))
              .map(p => parseInt(p.proposalNumber.replace('MUP', '')))
              .filter(num => !isNaN(num));
            
            const nextNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) + 1 : 1;
            proposal.proposalNumber = 'MUP' + String(nextNumber).padStart(4, '0');
          }
          
          // 为投票中的提案设置15天倒计时
          if (proposal.status === '投票中') {
            const startDate = new Date();
            const deadlineDate = new Date(startDate);
            deadlineDate.setDate(deadlineDate.getDate() + 15);
            deadlineDate.setHours(0, 0, 0, 0);
            proposal.deadline = deadlineDate.toISOString();
          }
          
          // 为每个提案生成一些随机投票数据
          const supportVotes = Math.floor(Math.random() * 150) + 20
          const opposeVotes = Math.floor(Math.random() * 50) + 5
          const abstainVotes = Math.floor(Math.random() * 30) + 2
          
          proposal.vote_stats = {
            support: supportVotes,
            oppose: opposeVotes,
            abstain: abstainVotes,
            total: supportVotes + opposeVotes + abstainVotes
          }
          
          console.log(`提案 ${proposal.proposalNumber} 投票数据: 支持${supportVotes}, 反对${opposeVotes}, 弃权${abstainVotes}`)
        }
        
        // 添加一些随机的浏览量和评论数
        proposal.views_count = Math.floor(Math.random() * 500) + 50
        proposal.comments_count = Math.floor(Math.random() * 30) + 3
        
        createdProposals.push(proposal)
        
      } catch (error) {
        console.error('创建测试提案失败:', error)
      }
    }
    
    return createdProposals
  }

  // 清空测试数据
  static async clearTestData() {
    try {
      // 使用内存数组，直接清理
      const instance = new ProposalModel()
      instance.proposals = instance.proposals.filter(p => p.creator_id !== 1)
      instance.votes = instance.votes.filter(v => v.user_id !== 1)
      instance.comments = instance.comments.filter(c => c.author_id !== 1)
      console.log('测试数据已清空')
    } catch (error) {
      console.error('清空测试数据失败:', error)
      throw error
    }
  }
}

// 用户DAO成员状态管理
class DAOMemberModel {
  constructor() {
    this.daoMembers = [
      {
        user_id: 1,
        membership_type: 'dao_member',
        joined_at: '2024-01-01T00:00:00.000Z',
        voting_power: 1,
        status: 'active'
      }
    ];
  }

  // 获取用户DAO状态
  async getUserDAOStatus(userId) {
    const member = this.daoMembers.find(m => m.user_id === userId);
    
    return {
      success: true,
      data: {
        isDaoMember: !!member,
        membershipType: member?.membership_type || 'regular',
        votingPower: member?.voting_power || 0,
        joinedAt: member?.joined_at || null,
        status: member?.status || 'inactive'
      }
    };
  }

  // 添加DAO成员
  async addDAOMember(userId, membershipType = 'dao_member') {
    const existingMember = this.daoMembers.find(m => m.user_id === userId);
    
    if (existingMember) {
      return { success: false, message: '用户已是DAO成员' };
    }

    const newMember = {
      user_id: userId,
      membership_type: membershipType,
      joined_at: new Date().toISOString(),
      voting_power: 1,
      status: 'active'
    };

    this.daoMembers.push(newMember);

    return {
      success: true,
      data: newMember,
      message: 'DAO成员添加成功'
    };
  }

  // 移除DAO成员
  async removeDAOMember(userId) {
    const memberIndex = this.daoMembers.findIndex(m => m.user_id === userId);
    
    if (memberIndex === -1) {
      return { success: false, message: '用户不是DAO成员' };
    }

    this.daoMembers.splice(memberIndex, 1);

    return {
      success: true,
      message: 'DAO成员移除成功'
    };
  }

  // 获取所有DAO成员
  async getAllDAOMembers() {
    return {
      success: true,
      data: this.daoMembers
    };
  }
}

export { ProposalModel, DAOMemberModel }; 