import { asyncHandler } from '../middleware/errorHandler.js';
import * as authService from '../services/authService.js';
import { validateRegistration, validateLogin, validatePasswordChange } from '../utils/validators.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// @desc    用户注册
// @route   POST /api/auth/register
// @access  Public
export const register = asyncHandler(async (req, res) => {
  // 验证请求数据
  const { error, value } = validateRegistration(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请求数据验证失败',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => detail.message)
      }
    });
  }

  // 调用注册服务
  const result = await authService.registerUser(value);

  req.logger.info('用户注册成功', {
    userId: result.user.id,
    username: result.user.username,
    email: result.user.email
  });

  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      user: result.user,
      token: result.token
    }
  });
});

// @desc    用户登录
// @route   POST /api/auth/login
// @access  Public
export const login = asyncHandler(async (req, res) => {
  // 验证请求数据
  const { error, value } = validateLogin(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请求数据验证失败',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => detail.message)
      }
    });
  }

  // 调用登录服务
  const result = await authService.loginUser(value);

  req.logger.info('用户登录成功', {
    userId: result.user.id,
    username: result.user.username,
    email: result.user.email
  });

  res.status(200).json({
    success: true,
    message: '登录成功',
    data: {
      user: result.user,
      token: result.token
    }
  });
});

// @desc    获取当前用户信息
// @route   GET /api/auth/profile
// @access  Private
export const getProfile = asyncHandler(async (req, res) => {
  const user = await authService.getCurrentUser(req.user.id);

  res.status(200).json({
    success: true,
    message: '获取用户信息成功',
    data: { user }
  });
});

// @desc    更新用户资料
// @route   PUT /api/auth/profile
// @access  Private
export const updateProfile = asyncHandler(async (req, res) => {
  const allowedFields = [
    'username',
    'bio',
    'location',
    'githubUrl',
    'bilibiliUrl',
    'portfolioUrl',
    'avatarUrl',
    // 个人信息字段
    'gender',
    'phone',
    'wechat',
    'profession',
    // 工作偏好字段
    'workType',
    'workTime',
    'cooperationMode'
  ];

  // 过滤允许更新的字段
  const updateData = {};
  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      updateData[field] = req.body[field];
    }
  });

  const user = await authService.updateUserProfile(req.user.id, updateData);

  req.logger.info('用户资料更新成功', {
    userId: req.user.id,
    updatedFields: Object.keys(updateData)
  });

  res.status(200).json({
    success: true,
    message: '资料更新成功',
    data: { user }
  });
});

// @desc    修改密码
// @route   PUT /api/auth/password
// @access  Private
export const changePassword = asyncHandler(async (req, res) => {
  // 验证请求数据
  const { error, value } = validatePasswordChange(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请求数据验证失败',
        code: 'VALIDATION_ERROR',
        details: error.details.map(detail => detail.message)
      }
    });
  }

  const result = await authService.changePassword(req.user.id, value);

  req.logger.info('用户密码修改成功', {
    userId: req.user.id
  });

  res.status(200).json({
    success: true,
    message: result.message
  });
});

// @desc    验证token
// @route   POST /api/auth/verify
// @access  Public
export const verifyToken = asyncHandler(async (req, res) => {
  const { token } = req.body;

  if (!token) {
    return res.status(400).json({
      success: false,
      error: {
        message: '未提供token',
        code: 'NO_TOKEN'
      }
    });
  }

  const user = await authService.verifyToken(token);

  res.status(200).json({
    success: true,
    message: 'Token验证成功',
    data: { user }
  });
});

// @desc    用户退出登录
// @route   POST /api/auth/logout
// @access  Private
export const logout = asyncHandler(async (req, res) => {
  // 目前使用JWT无状态认证，前端删除token即可
  // 后续可以添加token黑名单机制

  req.logger.info('用户退出登录', {
    userId: req.user.id
  });

  res.status(200).json({
    success: true,
    message: '退出登录成功'
  });
});

// @desc    忘记密码
// @route   POST /api/auth/forgot-password
// @access  Public
export const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请提供邮箱地址',
        code: 'EMAIL_REQUIRED'
      }
    });
  }

  // 调用忘记密码服务
  const result = await authService.forgotPassword(email);

  req.logger.info('密码重置邮件发送成功', {
    email: email
  });

  res.status(200).json({
    success: true,
    message: result.message
  });
});

// @desc    重置密码
// @route   POST /api/auth/reset-password
// @access  Public
export const resetPassword = asyncHandler(async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请提供重置令牌和新密码',
        code: 'MISSING_REQUIRED_FIELDS'
      }
    });
  }

  // 调用重置密码服务
  const result = await authService.resetPassword({ token, password });

  req.logger.info('密码重置成功', {
    token: token.substring(0, 10) + '...' // 只记录令牌前10个字符
  });

  res.status(200).json({
    success: true,
    message: result.message
  });
});

// 配置multer用于头像上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    // 确保上传目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'image-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req, file, cb) => {
  // 只允许图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB
  },
  fileFilter: fileFilter
});

// @desc    上传头像
// @route   POST /api/auth/upload-avatar
// @access  Private
export const uploadAvatar = asyncHandler(async (req, res) => {
  // 使用multer中间件处理单文件上传
  upload.single('avatar')(req, res, async (err) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: {
          message: err.message || '文件上传失败',
          code: 'UPLOAD_ERROR'
        }
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: {
          message: '请选择要上传的文件',
          code: 'NO_FILE'
        }
      });
    }

    try {
      // 构建文件URL
      const avatarUrl = `/uploads/${req.file.filename}`;
      
      // 更新用户头像
      const user = await authService.updateUserProfile(req.user.id, { avatarUrl });

      req.logger.info('用户头像上传成功', {
        userId: req.user.id,
        filename: req.file.filename,
        avatarUrl: avatarUrl
      });

      res.status(200).json({
        success: true,
        message: '头像上传成功',
        data: {
          avatarUrl: avatarUrl,
          user: user
        }
      });
    } catch (error) {
      // 如果数据库更新失败，删除已上传的文件
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }
      throw error;
    }
  });
}); 