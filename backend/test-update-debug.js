import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000/api';

// 测试更新用户资料
async function testUpdateProfile() {
  console.log('🔐 先登录获取token...');
  
  // 登录获取token
  const loginResponse = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    })
  });
  
  const loginData = await loginResponse.json();
  
  if (!loginData.success) {
    console.log('❌ 登录失败:', loginData);
    return;
  }
  
  const token = loginData.data.token;
  console.log('✅ 登录成功');
  
  console.log('\n💾 测试更新用户资料...');
  
  const updateData = {
    username: '测试用户更新',
    bio: '这是更新后的个人简介',
    location: '北京',
    gender: 'male',
    phone: '13888888888',
    wechat: 'testwechat123',
    qq: '123456789',
    profession: '前端开发工程师',
    experience: '3-5',
    expectedSalary: '15k-20k',
    skills: ['JavaScript', 'Vue.js', 'React', 'Node.js'],
    workType: 'remote',
    workTime: 'fulltime'
  };
  
  console.log('发送的数据:', JSON.stringify(updateData, null, 2));
  
  const response = await fetch(`${API_BASE}/auth/profile`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  });
  
  console.log('响应状态码:', response.status);
  console.log('响应头:', response.headers.raw());
  
  const responseText = await response.text();
  console.log('响应原始内容:', responseText);
  
  try {
    const data = JSON.parse(responseText);
    console.log('解析后的响应:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.log('❌ JSON解析失败:', error.message);
    console.log('响应内容前100个字符:', responseText.substring(0, 100));
  }
}

testUpdateProfile();