import express from 'express';
import cors from 'cors';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import Jo<PERSON> from 'joi';
import multer from 'multer';

// 导入邮件验证服务
import { sendEmailVerificationCode, verifyEmailCode, verifyEmailCodeForReset } from './src/services/emailVerificationService.js';

// 导入路由
import projectRoutes from './src/routes/projects.js';
import adminRoutes from './src/routes/admin.js';
import userRoutes from './src/routes/users.js';
import userCurrentRoutes from './src/routes/user.js';
import { authenticate, optionalAuth } from './src/middleware/auth.js';

// 加载环境变量
dotenv.config();

// 初始化Prisma客户端
const prisma = new PrismaClient();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 8000;

// 基础中间件
app.use(cors({
  origin: [
    'http://localhost:3000', 'http://127.0.0.1:3000',
    'http://localhost:8080', 'http://127.0.0.1:8080', // 后台管理界面
    'http://localhost:5173', 'http://localhost:5174', 'http://localhost:5175', 'http://localhost:5176', 'http://localhost:5177', 
    'http://127.0.0.1:5173', 'http://127.0.0.1:5174', 'http://127.0.0.1:5175', 'http://127.0.0.1:5176', 'http://127.0.0.1:5177'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID']
}));
app.use(express.json());

// 创建uploads目录
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // 生成唯一文件名: 时间戳-随机数.扩展名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `image-${uniqueSuffix}${ext}`);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  },
  fileFilter: function (req, file, cb) {
    // 检查文件类型
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'), false);
    }
  }
});

// 静态文件服务 - 提供上传的图片访问
app.use('/uploads', express.static(uploadsDir));

// 使用路由
app.use('/api/projects', projectRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/users', userRoutes);
app.use('/api/user', userCurrentRoutes);

// 不再使用示例数据，全部使用数据库
console.log('系统已配置为仅使用数据库数据');

// 内存数据库模拟 - 新的测试数据，包含所有25个字段
let nextApplicationId = 5;
let nextProjectId = 100; // 用于旧的模拟路由

// 工具函数
function getCategoryName(category) {
  const categoryMap = {
    'health': '健康生活',
    'web': 'Web开发',
    'mobile': '移动应用',
    'ai': 'AI/机器学习',
    'blockchain': '区块链',
    'ecommerce': '电商平台',
    'enterprise': '企业软件',
    'game': '游戏开发',
    'data': '数据分析',
    'education': '教育培训',
    'social': '社交网络',
    'fintech': '金融科技'
  };
  return categoryMap[category] || category;
}

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'DevMatch API 运行正常',
    timestamp: new Date().toISOString(),
    environment: 'development',
    version: '1.0.0'
  });
});

// 图片上传接口
app.post('/api/upload/image', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '没有上传文件'
      });
    }

    // 构建文件访问URL
    const fileUrl = `/uploads/${req.file.filename}`;
    
    // 返回上传结果
    res.json({
      success: true,
      message: '图片上传成功',
      data: {
        id: req.file.filename, // 使用文件名作为ID
        name: req.file.originalname,
        url: fileUrl,
        filename: req.file.filename,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('图片上传失败:', error);
    res.status(500).json({
      success: false,
      message: '图片上传失败: ' + error.message
    });
  }
});

// 删除图片接口
app.delete('/api/upload/image/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(uploadsDir, filename);
    
    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      res.json({
        success: true,
        message: '图片删除成功'
      });
    } else {
      res.status(404).json({
        success: false,
        message: '图片不存在'
      });
    }
  } catch (error) {
    console.error('图片删除失败:', error);
    res.status(500).json({
      success: false,
      message: '图片删除失败: ' + error.message
    });
  }
});

// 发送邮箱验证码
app.post('/api/auth/send-verification-code', async (req, res) => {
  try {
    const { email, type = 'registration' } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: '邮箱地址是必需的'
      });
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: '请输入有效的邮箱地址'
      });
    }
    
    // 注册类型时检查邮箱是否已存在
    if (type === 'registration') {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });
      
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: '该邮箱已被注册'
        });
      }
    }
    
    // 发送验证码
    const result = await sendEmailVerificationCode(email, type);
    
    res.json({
      success: true,
      message: result.message
    });
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '发送验证码失败'
    });
  }
});

// 验证邮箱验证码
app.post('/api/auth/verify-code', async (req, res) => {
  try {
    const { email, code, type = 'registration' } = req.body;
    
    if (!email || !code) {
      return res.status(400).json({
        success: false,
        message: '邮箱地址和验证码是必需的'
      });
    }
    
    // 根据类型选择验证函数
    let result;
    if (type === 'password_reset') {
      // 密码重置验证码校验，不标记为已使用
      result = await verifyEmailCodeForReset(email, code, type);
    } else {
      // 其他类型的验证码校验，标记为已使用
      result = await verifyEmailCode(email, code, type);
    }
    
    res.json({
      success: true,
      message: result.message
    });
    
  } catch (error) {
    console.error('验证码验证失败:', error);
    res.status(400).json({
      success: false,
      message: error.message || '验证码验证失败'
    });
  }
});

// 登录接口
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // 验证输入数据
    const schema = Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().min(8).required()
    });
    
    const { error } = schema.validate({ email, password });
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email }
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }
    
    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
    
    // 返回用户信息和token
    res.json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          avatar: user.avatarUrl,
          bio: user.bio,
          location: user.location,
          githubUrl: user.githubUrl,
          portfolioUrl: user.portfolioUrl
        }
      },
      message: '登录成功'
    });
    
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后再试'
    });
  }
});

// 注册接口
app.post('/api/auth/register', async (req, res) => {
  try {
    const { username, email, password, verificationCode } = req.body;
    
    // 验证输入数据
    const schema = Joi.object({
      username: Joi.string().min(3).max(30).optional(),
      email: Joi.string().email().required(),
      password: Joi.string().min(8).required(),
      verificationCode: Joi.string().length(6).required(),
      confirmPassword: Joi.string().min(8).optional()
    });
    
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message
      });
    }
    
    // 验证密码强度
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        success: false,
        message: '密码必须至少8个字符，包含大写字母、小写字母、数字和特殊字符'
      });
    }
    
    // 验证邮箱验证码
    try {
      await verifyEmailCode(email, verificationCode, 'registration');
    } catch (codeError) {
      return res.status(400).json({
        success: false,
        message: codeError.message
      });
    }
    
    // 检查用户是否已存在
    const whereConditions = [{ email }];
    if (username) {
      whereConditions.push({ username });
    }
    
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: whereConditions
      }
    });
    
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: existingUser.email === email ? '邮箱已被注册' : '用户名已被使用'
      });
    }
    
    // 加密密码
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // 创建用户
    const user = await prisma.user.create({
      data: {
        username,
        email,
        passwordHash,
        role: 'developer'
      }
    });
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        email: user.email,
        role: user.role
      },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );
    
    // 返回用户信息和token
    res.status(201).json({
      success: true,
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          avatar: user.avatarUrl,
          bio: user.bio,
          location: user.location,
          githubUrl: user.githubUrl,
          portfolioUrl: user.portfolioUrl
        }
      },
      message: '注册成功'
    });
    
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败，请稍后再试'
    });
  }
});

// 获取当前用户信息
app.get('/api/auth/profile', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 从数据库获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatarUrl: true,
        bio: true,
        location: true,
        githubUrl: true,
        bilibiliUrl: true,
        portfolioUrl: true,
        gender: true,
        phone: true,
        wechat: true,
        profession: true,
        workType: true,
        workTime: true,
        cooperationMode: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: user,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取失败: ' + error.message
    });
  }
});

// 更新用户资料
app.put('/api/auth/profile', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const updateData = req.body;
    
    // 更新用户资料
    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatarUrl: true,
        bio: true,
        location: true,
        githubUrl: true,
        bilibiliUrl: true,
        portfolioUrl: true,
        gender: true,
        phone: true,
        wechat: true,
        profession: true,
        workType: true,
        workTime: true,
        cooperationMode: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    res.json({
      success: true,
      data: { user },
      message: '资料更新成功'
    });
  } catch (error) {
    console.error('更新用户资料失败:', error);
    res.status(500).json({
      success: false,
      message: '更新失败: ' + error.message
    });
  }
});

// 头像上传接口
app.post('/api/auth/upload-avatar', authenticate, upload.single('avatar'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '没有上传文件'
      });
    }

    const userId = req.user.id;
    const fileUrl = `/uploads/${req.file.filename}`;
    
    // 更新用户头像URL到数据库
    const user = await prisma.user.update({
      where: { id: userId },
      data: { avatarUrl: fileUrl },
      select: {
        id: true,
        username: true,
        email: true,
        avatarUrl: true
      }
    });
    
    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatarUrl: fileUrl,
        user: user
      }
    });

  } catch (error) {
    console.error('头像上传失败:', error);
    res.status(500).json({
      success: false,
      message: '头像上传失败: ' + error.message
    });
  }
});

// 验证token
app.get('/api/auth/verify', (req, res) => {
  res.json({
    success: true,
    data: {
      valid: true,
      user: {
        id: 1,
        name: '测试用户',
        email: '<EMAIL>',
        role: 'developer'
      }
    },
    message: 'Token验证成功'
  });
});

// 退出登录
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: '退出登录成功'
  });
});

// 忘记密码
app.post('/api/auth/forgot-password', async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请提供邮箱地址',
        code: 'EMAIL_REQUIRED'
      }
    });
  }

  try {
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        username: true
      }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '该邮箱未注册',
          code: 'EMAIL_NOT_FOUND'
        }
      });
    }
    
    // 检查是否存在未过期的重置令牌
    const existingToken = await prisma.passwordResetToken.findFirst({
      where: {
        userId: user.id,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });
    
    if (existingToken) {
      return res.status(429).json({
        success: false,
        error: {
          message: '重置邮件已发送，请稍后再试',
          code: 'RESET_EMAIL_SENT'
        }
      });
    }
    
    // 生成重置令牌
    const resetToken = jwt.sign(
      { 
        email: user.email, 
        userId: user.id, 
        type: 'password_reset',
        timestamp: Date.now() 
      },
      process.env.JWT_SECRET,
      { expiresIn: '15m' }
    );
    
    // 生成数据库令牌
    const dbToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    
    // 计算过期时间（15分钟）
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000);
    
    // 保存令牌到数据库
    await prisma.passwordResetToken.create({
      data: {
        email: user.email,
        token: dbToken,
        userId: user.id,
        expiresAt
      }
    });
    
    // 生成重置链接
    const resetUrl = `http://localhost:3000/forgot-password?token=${resetToken}`;
    
    // 模拟发送邮件（实际应用中需要真实的邮件服务）
    console.log('======= 密码重置邮件 =======');
    console.log('收件人:', user.email);
    console.log('重置链接:', resetUrl);
    console.log('有效期: 15分钟');
    console.log('============================');
    
    res.json({
      success: true,
      message: '密码重置邮件已发送，请检查您的邮箱'
    });
    
  } catch (error) {
    console.error('忘记密码服务错误:', error);
    res.status(500).json({
      success: false,
      error: {
        message: '服务器内部错误',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
});

// 重置密码
app.post('/api/auth/reset-password', async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({
      success: false,
      error: {
        message: '请提供重置令牌和新密码',
        code: 'MISSING_REQUIRED_FIELDS'
      }
    });
  }

  try {
    // 验证JWT令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'password_reset') {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的重置令牌',
          code: 'INVALID_TOKEN'
        }
      });
    }
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true
      }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在',
          code: 'USER_NOT_FOUND'
        }
      });
    }
    
    // 验证邮箱匹配
    if (user.email !== decoded.email) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的重置令牌',
          code: 'INVALID_TOKEN'
        }
      });
    }
    
    // 检查数据库中是否有有效的重置令牌
    const dbToken = await prisma.passwordResetToken.findFirst({
      where: {
        userId: user.id,
        email: user.email,
        isUsed: false,
        expiresAt: {
          gt: new Date()
        }
      }
    });
    
    if (!dbToken) {
      return res.status(400).json({
        success: false,
        error: {
          message: '重置令牌已过期或无效',
          code: 'TOKEN_EXPIRED'
        }
      });
    }
    
    // 验证密码强度
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '密码必须至少8个字符，包含大写字母、小写字母、数字和特殊字符',
          code: 'INVALID_PASSWORD_FORMAT'
        }
      });
    }
    
    // 加密新密码
    const passwordHash = await bcrypt.hash(password, 12);
    
    // 使用事务更新密码并标记令牌为已使用
    await prisma.$transaction([
      prisma.user.update({
        where: { id: user.id },
        data: { passwordHash }
      }),
      prisma.passwordResetToken.update({
        where: { id: dbToken.id },
        data: { isUsed: true }
      })
    ]);
    
    console.log('用户密码重置成功:', user.email);
    
    res.json({
      success: true,
      message: '密码重置成功'
    });
    
  } catch (error) {
    console.error('重置密码服务错误:', error);
    
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(400).json({
        success: false,
        error: {
          message: '重置令牌已过期或无效',
          code: 'INVALID_TOKEN'
        }
      });
    }
    
    res.status(500).json({
      success: false,
      error: {
        message: '服务器内部错误',
        code: 'INTERNAL_SERVER_ERROR'
      }
    });
  }
});

// 重置密码（使用验证码）
app.post('/api/auth/reset-password-with-code', async (req, res) => {
  try {
    const { email, code, password } = req.body;
    
    // 验证输入数据
    if (!email || !code || !password) {
      return res.status(400).json({
        success: false,
        message: '邮箱、验证码和新密码都是必需的'
      });
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: '请输入有效的邮箱地址'
      });
    }
    
    // 验证验证码格式
    if (!/^\d{6}$/.test(code)) {
      return res.status(400).json({
        success: false,
        message: '请输入6位数字验证码'
      });
    }
    
    // 验证密码强度
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(password)) {
      return res.status(400).json({
        success: false,
        message: '密码必须至少8个字符，包含大写字母、小写字母、数字和特殊字符'
      });
    }
    
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { email }
    });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '该邮箱未注册'
      });
    }
    
    // 验证验证码
    try {
      await verifyEmailCode(email, code, 'password_reset');
    } catch (codeError) {
      return res.status(400).json({
        success: false,
        message: codeError.message
      });
    }
    
    // 加密新密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);
    
    // 更新用户密码
    await prisma.user.update({
      where: { id: user.id },
      data: { passwordHash }
    });
    
    console.log('用户密码重置成功（验证码方式）:', user.email);
    
    res.json({
      success: true,
      message: '密码重置成功，请使用新密码登录'
    });
    
  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败，请稍后重试'
    });
  }
});

// 获取项目列表
app.get('/api/projects', async (req, res) => {
  const { page = 1, pageSize = 10, category, status, keyword } = req.query;
  
  try {
    // 优先从数据库获取审核通过的项目
    let dbProjects = [];
    try {
      console.log('🔍 尝试从数据库获取项目...');
      const dbProjectsList = await prisma.project.findMany({
        where: {
          reviewStatus: 'approved',
          status: 'recruiting'
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      console.log('📊 数据库查询结果:', dbProjectsList.length, '个项目');
      
      // 将数据库项目转换为兼容格式
      dbProjects = dbProjectsList.map(project => ({
        id: project.id,
        title: project.title,
        tagline: project.summary,
        category: project.category,
        description: project.description,
        budget: project.budgetRange || '面议',
        timeframe: project.durationMonths ? `${project.durationMonths}个月` : '3-6个月',
        status: project.status,
        urgency: 'medium',
        requirements: project.recruitmentInfo ? 
          (Array.isArray(project.recruitmentInfo) ? project.recruitmentInfo : []) : [],
        remote: project.workType === 'remote' || project.workType === 'hybrid',
        location: project.workLocation || '远程',
        createdAt: project.createdAt,
        existingTeam: project.teamInfo ? 
          (Array.isArray(project.teamInfo) ? project.teamInfo : []) : [],
        workLocation: project.workType,
        workArrangement: project.workArrangement,
        featured: project.featured || false,
        reviewStatus: 'approved',
        owner: {
          id: project.creator.id,
          name: project.creator.username,
          email: project.creator.email
        }
      }));
      
      console.log('✅ Database projects converted:', dbProjects.length, 'projects');
    } catch (dbError) {
      console.log('❌ 从数据库获取项目失败，使用示例数据:', dbError.message);
    }

    // 只使用数据库项目
    let allProjects = dbProjects;
    
    // 只显示审核通过且未下架且未删除的项目
    let filteredProjects = allProjects.filter(p => 
      p.reviewStatus === 'approved' && 
      p.status === 'recruiting' && 
      !p.deleted
    );
    
    // 筛选条件
    if (category && category !== 'all') {
      filteredProjects = filteredProjects.filter(p => p.category === category);
    }
    
    if (status && status !== 'all') {
      filteredProjects = filteredProjects.filter(p => p.status === status);
    }
    
    if (keyword) {
      filteredProjects = filteredProjects.filter(p => 
        p.title.includes(keyword) || p.description.includes(keyword)
      );
    }
    
    // 分页
    const start = (page - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const paginatedProjects = filteredProjects.slice(start, end);
    
    res.json({
      success: true,
      data: {
        projects: paginatedProjects,
        total: filteredProjects.length,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取项目列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目列表失败'
    });
  }
});

// 获取精选项目
app.get('/api/projects/featured', async (req, res) => {
  console.log('获取精选项目请求');
  
  try {
    let featuredProjects = [];
    
    // 优先从数据库获取精选项目，然后合并所有数据源
    let dbProjects = [];
    try {
      const dbProjectsList = await prisma.project.findMany({
        where: {
          featured: true,
          reviewStatus: 'approved',
          status: 'recruiting'
        },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      dbProjects = dbProjectsList.map(project => ({
        id: project.id,
        title: project.title,
        tagline: project.summary,
        category: project.category,
        description: project.description,
        budget: project.budgetRange || '面议',
        timeframe: project.durationMonths ? `${project.durationMonths}个月` : '3-6个月',
        status: project.status,
        featured: true,
        reviewStatus: 'approved',
        workLocation: project.workType,
        location: project.workLocation || '远程',
        existingTeam: project.teamInfo ? 
          (Array.isArray(project.teamInfo) ? project.teamInfo : []) : [],
        owner: {
          id: project.creator.id,
          name: project.creator.username,
          email: project.creator.email
        }
      }));
    } catch (dbError) {
      console.log('从数据库获取精选项目失败:', dbError.message);
    }
    
    // 只使用数据库精选项目
    let allProjects = dbProjects;
    
    const allFeaturedProjects = allProjects.filter(p => 
      p.featured === true && 
      p.reviewStatus === 'approved' && 
      p.status === 'recruiting'
    );
    
    console.log('找到精选项目:', allFeaturedProjects.length, '个');
    allFeaturedProjects.forEach(p => {
      console.log('精选项目:', p.id, p.title, 'featured:', p.featured);
    });
    
    featuredProjects = allFeaturedProjects.slice(0, 6).map(project => ({
      id: project.id,
      title: project.title,
      summary: project.tagline || (project.summary ? project.summary : project.description.substring(0, 100)),
      description: project.description.length > 150 ? 
        project.description.substring(0, 150) + '...' : project.description,
      category: project.category,
      budget: project.budget,
      timeframe: project.timeframe,
      status: project.status,
      mainImage: project.mainImage,
      workType: project.workLocation === 'remote' ? '远程' : '现场',
      location: project.location || '远程协作',
      stage: '招募中',
      featured: true,
      owner: {
        name: project.existingTeam?.[0]?.name || project.owner?.name || '匿名发布者'
      }
    }));

    console.log('返回精选项目:', featuredProjects.length, '个');
    featuredProjects.forEach(p => {
      console.log('返回项目:', p.id, p.title);
    });
    
    res.status(200).json({
      success: true,
      message: '获取精选项目成功',
      data: {
        projects: featuredProjects
      }
    });
  } catch (error) {
    console.error('获取精选项目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取精选项目失败'
    });
  }
});

// 获取项目详情
app.get('/api/projects/:id', async (req, res) => {
  const id = req.params.id;
  
  try {
    // 优先从数据库查找项目
    let project = null;
    try {
      const dbProject = await prisma.project.findUnique({
        where: { id: parseInt(id) },
        include: {
          creator: {
            select: {
              id: true,
              username: true,
              email: true
            }
          }
        }
      });
      
      if (dbProject) {
        // 转换数据库项目为兼容格式
        project = {
          id: dbProject.id,
          title: dbProject.title,
          tagline: dbProject.summary,
          category: dbProject.category,
          description: dbProject.description,
          budget: dbProject.budgetRange || '面议',
          timeframe: dbProject.durationMonths ? `${dbProject.durationMonths}个月` : '3-6个月',
          status: dbProject.status,
          urgency: 'medium',
          requirements: dbProject.recruitmentInfo ? 
            (Array.isArray(dbProject.recruitmentInfo) ? dbProject.recruitmentInfo : []) : [],
          remote: dbProject.workType === 'remote' || dbProject.workType === 'hybrid',
          location: dbProject.workLocation || '远程',
          createdAt: dbProject.createdAt,
          updatedAt: dbProject.updatedAt,
          teamInfo: dbProject.teamInfo ? 
            (Array.isArray(dbProject.teamInfo) ? dbProject.teamInfo : []) : [],
          existingTeam: dbProject.teamInfo ? 
            (Array.isArray(dbProject.teamInfo) ? dbProject.teamInfo : []) : [],
          workLocation: dbProject.workType,
          workArrangement: dbProject.workArrangement,
          featured: dbProject.featured || false,
          reviewStatus: 'approved',
          owner: {
            id: dbProject.creator.id,
            name: dbProject.creator.username,
            email: dbProject.creator.email,
            avatar: null
          },
          // 添加项目详情页需要的字段
          projectOrigin: dbProject.projectOrigin || '基于市场需求和技术创新的创业项目',
          projectAdvantage: dbProject.competitiveAdvantage || '具有独特的技术优势和市场定位',
          userAnalysis: dbProject.userAnalysis || '面向广大用户群体，解决实际痛点',
          teamCommitment: dbProject.teamInvestment || '团队全力投入，致力于项目成功',
          recentProgress: dbProject.recentProgress || '项目进展顺利，各项功能正在稳步推进',
          businessModel: dbProject.businessModel || 'subscription',
          businessModelDetail: dbProject.businessDescription || '采用可持续的商业模式，确保项目长期发展',
          legalEntity: 'planning',
          legalEntityDetail: '正在规划相关法律实体设立',
          paymentType: 'milestone',
          progress: 25,
          currentMilestone: 'development',
          milestoneDescription: '项目正在积极开发中'
        };
      }
    } catch (dbError) {
      console.log('数据库查询失败，尝试内存数据:', dbError.message);
    }
    
    // 如果数据库中没有找到，返回404
    if (!project) {
      return res.status(404).json({
        success: false,
        message: '项目不存在'
      });
    }
  
    // 为项目添加默认owner信息
    if (!project.owner) {
      project.owner = {
        id: 1,
        name: '项目创始人',
        email: '<EMAIL>',
        avatar: null
      };
    }

    // 为项目添加默认申请者数组
    if (!project.applicants) {
      project.applicants = [];
    }
    
    // 返回真实的项目数据，使用数据库字段
    const enhancedProject = {
      id: project.id,
      title: project.title,
      tagline: project.tagline || '基于先进技术的创新应用，为用户提供卓越体验',
      category: project.category,
      status: project.status,
      featured: project.featured,
      description: project.description,
      
      // 项目基本信息扩展字段
      projectOrigin: project.projectOrigin,
      projectAdvantage: project.projectAdvantage,
      userAnalysis: project.userAnalysis,
      
      // 团队信息字段 - 使用真实的teamInfo数据
      teamInfo: project.teamInfo || [],
      teamCommitment: project.teamCommitment,
      workLocation: project.workLocation,
      workArrangement: project.workArrangement,
      location: project.location,
      
      // 进展和商业模式字段
      recentProgress: project.recentProgress,
      businessModel: project.businessModel,
      businessModelDetail: project.businessModelDetail,
      timeframe: project.timeframe,
      expectedStartDate: project.expectedStartDate,
      legalEntity: project.legalEntity,
      legalEntityDetail: project.legalEntityDetail,
      
      // Demo链接字段
      demoLinks: project.demoLinks || [],
      
      // 项目展示 - 使用真实的图片数据
      mainImage: project.mainImage || `https://via.placeholder.com/800x400?text=${encodeURIComponent(project.title)}+展示图`,
      images: project.images && project.images.length > 0 ? project.images : [
        { name: '主界面', url: `https://via.placeholder.com/200x150?text=主界面` },
        { name: '功能页', url: `https://via.placeholder.com/200x150?text=功能页` },
        { name: '数据页', url: `https://via.placeholder.com/200x150?text=数据页` },
        { name: '设置页', url: `https://via.placeholder.com/200x150?text=设置页` }
      ],
      
      // 项目基本信息 - 使用真实数据
      meta: {
        timeframe: project.timeframe,
        teamSize: project.teamSize,
        currentTeamSize: (project.teamInfo || []).length || 1,
        workLocation: project.workLocation,
        workArrangement: project.workArrangement,
        location: project.location,
        expectedStartDate: project.expectedStartDate
      },
      
      // 项目进度 - 使用真实数据
      progress: project.progress || 25,
      currentMilestone: project.currentMilestone || 'development',
      milestoneDescription: project.milestoneDescription || '项目正在积极开发中',
      milestones: [
        { id: 'team-building', name: '团队组建', status: 'completed' },
        { id: 'requirement-design', name: '需求设计', status: 'completed' },
        { id: 'development', name: '开发实现', status: 'current' },
        { id: 'testing-launch', name: '测试上线', status: 'pending' }
      ],
      
      // 项目链接 - 使用真实数据
      links: project.links && project.links.length > 0 ? project.links : [
        { type: 'website', name: '官方网站', url: '', enabled: false },
        { type: 'app', name: 'APP体验', url: '', enabled: false },
        { type: 'document', name: 'PRD文档', url: '#', enabled: true },
        { type: 'design', name: '设计稿', url: '#', enabled: true },
        { type: 'research', name: '市场调研', url: '#', enabled: true }
      ],
      
      // 团队成员 - 使用真实的teamInfo数据
      teamMembers: (project.teamInfo || []).length > 0 ? 
        (project.teamInfo || []).map((member, index) => ({
          id: index + 1,
          name: member.name,
          role: member.role,
          avatar: `https://via.placeholder.com/80x80?text=${member.name.charAt(0)}`,
          skills: extractSkillsFromBackground(member.background),
          isOwner: member.role === 'founder'
        })) : [
          {
            id: project.owner.id,
            name: project.owner.name,
            role: '项目发起人',
            avatar: `https://via.placeholder.com/80x80?text=${project.owner.name.charAt(0)}`,
            skills: ['产品设计', '项目管理', '团队协作'],
            isOwner: true
          }
        ],
      
      // 人员需求 - 使用真实的发布数据
      requirements: (project.requirements || []).map((req) => {
        const getRoleName = (roleValue) => {
          const roleMap = {
            'ui': 'UI设计师',
            'frontend': '前端工程师',
            'backend': '后端工程师',
            'mobile': '移动端开发',
            'ai': 'AI工程师',
            'blockchain': '区块链工程师',
            'data': '数据工程师',
            'pm': '产品经理',
            'qa': '测试工程师',
            'operations': '运营专员'
          };
          return roleMap[roleValue] || roleValue;
        };

        const getCooperationText = (cooperation, salaryAmount, equityAmount) => {
          switch(cooperation) {
            case 'salary': return `薪资: ${salaryAmount || 0}元/月`;
            case 'equity': return `股权: ${equityAmount || 0}%`;
            case 'salary_equity': return `薪资: ${salaryAmount || 0}元/月 + 股权: ${equityAmount || 0}%`;
            default: return '面议';
          }
        };
        
        return {
          role: req.role,
          roleName: getRoleName(req.role),
          skillName: req.skillName,
          cooperation: req.cooperation,
          salaryAmount: req.salaryAmount,
          equityAmount: req.equityAmount,
          cooperationText: getCooperationText(req.cooperation, req.salaryAmount, req.equityAmount)
        };
      }),
      
      // 项目亮点 - 使用真实数据
      highlights: project.highlights || '行业领先的创新项目，市场前景广阔。',
      
      // 额外信息
      contactInfo: project.contactInfo,
      paymentType: project.paymentType,
      
      // 项目发起人详细信息
      owner: {
        id: project.owner.id,
        name: project.owner.name,
        title: '项目发起人',
        bio: '专注产品创新和团队管理，致力于打造有价值的产品。',
        avatar: project.owner.avatar || `https://via.placeholder.com/80x80?text=${project.owner.name.charAt(0)}`,
        rating: 4.8,
        projectCount: 3,
        completedProjects: 2,
        skills: ['产品设计', '项目管理', '团队协作']
      },
      
      // 统计数据
      stats: {
        viewCount: Math.floor(Math.random() * 2000) + 500,
        applicationCount: 0,
        progress: project.progress || 25,
        daysPublished: Math.floor((Date.now() - new Date(project.createdAt).getTime()) / (1000 * 60 * 60 * 24))
      },
      
      // 时间信息
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      
      // 其他发布字段
      budget: project.budget,
      urgency: project.urgency,
      contactInfo: project.contactInfo,
      paymentType: project.paymentType,
      
      // 面包屑导航
      breadcrumb: [
        { name: '项目精选', path: '/projects' },
        { name: getCategoryName(project.category), path: `/projects?category=${project.category}` },
        { name: project.title, path: '' }
      ]
    };
    
    // 添加提取技能的辅助函数
    function extractSkillsFromBackground(background) {
      if (!background) return ['专业技能'];
      const skills = [];
      if (background.includes('前端') || background.includes('React') || background.includes('Vue')) skills.push('前端开发');
      if (background.includes('后端') || background.includes('Java') || background.includes('Python')) skills.push('后端开发');
      if (background.includes('产品') || background.includes('设计')) skills.push('产品设计');
      if (background.includes('AI') || background.includes('机器学习')) skills.push('人工智能');
      if (background.includes('区块链')) skills.push('区块链');
      if (background.includes('游戏')) skills.push('游戏开发');
      if (background.includes('Unity')) skills.push('Unity开发');
      return skills.length > 0 ? skills : ['专业技能'];
    }

    function getCategoryName(category) {
      const categoryMap = {
        'web': 'Web开发',
        'mobile': '移动应用', 
        'ai': 'AI/机器学习',
        'social': '社交网络',
        'ecommerce': '电商平台',
        'fintech': '金融科技',
        'blockchain': '区块链',
        'health': '健康医疗',
        'education': '教育培训',
        'game': '游戏娱乐',
        'enterprise': '企业软件'
      };
      return categoryMap[category] || category;
    }
  
  res.json({
    success: true,
    data: {
      project: enhancedProject
    },
    message: '获取项目详情成功'
  });
  } catch (error) {
    console.error('获取项目详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目详情失败'
    });
  }
});

// 项目发布路由已移至 src/routes/projects.js

// 获取用户发布的项目
app.get('/api/user/projects', async (req, res) => {
  try {
    // 从数据库获取用户项目
    const userProjects = await prisma.project.findMany({
      where: {
        creatorId: 1 // 测试用户ID为1
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    res.json({
      success: true,
      data: {
        projects: userProjects,
        total: userProjects.length
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取用户项目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户项目失败'
    });
  }
});

// 获取用户参与的项目
app.get('/api/user/joined-projects', (req, res) => {
  // 模拟用户参与的项目数据
  const joinedProjects = [
    {
      id: 101,
      title: 'CRM系统开发',
      description: '企业客户关系管理系统，包含客户管理、销售跟进等功能',
      category: 'enterprise',
      budget: 100000,
      status: 'in_progress',
      role: '前端开发工程师',
      team: '6人团队',
      progress: 65,
      joinedAt: '2024-03-15',
      owner: {
        id: 4,
        name: '王总',
        email: '<EMAIL>'
      }
    },
    {
      id: 102,
      title: '移动支付APP',
      description: '金融科技移动支付应用，支持多种支付方式',
      category: 'fintech',
      budget: 80000,
      status: 'completed',
      role: 'React Native开发',
      team: '4人团队',
      progress: 100,
      joinedAt: '2023-10-01',
      completedAt: '2023-12-20',
      owner: {
        id: 5,
        name: '刘经理',
        email: '<EMAIL>'
      }
    }
  ];

  res.json({
    success: true,
    data: {
      projects: joinedProjects,
      total: joinedProjects.length
    },
    message: '获取成功'
  });
});

// 获取用户认证状态
app.get('/api/user/certification-status', authenticate, async (req, res) => {
  try {
    // 从数据库查询用户信息或返回默认认证状态
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: { 
        id: true,
        email: true,
        role: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 简单的认证状态逻辑，可以根据需要扩展
    let certificationStatus = 'none';
    if (user.role === 'admin') {
      certificationStatus = 'verified';
    } else if (user.email && user.email.includes('@')) {
      certificationStatus = 'pending';
    }

    res.json({
      success: true,
      data: {
        status: certificationStatus,
        userId: user.id,
        email: user.email
      },
      message: '获取认证状态成功'
    });
  } catch (error) {
    console.error('获取认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 申请项目功能已移除 - 请使用数据库实现

// 收藏功能已移除 - 请使用数据库实现

// 获取用户收藏的项目功能已移除 - 请使用数据库实现

// 收藏项目功能已移除 - 请使用数据库实现

// 取消收藏项目功能已移除 - 请使用数据库实现

// 检查项目是否已收藏功能已移除 - 请使用数据库实现

// 管理接口 - 获取所有数据概览
app.get('/api/admin/overview', async (req, res) => {
  try {
    // 从数据库获取统计数据
    const totalProjects = await prisma.project.count();
    const activeProjects = await prisma.project.count({
      where: { status: 'recruiting' }
    });
    const completedProjects = await prisma.project.count({
      where: { status: 'completed' }
    });
    
    const recentProjects = await prisma.project.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        title: true,
        category: true,
        status: true,
        createdAt: true
      }
    });

    res.json({
      success: true,
      data: {
        stats: {
          totalProjects,
          totalApplications: 0, // 待实现
          activeProjects,
          completedProjects
        },
        recentProjects,
        recentApplications: [] // 待实现
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取管理概览失败:', error);
    res.status(500).json({
      success: false,
      message: '获取概览数据失败'
    });
  }
});

// 管理接口 - 获取所有项目详细信息
app.get('/api/admin/projects', async (req, res) => {
  try {
    const projects = await prisma.project.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: {
        projects,
        total: projects.length
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取所有项目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目列表失败'
    });
  }
});

// 管理接口 - 获取所有申请记录（功能已移除）
app.get('/api/admin/applications', (req, res) => {
  res.json({
    success: true,
    data: {
      applications: [],
      total: 0
    },
    message: '申请记录功能已移除，请使用数据库实现'
  });
});

// ============== 管理后台 API 接口 ==============
// 注意：管理员登录通过 /api/admin/login 路由处理（在adminRoutes中）

// 管理员验证token
app.get('/api/admin/verify', (req, res) => {
  res.json({
    success: true,
    data: {
      valid: true,
      admin: {
        id: 1,
        username: 'admin',
        name: '系统管理员',
        role: 'admin'
      }
    },
    message: '管理员Token验证成功'
  });
});

// 管理后台首页统计数据
app.get('/api/admin/dashboard', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // 从数据库获取统计数据
    const totalProjects = await prisma.project.count();
    const todayProjects = await prisma.project.count({
      where: {
        createdAt: {
          gte: new Date(today)
        }
      }
    });
    const pendingProjects = await prisma.project.count({
      where: { reviewStatus: 'pending' }
    });
    const approvedProjects = await prisma.project.count({
      where: { reviewStatus: 'approved' }
    });
    const rejectedProjects = await prisma.project.count({
      where: { reviewStatus: 'rejected' }
    });
    
    // 近7天项目发布趋势（简化版本）
    const last7Days = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      last7Days.push({
        date: dateStr,
        count: Math.floor(Math.random() * 5) + 1 // 模拟数据
      });
    }
    
    res.json({
      success: true,
      data: {
        stats: {
          totalUsers: 128, // 模拟用户总数
          todayNewUsers: 3,
          totalProjects,
          todayNewProjects: todayProjects,
          pendingProjects,
          approvedProjects,
          rejectedProjects,
          approvalRate: approvedProjects > 0 ? 
            Math.round((approvedProjects / (approvedProjects + rejectedProjects)) * 100) : 0
        },
        charts: {
          projectTrend: last7Days,
          categoryDistribution: [] // 暂时为空
        }
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表板数据失败'
    });
  }
});

// 获取待审核项目列表
app.get('/api/admin/review/pending', async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    // 从数据库获取待审核项目
    const pendingProjects = await prisma.project.findMany({
      where: {
        reviewStatus: 'pending'
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: parseInt(pageSize)
    });

    const total = await prisma.project.count({
      where: { reviewStatus: 'pending' }
    });
    
    res.json({
      success: true,
      data: {
        projects: pendingProjects,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取待审核项目失败:', error);
    res.status(500).json({
      success: false,
      message: '获取待审核项目失败'
    });
  }
});

// 获取所有项目列表（管理后台）
app.get('/api/admin/projects/all', (req, res) => {
  const { page = 1, pageSize = 10, reviewStatus, category, keyword } = req.query;
  
  let filteredProjects = projects.filter(p => !p.deleted);
  
  // 按审核状态筛选
  if (reviewStatus && reviewStatus !== 'all') {
    filteredProjects = filteredProjects.filter(p => p.reviewStatus === reviewStatus);
  }
  
  // 按分类筛选
  if (category && category !== 'all') {
    filteredProjects = filteredProjects.filter(p => p.category === category);
  }
  
  // 关键词搜索
  if (keyword) {
    filteredProjects = filteredProjects.filter(p => 
      p.title.includes(keyword) || p.description.includes(keyword)
    );
  }
  
  // 按创建时间倒序排列
  filteredProjects.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
  
  // 分页
  const start = (page - 1) * pageSize;
  const end = start + parseInt(pageSize);
  const paginatedProjects = filteredProjects.slice(start, end);
  
  res.json({
    success: true,
    data: {
      projects: paginatedProjects,
      total: filteredProjects.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    },
    message: '获取成功'
  });
});

// 项目审核 - 通过
app.post('/api/admin/review/:id/approve', (req, res) => {
  const projectId = parseInt(req.params.id);
  const project = projects.find(p => p.id === projectId);
  
  if (!project) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }
  
  project.reviewStatus = 'approved';
  project.reviewTime = new Date().toISOString();
  project.reviewerId = 1; // 管理员ID
  project.updatedAt = new Date().toISOString();
  
  // 发送系统消息通知用户项目审核通过
  console.log(`项目${project.title}审核通过，已发送通知给用户`);
  
  res.json({
    success: true,
    data: project,
    message: '项目审核通过，已发送通知给用户'
  });
});

// 项目审核 - 拒绝
app.post('/api/admin/review/:id/reject', (req, res) => {
  try {
    const projectId = parseInt(req.params.id);
    const { reason } = req.body;
    
    console.log('拒绝项目请求:', { projectId, reason });
    
    const project = projects.find(p => p.id === projectId);
    
    if (!project) {
      return res.status(404).json({
        success: false,
        message: '项目不存在'
      });
    }
    
    if (!reason || reason.trim() === '') {
      return res.status(400).json({
        success: false,
        message: '请填写拒绝原因'
      });
    }
    
    project.reviewStatus = 'rejected';
    project.reviewTime = new Date().toISOString();
    project.reviewerId = 1; // 管理员ID
    project.rejectReason = reason;
    project.updatedAt = new Date().toISOString();
    
    // 发送系统消息通知用户
    console.log(`项目${project.title}已拒绝，系统消息已发送`);
    
    console.log('项目拒绝成功:', { projectId, status: project.reviewStatus });
    
    res.json({
      success: true,
      data: project,
      message: '项目已拒绝，系统消息已发送'
    });
  } catch (error) {
    console.error('拒绝项目时发生错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 项目操作 - 切换精选状态
app.post('/api/admin/projects/:id/toggle-featured', (req, res) => {
  const projectId = parseInt(req.params.id);
  const { featured } = req.body;
  
  console.log('切换精选状态请求:', { projectId, featured });
  
  // 查找项目（在内存数据和示例数据中都查找）
  let project = projects.find(p => p.id === projectId);
  let isFromSample = false;
  
  if (!project) {
    // 在示例项目中查找
    project = sampleProjects.find(p => p.id === projectId);
    isFromSample = true;
  }
  
  if (!project) {
    console.log('项目不存在:', projectId);
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }
  
  // 检查项目是否已审核通过
  if (project.reviewStatus !== 'approved') {
    return res.status(400).json({
      success: false,
      message: '只有审核通过的项目才能设为精选'
    });
  }
  
  // 更新精选状态
  project.featured = !!featured;
  project.updatedAt = new Date().toISOString();
  
  console.log('精选状态更新成功:', { projectId, featured: project.featured });
  
  res.json({
    success: true,
    message: featured ? '项目已设为精选' : '已取消项目精选',
    data: {
      projectId: project.id,
      featured: project.featured
    }
  });
});

// 项目操作 - 下架项目
app.post('/api/admin/projects/:id/hide', (req, res) => {
  const projectId = parseInt(req.params.id);
  const project = projects.find(p => p.id === projectId);
  
  if (!project) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }
  
  project.status = 'hidden';
  project.updatedAt = new Date().toISOString();
  
  res.json({
    success: true,
    data: project,
    message: '项目已下架'
  });
});

// 项目操作 - 恢复项目
app.post('/api/admin/projects/:id/restore', (req, res) => {
  const projectId = parseInt(req.params.id);
  const project = projects.find(p => p.id === projectId);
  
  if (!project) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }
  
  project.status = 'recruiting';
  project.updatedAt = new Date().toISOString();
  
  res.json({
    success: true,
    data: project,
    message: '项目已恢复'
  });
});

// 项目操作 - 删除项目（软删除）
app.delete('/api/admin/projects/:id', (req, res) => {
  const projectId = parseInt(req.params.id);
  const projectIndex = projects.findIndex(p => p.id === projectId);
  
  if (projectIndex === -1) {
    return res.status(404).json({
      success: false,
      message: '项目不存在'
    });
  }
  
  // 软删除：标记为已删除而不是真正删除
  projects[projectIndex].deleted = true;
  projects[projectIndex].deletedAt = new Date().toISOString();
  projects[projectIndex].updatedAt = new Date().toISOString();
  
  res.json({
    success: true,
    message: '项目已删除'
  });
});

// 用户管理 - 获取用户列表
app.get('/api/admin/users', async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    
    // 从数据库获取用户数据
    const users = await prisma.user.findMany({
      skip: (page - 1) * pageSize,
      take: parseInt(pageSize),
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        avatarUrl: true,
        bio: true,
        location: true,
        githubUrl: true,
        portfolioUrl: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    // 获取总数
    const total = await prisma.user.count();
    
    // 格式化用户数据
    const formattedUsers = users.map(user => ({
      id: user.id,
      name: user.username || '未设置',
      email: user.email,
      role: user.role,
      status: 'active', // 默认状态，可以根据需要添加status字段到数据库
      avatar: user.avatarUrl,
      bio: user.bio || '暂无简介',
      skills: [], // 可以根据需要添加技能字段
      location: user.location || '未设置',
      phone: '未设置', // 可以根据需要添加phone字段
      wechat: '未设置', // 可以根据需要添加wechat字段
      github: user.githubUrl || '未设置',
      website: user.portfolioUrl || '未设置',
      experience: '未设置', // 可以根据需要添加experience字段
      education: '未设置', // 可以根据需要添加education字段
      company: '未设置', // 可以根据需要添加company字段
      position: '未设置', // 可以根据需要添加position字段
      hourlyRate: 0, // 可以根据需要添加hourlyRate字段
      projectCount: 0, // 可以通过关联查询获取
      applicationCount: 0, // 可以通过关联查询获取
      completedProjects: 0, // 可以通过关联查询获取
      rating: 0, // 可以根据需要添加rating字段
      createdAt: user.createdAt,
      lastLoginAt: user.updatedAt // 使用updatedAt作为最后登录时间的近似值
    }));
    
    res.json({
      success: true,
      data: {
        users: formattedUsers,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      },
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

// 用户管理 - 禁用/启用用户
app.post('/api/admin/users/:id/toggle-status', (req, res) => {
  const userId = parseInt(req.params.id);
  
  // 这里应该更新数据库中的用户状态
  // 现在只是返回模拟响应
  res.json({
    success: true,
    message: '用户状态已更新'
  });
});

// 获取审核历史
app.get('/api/admin/review/history', (req, res) => {
  const { page = 1, pageSize = 10, status } = req.query;
  
  let reviewedProjects = projects.filter(p => p.reviewStatus !== 'pending');
  
  if (status && status !== 'all') {
    reviewedProjects = reviewedProjects.filter(p => p.reviewStatus === status);
  }
  
  // 按审核时间倒序排列
  reviewedProjects.sort((a, b) => new Date(b.reviewTime) - new Date(a.reviewTime));
  
  // 分页
  const start = (page - 1) * pageSize;
  const end = start + parseInt(pageSize);
  const paginatedProjects = reviewedProjects.slice(start, end);
  
  res.json({
    success: true,
    data: {
      projects: paginatedProjects,
      total: reviewedProjects.length,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    },
    message: '获取成功'
  });
});

// 获取用户的系统消息
app.get('/api/messages', (req, res) => {
  const userId = 1; // 当前用户ID，实际应从token中获取
  
  res.json({
    success: true,
    data: {
      messages: [],
      total: 0,
      unreadCount: 0
    },
    message: '消息功能已移除，请使用数据库实现'
  });
});

// 标记消息为已读
app.post('/api/messages/:id/read', (req, res) => {
  const messageId = parseInt(req.params.id);
  
  res.json({
    success: true,
    message: '消息功能已移除，请使用数据库实现'
  });
});

// DAO相关API路由
// DAO模型实例
let proposalModel = null;
let daoMemberModel = null;

// 初始化DAO模型
const initDAOModels = async () => {
  if (!proposalModel) {
    const { ProposalModel, DAOMemberModel } = await import('./src/models/dao.js');
    proposalModel = new ProposalModel();
    daoMemberModel = new DAOMemberModel();
  }
};

// 获取DAO统计数据
app.get('/api/dao/stats', async (req, res) => {
  try {
    await initDAOModels();
    
    // 计算统计数据
    const proposals = await proposalModel.getProposals();
    const allProposals = proposals.data;
    const members = await daoMemberModel.getAllDAOMembers();
    
    const stats = {
      total_proposals: allProposals.length,
      active_proposals: allProposals.filter(p => p.status === '投票中').length,
      approved_proposals: allProposals.filter(p => p.status === '已通过').length,
      reviewing_proposals: allProposals.filter(p => p.status === '审核中').length,
      total_votes: allProposals.reduce((sum, p) => sum + p.vote_stats.total, 0),
      total_dao_members: members.data.length,
      this_week_proposals: allProposals.filter(p => {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return new Date(p.created_at) > oneWeekAgo;
      }).length
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取DAO统计失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取提案列表（前端API - 只返回审核通过的提案）
app.get('/api/dao/proposals', async (req, res) => {
  try {
    await initDAOModels();
    
    const { page = 1, limit = 10, status } = req.query;
    const result = await proposalModel.getProposalsForFrontend({
      page: parseInt(page),
      limit: parseInt(limit),
      status
    });
    res.json(result);
  } catch (error) {
    console.error('获取提案列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取提案列表（管理员API - 返回所有提案）
app.get('/api/dao/admin/proposals', async (req, res) => {
  try {
    await initDAOModels();
    
    const { page = 1, limit = 10, status } = req.query;
    const result = await proposalModel.getProposals({
      page: parseInt(page),
      limit: parseInt(limit),
      status
    });
    res.json(result);
  } catch (error) {
    console.error('获取管理员提案列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 创建新提案
app.post('/api/dao/proposals', async (req, res) => {
  try {
    await initDAOModels();
    
    const userId = 1; // 临时用户ID
    const proposal = await proposalModel.createProposal(req.body, userId);
    res.json(proposal);
  } catch (error) {
    console.error('创建提案失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 提案投票
app.post('/api/dao/proposals/:id/vote', async (req, res) => {
  try {
    await initDAOModels();
    
    const { id } = req.params;
    const { voteType } = req.body;
    const userId = 1; // 临时用户ID
    
    // 获取用户DAO状态
    const userDaoStatus = await daoMemberModel.getUserDAOStatus(userId);
    
    const result = await proposalModel.vote(id, userId, voteType, userDaoStatus.data);
    res.json(result);
  } catch (error) {
    console.error('投票失败:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
});

// DAO管理员API路由

// 获取所有DAO成员（管理员功能）
app.get('/api/dao/admin/members', async (req, res) => {
  try {
    await initDAOModels();
    
    const result = await daoMemberModel.getAllDAOMembers();
    
    // 为成员添加用户名信息（模拟）
    const members = result.data.map(member => ({
      ...member,
      username: `用户${member.user_id}`
    }));
    
    res.json({
      success: true,
      data: members
    });
  } catch (error) {
    console.error('获取DAO成员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 添加DAO成员（管理员功能）
app.post('/api/dao/admin/members', async (req, res) => {
  try {
    await initDAOModels();
    
    const { user_id, membership_type = 'dao_member' } = req.body;

    if (!user_id) {
      return res.status(400).json({
        success: false,
        message: '用户ID是必需的'
      });
    }

    const result = await daoMemberModel.addDAOMember(user_id, membership_type);
    res.status(201).json(result);
  } catch (error) {
    console.error('添加DAO成员失败:', error);
    res.status(500).json({
      success: false,
      message: '添加DAO成员失败'
    });
  }
});

// 移除DAO成员（管理员功能）
app.delete('/api/dao/admin/members/:userId', async (req, res) => {
  try {
    await initDAOModels();
    
    const result = await daoMemberModel.removeDAOMember(parseInt(req.params.userId));
    res.json(result);
  } catch (error) {
    console.error('移除DAO成员失败:', error);
    res.status(500).json({
      success: false,
      message: '移除DAO成员失败'
    });
  }
});

// 更新提案状态（管理员功能 - 只能审核通过或拒绝）
app.put('/api/dao/admin/proposals/:id/review', async (req, res) => {
  try {
    await initDAOModels();
    
    const { action } = req.body; // action: '审核通过' 或 '审核拒绝'

    if (!['审核通过', '审核拒绝'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: '无效的操作类型，只能选择审核通过或审核拒绝'
      });
    }

    const result = await proposalModel.updateProposalStatus(
      req.params.id,
      action,
      1 // 管理员ID
    );

    res.json(result);
  } catch (error) {
    console.error('审核提案失败:', error);
    res.status(500).json({
      success: false,
      message: '审核提案失败'
    });
  }
});

// 检查用户DAO成员身份
app.get('/api/dao/membership-status', async (req, res) => {
  try {
    await initDAOModels();
    
    const userId = 1; // 临时用户ID，实际应从token中获取
    const result = await daoMemberModel.getUserDAOStatus(userId);
    
    res.json({
      success: true,
      isDaoMember: result.data.isDaoMember,
      membershipType: result.data.membershipType,
      joinedAt: result.data.joinedAt
    });
  } catch (error) {
    console.error('检查DAO成员身份失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取用户投票状态
app.get('/api/dao/user-votes', async (req, res) => {
  try {
    await initDAOModels();
    
    const userId = 1; // 临时用户ID，实际应从token中获取
    
    // 获取用户的所有投票记录
    const userVotes = proposalModel.votes.filter(v => v.user_id === userId);
    const voteMap = {};
    userVotes.forEach(vote => {
      voteMap[vote.proposal_id] = vote.vote_type;
    });
    
    res.json({
      success: true,
      data: voteMap
    });
  } catch (error) {
    console.error('获取用户投票状态失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 启动投票截止时间检查器
const startVotingDeadlineChecker = async () => {
  console.log('🕐 启动投票截止时间检查器...');
  
  // 立即执行一次检查
  await checkVotingDeadlines();
  
  // 每小时检查一次
  setInterval(async () => {
    await checkVotingDeadlines();
  }, 60 * 60 * 1000); // 1小时 = 60分钟 * 60秒 * 1000毫秒
};

// 检查所有投票截止时间
const checkVotingDeadlines = async () => {
  try {
    await initDAOModels();
    
    console.log('🔍 检查投票截止时间...');
    await proposalModel.checkAllVotingDeadlines();
    
    const votingProposals = proposalModel.proposals.filter(p => p.status === '投票中');
    console.log(`📊 当前投票中的提案数量: ${votingProposals.length}`);
    
  } catch (error) {
    console.error('❌ 检查投票截止时间失败:', error);
  }
};

// 数据库连接和服务器启动
const startServer = async () => {
  try {
    // 连接数据库
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 启动服务器
    const server = app.listen(PORT, '127.0.0.1', () => {
      console.log(`🚀 DevMatch 测试服务器启动成功!`);
      console.log(`📍 后端服务: http://localhost:${PORT}`);
      console.log(`💊 健康检查: http://localhost:${PORT}/api/health`);
      console.log(`📝 数据库: ${process.env.DATABASE_URL ? '✅ 真实数据库' : '❌ 未配置数据库'}`);
      console.log(`🔐 JWT密钥: ${process.env.JWT_SECRET ? '✅ 已配置' : '❌ 未配置'}`);
      console.log(`📊 管理面板: http://localhost:${PORT}/api/admin/dashboard`);
      console.log('');
      console.log('🔧 可用接口:');
      console.log('  POST /api/auth/register - 用户注册（真实数据库）');
      console.log('  POST /api/auth/login - 用户登录（真实数据库）');
      console.log('  POST /api/projects - 发布项目（待审核状态）');
      console.log('  GET  /api/projects - 项目列表（仅显示审核通过）');
      console.log('  GET  /api/user/projects - 我的项目');
      console.log('  GET  /api/messages - 系统消息');
      console.log('  POST /api/admin/login - 管理员登录');
      console.log('  GET  /api/admin/review/pending - 待审核项目');
      console.log('  POST /api/admin/review/:id/approve - 审核通过');
      console.log('  POST /api/admin/review/:id/reject - 审核拒绝');
      
      // 启动定时检查投票截止时间的任务
      startVotingDeadlineChecker();
    });
    
    server.on('error', (error) => {
      console.error('❌ 服务器启动失败:', error);
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用`);
      }
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🔄 正在关闭服务器...');
  await prisma.$disconnect();
  console.log('✅ 数据库连接已关闭');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🔄 正在关闭服务器...');
  await prisma.$disconnect();
  console.log('✅ 数据库连接已关闭');
  process.exit(0);
});

startServer(); 