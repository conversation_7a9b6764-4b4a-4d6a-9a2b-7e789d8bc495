import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();
const prisma = new PrismaClient();

async function testDatabaseConnection() {
  try {
    console.log('📋 检查数据库中的用户...');
    
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        username: true,
        email: true,
        passwordHash: true,
        gender: true,
        phone: true,
        wechat: true,
        profession: true,
        workType: true,
        workTime: true,
        cooperationMode: true
      }
    });
    
    if (user) {
      console.log('✅ 找到用户:', {
        id: user.id,
        username: user.username,
        email: user.email,
        hasPassword: !!user.passwordHash,
        gender: user.gender,
        phone: user.phone,
        wechat: user.wechat,
        profession: user.profession,
        workType: user.workType,
        workTime: user.workTime,
        cooperationMode: user.cooperationMode
      });
      
      // 测试密码
      const isValidPassword = await bcrypt.compare('password123', user.passwordHash);
      console.log('密码验证:', isValidPassword ? '✅ 正确' : '❌ 错误');
      
    } else {
      console.log('❌ 未找到用户');
    }
    
  } catch (error) {
    console.error('数据库查询错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabaseConnection();