import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function clearVerificationCodes() {
  try {
    // 删除所有验证码记录
    const result = await prisma.emailVerification.deleteMany({});
    console.log(`✅ 已清理 ${result.count} 个验证码记录`);
    
    // 重置数据库序列
    await prisma.$executeRaw`ALTER SEQUENCE "email_verifications_id_seq" RESTART WITH 1`;
    console.log('✅ 已重置序列');
    
  } catch (error) {
    console.error('❌ 清理验证码失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

clearVerificationCodes();