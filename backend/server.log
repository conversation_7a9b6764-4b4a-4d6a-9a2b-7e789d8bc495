系统已配置为仅使用数据库数据
系统已配置为仅使用数据库数据
✅ 数据库连接成功
🚀 DevMatch 测试服务器启动成功!
📍 后端服务: http://localhost:8000
💊 健康检查: http://localhost:8000/api/health
📝 数据库: ✅ 真实数据库
🔐 JWT密钥: ✅ 已配置
📊 管理面板: http://localhost:8000/api/admin/dashboard

🔧 可用接口:
  POST /api/auth/register - 用户注册（真实数据库）
  POST /api/auth/login - 用户登录（真实数据库）
  POST /api/projects - 发布项目（待审核状态）
  GET  /api/projects - 项目列表（仅显示审核通过）
  GET  /api/user/projects - 我的项目
  GET  /api/messages - 系统消息
  POST /api/admin/login - 管理员登录
  GET  /api/admin/review/pending - 待审核项目
  POST /api/admin/review/:id/approve - 审核通过
  POST /api/admin/review/:id/reject - 审核拒绝
🕐 启动投票截止时间检查器...
🔍 检查投票截止时间...
📊 当前投票中的提案数量: 0
Error: 无效的访问令牌
    at file:///Users/<USER>/Downloads/match/backend/src/middleware/auth.js:47:13
    at file:///Users/<USER>/Downloads/match/backend/src/middleware/errorHandler.js:95:21
    at Layer.handle [as handle_request] (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/layer.js:95:5)
    at next (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/layer.js:95:5)
    at /Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/index.js:346:12)
    at next (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/router/index.js:175:3)
🔍 尝试从数据库获取项目...
📊 数据库查询结果: 1 个项目
📝 第一个项目: 12345
✅ Database projects converted: 1 projects
Current projects loaded: 1 projects
First project ID: 19
