import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    console.log('🔧 开始创建测试用户...');
    
    // 加密密码
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 创建测试用户
    const user = await prisma.user.create({
      data: {
        username: 'testuser',
        email: '<EMAIL>',
        passwordHash: passwordHash,
        role: 'developer',
        bio: '这是一个测试用户账号',
        location: '北京'
      }
    });
    
    console.log('✅ 测试用户创建成功:');
    console.log(`   用户名: ${user.username}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   密码: password123`);
    console.log(`   ID: ${user.id}`);
    
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('⚠️  测试用户已存在，跳过创建');
      console.log('   用户名: testuser');
      console.log('   邮箱: <EMAIL>');
      console.log('   密码: password123');
    } else {
      console.error('❌ 创建测试用户失败:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser(); 