import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000/api';

// 测试完整的个人设置功能
async function testCompleteSettings() {
  console.log('🔐 先登录获取token...');
  
  // 登录获取token
  const loginResponse = await fetch(`${API_BASE}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'password123'
    })
  });
  
  const loginData = await loginResponse.json();
  
  if (!loginData.success) {
    console.log('❌ 登录失败:', loginData);
    return;
  }
  
  const token = loginData.data.token;
  console.log('✅ 登录成功');
  
  console.log('\n📋 获取当前用户资料...');
  const profileResponse = await fetch(`${API_BASE}/auth/profile`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  const profileData = await profileResponse.json();
  console.log('当前用户资料:', JSON.stringify(profileData, null, 2));
  
  console.log('\n💾 测试更新用户资料（包含新字段）...');
  
  const updateData = {
    username: '测试用户更新完整版',
    bio: '这是更新后的个人简介',
    location: '上海',
    gender: 'female',
    phone: '13999999999',
    wechat: 'testwechat456',
    profession: '全栈开发工程师',
    workType: 'hybrid',
    workTime: 'weekends_only',
    cooperationMode: 'both_acceptable',
    avatarUrl: 'https://example.com/avatar.jpg'
  };
  
  console.log('发送的数据:', JSON.stringify(updateData, null, 2));
  
  const updateResponse = await fetch(`${API_BASE}/auth/profile`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updateData)
  });
  
  const updateResult = await updateResponse.json();
  console.log('更新结果:', JSON.stringify(updateResult, null, 2));
  
  if (updateResult.success) {
    console.log('✅ 用户资料更新成功！');
    
    console.log('\n📋 再次获取用户资料验证更新...');
    const verifyResponse = await fetch(`${API_BASE}/auth/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const verifyData = await verifyResponse.json();
    console.log('验证更新后的用户资料:', JSON.stringify(verifyData, null, 2));
  } else {
    console.log('❌ 用户资料更新失败');
  }
}

testCompleteSettings();