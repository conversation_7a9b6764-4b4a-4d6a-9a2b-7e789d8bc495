import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建包含完整字段的示例项目数据
const projects = [
  {
    id: 1,
    title: "智能健康管理平台",
    tagline: "AI驱动的个人健康助手",
    category: "health",
    status: "recruiting",
    featured: true,
    description: "基于人工智能的个人健康管理平台，提供健康数据分析、个性化建议、医疗资源推荐等功能。支持多种健康设备接入，为用户提供全方位的健康管理服务。",
    projectOrigin: "创始人家人患慢性病，深感传统医疗效率低下。结合AI技术背景，决定开发智能健康管理平台，让每个人都能享受个性化的健康服务。",
    projectAdvantage: "独有的医疗知识图谱和AI诊断算法，与传统健康APP相比，我们的建议更专业、更个性化。团队医工结合，技术+医学双重保障。",
    userAnalysis: "目标用户是25-45岁关注健康的都市白领，痛点是缺乏专业健康指导和个性化建议。通过AI分析用户健康数据，提供科学的健康管理方案。",
    existingTeam: [
      {
        name: "张医生",
        role: "医学顾问", 
        background: "三甲医院10年临床经验",
        introduction: "专注数字医疗领域"
      },
      {
        name: "李工程师",
        role: "技术负责人",
        background: "Google AI团队5年经验", 
        introduction: "机器学习专家"
      }
    ],
    teamCommitment: "核心团队全职投入，已投入6个月时间进行产品研发和市场调研，团队凝聚力强，目标明确。",
    workLocation: "hybrid",
    workArrangement: "fullTime", 
    location: "北京市朝阳区",
    recentProgress: "完成了核心AI算法开发，用户健康数据分析准确率达到92%。已与3家医院达成初步合作意向，完成了10000+用户的内测，用户满意度4.6/5.0。",
    businessModel: "subscription",
    businessModelDetail: "采用订阅制，基础功能免费，高级AI分析和个性化服务收费。月费99元，年费899元。预计第二年达到10万付费用户。",
    timeframe: "6个月以上",
    expectedStartDate: "2024-07-01T00:00:00.000Z",
    legalEntity: "registered",
    legalEntityDetail: "北京智康科技有限公司，注册资本500万元，已获得医疗器械软件备案。",
    demoLinks: [],
    mainImage: "https://picsum.photos/800/400?random=1",
    images: [
      { name: "主界面", url: "https://via.placeholder.com/200x150?text=主界面" },
      { name: "功能页", url: "https://via.placeholder.com/200x150?text=功能页" },
      { name: "数据页", url: "https://via.placeholder.com/200x150?text=数据页" },
      { name: "设置页", url: "https://via.placeholder.com/200x150?text=设置页" }
    ],
    meta: {
      timeframe: "6个月以上",
      teamSize: 8,
      currentTeamSize: 1,
      workLocation: "hybrid",
      workArrangement: "fullTime",
      location: "北京市朝阳区",
      expectedStartDate: "2024-07-01T00:00:00.000Z"
    },
    progress: 35,
    currentMilestone: "development",
    milestoneDescription: "正在进行移动端开发，预计2个月内完成MVP版本。",
    milestones: [
      { id: "team-building", name: "团队组建", status: "completed" },
      { id: "requirement-design", name: "需求设计", status: "completed" },
      { id: "development", name: "开发实现", status: "current" },
      { id: "testing-launch", name: "测试上线", status: "pending" }
    ],
    links: [
      { type: "website", name: "官方网站", url: "", enabled: false },
      { type: "app", name: "APP体验", url: "", enabled: false },
      { type: "document", name: "PRD文档", url: "#", enabled: true },
      { type: "design", name: "设计稿", url: "#", enabled: true },
      { type: "research", name: "市场调研", url: "#", enabled: true }
    ],
    teamMembers: [
      {
        id: 2,
        name: "张医生",
        role: "项目发起人 / 产品经理",
        avatar: "https://via.placeholder.com/80x80?text=张",
        skills: ["产品设计", "项目管理", "团队协作"],
        isOwner: true
      }
    ],
    requirements: [
      {
        role: "Android开发工程师",
        skillName: ["Android", "Kotlin", "Java"],
        cooperation: "salary_equity",
        salaryAmount: "25K-35K",
        equityAmount: 2.5
      },
      {
        role: "iOS开发工程师", 
        skillName: ["iOS", "Swift", "Objective-C"],
        cooperation: "salary_equity",
        salaryAmount: "25K-35K",
        equityAmount: 2.5
      }
    ],
    highlights: "行业领先的创新项目，市场前景广阔。采用最新技术栈，提升开发体验。",
    paymentType: "milestone",
    owner: {
      id: 2,
      name: "张医生",
      title: "项目发起人 / 产品经理",
      bio: "多年行业经验，专注产品创新和团队管理，致力于打造有价值的产品。",
      avatar: "https://via.placeholder.com/80x80?text=张",
      rating: 4.8,
      projectCount: 3,
      completedProjects: 2,
      skills: ["产品设计", "项目管理", "团队协作"]
    },
    stats: {
      viewCount: 2000,
      applicationCount: 0,
      progress: 25,
      daysPublished: 372
    },
    createdAt: "2024-06-10",
    updatedAt: "2024-06-10",
    budget: 800000,
    urgency: "high",
    breadcrumb: [
      { name: "项目精选", path: "/projects" },
      { name: "健康生活", path: "/projects?category=health" },
      { name: "智能健康管理平台", path: "" }
    ]
  },
  {
    id: 2,
    title: "AI智能客服系统",
    tagline: "企业级智能对话解决方案",
    category: "ai",
    status: "recruiting",
    featured: true,
    description: "基于大语言模型的企业级智能客服系统，支持多轮对话、情感识别、知识库问答等功能。帮助企业降低人工客服成本，提升用户服务体验。",
    projectOrigin: "团队在为多家企业提供客服外包服务时，发现传统人工客服效率低、成本高、服务质量不稳定。决定利用AI技术革新客服行业。",
    projectAdvantage: "拥有自研的对话理解算法，支持100+行业知识库，客户满意度提升40%。团队具备丰富的企业服务经验。",
    userAnalysis: "目标客户是中大型企业，特别是电商、金融、教育等客服需求量大的行业。痛点是人工成本高、服务质量难以标准化。",
    existingTeam: [
      {
        name: "王总",
        role: "创始人",
        background: "前阿里巴巴产品总监，10年企业服务经验",
        introduction: "深度理解企业客服痛点"
      },
      {
        name: "刘博士",
        role: "AI算法专家",
        background: "清华大学NLP博士，前百度AI研究员",
        introduction: "自然语言处理专家"
      }
    ],
    teamCommitment: "核心团队已全职投入8个月，完成了技术验证和客户验证，获得了3家大客户的意向订单。",
    workLocation: "hybrid",
    workArrangement: "fullTime",
    location: "上海市浦东新区",
    recentProgress: "完成了核心对话引擎开发，支持10万+QA对，响应准确率达到95%。已接入5家试点客户，日处理对话量超过10万次。",
    businessModel: "subscription",
    businessModelDetail: "SaaS订阅模式，按座席数收费。基础版299元/座席/月，专业版599元/座席/月，企业版1299元/座席/月。",
    timeframe: "3-6个月",
    expectedStartDate: "2024-08-01T00:00:00.000Z",
    legalEntity: "registered",
    legalEntityDetail: "上海智语科技有限公司，注册资本300万元，已获得软件著作权和发明专利。",
    demoLinks: [
      { type: "demo", name: "在线体验", url: "https://demo.ai-service.com", enabled: true }
    ],
    mainImage: "https://picsum.photos/800/400?random=2",
    images: [
      { name: "对话界面", url: "https://via.placeholder.com/200x150?text=对话界面" },
      { name: "管理后台", url: "https://via.placeholder.com/200x150?text=管理后台" },
      { name: "数据分析", url: "https://via.placeholder.com/200x150?text=数据分析" }
    ],
    meta: {
      timeframe: "3-6个月",
      teamSize: 6,
      currentTeamSize: 2,
      workLocation: "hybrid",
      workArrangement: "fullTime",
      location: "上海市浦东新区",
      expectedStartDate: "2024-08-01T00:00:00.000Z"
    },
    progress: 60,
    currentMilestone: "testing",
    milestoneDescription: "正在进行大规模测试，预计1个月内正式上线。",
    milestones: [
      { id: "team-building", name: "团队组建", status: "completed" },
      { id: "requirement-design", name: "需求设计", status: "completed" },
      { id: "development", name: "开发实现", status: "completed" },
      { id: "testing-launch", name: "测试上线", status: "current" }
    ],
    links: [
      { type: "website", name: "官方网站", url: "https://ai-service.com", enabled: true },
      { type: "demo", name: "产品演示", url: "https://demo.ai-service.com", enabled: true },
      { type: "document", name: "产品文档", url: "#", enabled: true }
    ],
    teamMembers: [
      {
        id: 3,
        name: "王总",
        role: "创始人 / CEO",
        avatar: "https://via.placeholder.com/80x80?text=王",
        skills: ["产品策略", "商务拓展", "团队管理"],
        isOwner: true
      },
      {
        id: 4,
        name: "刘博士",
        role: "AI算法专家",
        avatar: "https://via.placeholder.com/80x80?text=刘",
        skills: ["机器学习", "自然语言处理", "算法优化"],
        isOwner: false
      }
    ],
    requirements: [
      {
        role: "前端工程师",
        skillName: ["React", "TypeScript", "Ant Design"],
        cooperation: "salary_equity",
        salaryAmount: "20K-30K",
        equityAmount: 1.5
      },
      {
        role: "后端工程师",
        skillName: ["Python", "Django", "Redis", "MySQL"],
        cooperation: "salary_equity",
        salaryAmount: "25K-35K",
        equityAmount: 2.0
      }
    ],
    highlights: "AI技术领先，已有客户验证，市场需求旺盛。团队经验丰富，技术壁垒高。",
    paymentType: "milestone",
    owner: {
      id: 3,
      name: "王总",
      title: "创始人 / CEO",
      bio: "前阿里巴巴产品总监，10年企业服务经验，深度理解企业客服痛点和AI技术应用。",
      avatar: "https://via.placeholder.com/80x80?text=王",
      rating: 4.9,
      projectCount: 2,
      completedProjects: 1,
      skills: ["产品策略", "商务拓展", "团队管理"]
    },
    stats: {
      viewCount: 1500,
      applicationCount: 8,
      progress: 60,
      daysPublished: 45
    },
    createdAt: "2024-12-01",
    updatedAt: "2024-12-15",
    budget: 1200000,
    urgency: "medium",
    breadcrumb: [
      { name: "项目精选", path: "/projects" },
      { name: "AI/机器学习", path: "/projects?category=ai" },
      { name: "AI智能客服系统", path: "" }
    ]
  },
  {
    id: 3,
    title: "跨境电商SaaS平台",
    tagline: "一站式跨境电商解决方案",
    category: "ecommerce",
    status: "recruiting", 
    featured: false,
    description: "为中小企业提供跨境电商一站式解决方案，包括店铺管理、订单处理、库存管理、物流跟踪、财务结算等功能。支持主流跨境电商平台对接。",
    projectOrigin: "创始人在跨境电商行业深耕5年，发现中小卖家缺乏高效的管理工具，大部分还在用Excel管理业务，效率低下且容易出错。",
    projectAdvantage: "深度理解跨境电商业务流程，产品功能贴合实际需求。已有20+客户试用，反馈良好。技术架构先进，支持快速扩展。",
    userAnalysis: "目标用户是年销售额100万-5000万的跨境电商卖家，痛点是缺乏专业的ERP系统，业务管理效率低。",
    existingTeam: [
      {
        name: "陈总",
        role: "创始人",
        background: "5年跨境电商经验，前Amazon头部卖家",
        introduction: "深度理解跨境电商痛点"
      }
    ],
    teamCommitment: "创始人全职投入，已投入自有资金200万元进行产品开发，目前产品已完成70%功能开发。",
    workLocation: "hybrid",
    workArrangement: "fullTime",
    location: "深圳市南山区",
    recentProgress: "完成了核心ERP功能开发，对接了Amazon、eBay、Shopify等主流平台API。已有20家客户开始试用，月活跃用户增长30%。",
    businessModel: "subscription",
    businessModelDetail: "SaaS订阅模式，按店铺数收费。基础版199元/店铺/月，专业版399元/店铺/月，企业版799元/店铺/月。",
    timeframe: "3-6个月",
    expectedStartDate: "2024-09-01T00:00:00.000Z",
    legalEntity: "registered",
    legalEntityDetail: "深圳跨境云科技有限公司，注册资本200万元，已获得高新技术企业认定。",
    demoLinks: [],
    mainImage: "https://picsum.photos/800/400?random=3",
    images: [
      { name: "店铺管理", url: "https://via.placeholder.com/200x150?text=店铺管理" },
      { name: "订单中心", url: "https://via.placeholder.com/200x150?text=订单中心" },
      { name: "库存管理", url: "https://via.placeholder.com/200x150?text=库存管理" }
    ],
    meta: {
      timeframe: "3-6个月",
      teamSize: 5,
      currentTeamSize: 1,
      workLocation: "hybrid",
      workArrangement: "fullTime",
      location: "深圳市南山区",
      expectedStartDate: "2024-09-01T00:00:00.000Z"
    },
    progress: 70,
    currentMilestone: "development",
    milestoneDescription: "正在完善高级功能，预计2个月内完成全部开发。",
    milestones: [
      { id: "team-building", name: "团队组建", status: "completed" },
      { id: "requirement-design", name: "需求设计", status: "completed" },
      { id: "development", name: "开发实现", status: "current" },
      { id: "testing-launch", name: "测试上线", status: "pending" }
    ],
    links: [
      { type: "website", name: "官方网站", url: "", enabled: false },
      { type: "document", name: "产品介绍", url: "#", enabled: true },
      { type: "research", name: "行业报告", url: "#", enabled: true }
    ],
    teamMembers: [
      {
        id: 5,
        name: "陈总",
        role: "创始人 / CEO",
        avatar: "https://via.placeholder.com/80x80?text=陈",
        skills: ["跨境电商", "产品规划", "商务拓展"],
        isOwner: true
      }
    ],
    requirements: [
      {
        role: "全栈工程师",
        skillName: ["Vue.js", "Node.js", "MySQL", "Redis"],
        cooperation: "salary_equity",
        salaryAmount: "22K-32K",
        equityAmount: 3.0
      },
      {
        role: "产品经理",
        skillName: ["产品设计", "用户研究", "项目管理"],
        cooperation: "salary_equity", 
        salaryAmount: "18K-28K",
        equityAmount: 2.0
      }
    ],
    highlights: "市场需求明确，已有客户验证。创始人行业经验丰富，产品方向清晰。",
    paymentType: "milestone",
    owner: {
      id: 5,
      name: "陈总",
      title: "创始人 / CEO",
      bio: "5年跨境电商经验，前Amazon头部卖家，深度理解跨境电商痛点和技术需求。",
      avatar: "https://via.placeholder.com/80x80?text=陈",
      rating: 4.7,
      projectCount: 1,
      completedProjects: 0,
      skills: ["跨境电商", "产品规划", "商务拓展"]
    },
    stats: {
      viewCount: 800,
      applicationCount: 5,
      progress: 70,
      daysPublished: 30
    },
    createdAt: "2024-12-15",
    updatedAt: "2024-12-15",
    budget: 600000,
    urgency: "medium",
    breadcrumb: [
      { name: "项目精选", path: "/projects" },
      { name: "电商平台", path: "/projects?category=ecommerce" },
      { name: "跨境电商SaaS平台", path: "" }
    ]
  }
];

console.log('正在创建示例项目数据...');
console.log(`创建了 ${projects.length} 个项目:`);
projects.forEach(project => {
  console.log(`- ${project.title} (ID: ${project.id})`);
});

// 将数据写入JSON文件
const outputPath = path.join(__dirname, 'sample-projects-new.json');
fs.writeFileSync(outputPath, JSON.stringify(projects, null, 2), 'utf8');
console.log(`\n数据已保存到: ${outputPath}`);
console.log('请将此文件内容替换到 backend/sample-projects.json 中'); 