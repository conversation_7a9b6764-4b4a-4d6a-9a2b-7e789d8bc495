// 测试认证API的脚本
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000';

// 测试发送验证码
async function testSendVerificationCode() {
  console.log('\n🧪 测试发送验证码API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        type: 'registration'
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 验证码发送成功');
    } else {
      console.log('❌ 验证码发送失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 测试用户登录
async function testUserLogin() {
  console.log('\n🧪 测试用户登录API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123@'
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 用户登录成功');
      console.log('Token:', data.data.token.substring(0, 50) + '...');
    } else {
      console.log('❌ 用户登录失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 测试注册用户
async function testUserRegister() {
  console.log('\n🧪 测试用户注册API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123@',
        verificationCode: '123456'  // 这里需要实际的验证码
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 用户注册成功');
    } else {
      console.log('❌ 用户注册失败 (需要有效验证码)');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 运行所有测试
async function runTests() {
  console.log('🚀 开始API测试...');
  
  await testSendVerificationCode();
  await testUserLogin();
  await testUserRegister();
  
  console.log('\n✨ 测试完成');
}

runTests();