import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function approveProject() {
  try {
    const project = await prisma.project.update({
      where: { id: 1 },
      data: {
        reviewStatus: 'approved',
        status: 'recruiting'
      }
    });
    
    console.log('项目审核通过:', project.title);
    console.log('项目状态:', project.reviewStatus, project.status);
  } catch (error) {
    console.error('审核失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

approveProject();