import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 8080;

// 基础中间件
app.use(cors());
app.use(express.json());

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'DevMatch API 运行正常',
    timestamp: new Date().toISOString()
  });
});

// 测试发送验证码
app.post('/api/auth/send-verification-code', (req, res) => {
  console.log('收到验证码请求:', req.body);
  res.json({
    success: true,
    message: '验证码发送成功（测试）'
  });
});

// 测试登录
app.post('/api/auth/login', (req, res) => {
  console.log('收到登录请求:', req.body);
  res.json({
    success: true,
    data: {
      token: 'test-token',
      user: {
        id: 1,
        username: 'testuser',
        email: req.body.email,
        role: 'developer'
      }
    },
    message: '登录成功'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动成功! 端口: ${PORT}`);
  console.log(`📍 后端服务: http://localhost:${PORT}`);
  console.log(`💊 健康检查: http://localhost:${PORT}/api/health`);
});