import { ProposalModel, DAOMemberModel } from './src/models/dao.js'

async function generateTestData() {
  try {
    console.log('开始生成DAO测试数据...')
    
    // 1. 确保用户1是DAO成员
    try {
      const daoMemberModel = new DAOMemberModel()
      const result = await daoMemberModel.addDAOMember(1, 'dao_member')
      if (result.success) {
        console.log('✓ 用户1已设置为DAO成员')
      } else {
        console.log('✓ 用户1已经是DAO成员')
      }
    } catch (error) {
      console.error('设置DAO成员失败:', error.message)
    }
    
    // 2. 清空现有测试数据
    await ProposalModel.clearTestData()
    console.log('✓ 已清空现有测试数据')
    
    // 3. 生成新的测试提案
    const proposals = await ProposalModel.generateTestData()
    console.log(`✓ 已生成 ${proposals.length} 个测试提案:`)
    
    proposals.forEach((proposal, index) => {
      console.log(`  ${index + 1}. ${proposal.title} [${proposal.status}]`)
    })
    
    console.log('\n🎉 DAO测试数据生成完成！')
    console.log('现在可以在前端查看这些提案数据。')
    
  } catch (error) {
    console.error('❌ 生成测试数据失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTestData()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
}

export default generateTestData 