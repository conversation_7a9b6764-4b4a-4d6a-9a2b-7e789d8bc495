// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id          Int      @id @default(autoincrement())
  username    String?  @unique @db.VarChar(50)
  email       String   @unique @db.VarChar(100)
  passwordHash String  @map("password_hash") @db.VarChar(255)
  role        String   @default("developer") @db.VarChar(20)
  avatarUrl   String?  @map("avatar_url") @db.Text
  bio         String?  @db.Text
  location    String?  @db.VarChar(100)
  githubUrl   String?  @map("github_url") @db.VarChar(255)
  bilibiliUrl String?  @map("bilibili_url") @db.VarChar(255)
  portfolioUrl String? @map("portfolio_url") @db.VarChar(255)
  
  // 个人信息字段
  gender      String?  @db.VarChar(20) // male, female, private
  phone       String?  @db.VarChar(20)
  wechat      String?  @db.VarChar(50)
  profession  String?  @db.VarChar(100)
  
  // 工作偏好字段
  workType    String?  @map("work_type") @db.VarChar(20) // remote, onsite, hybrid
  workTime    String?  @map("work_time") @db.VarChar(20) // everyday, weekends_only, evenings_weekends
  cooperationMode String? @map("cooperation_mode") @db.VarChar(30) // salary_required, profit_sharing_only, both_acceptable
  
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  userSkills     UserSkill[]
  projects       Project[]
  applications   Application[]
  teamMembers    TeamMember[]
  sentMessages   Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")
  reviewedProjects Project[] @relation("ProjectReviewer")
  
  // DAO相关关联
  proposalsCreated DaoProposal[] @relation("ProposalCreator")
  votes           DaoVote[] @relation("UserVotes")
  comments        DaoComment[] @relation("UserComments")
  daoMembership   DaoMember? @relation("DaoMembership")
  
  // 密码重置令牌关联
  passwordResetTokens PasswordResetToken[] @relation("UserPasswordResetTokens")

  @@map("users")
}

// 技能表
model Skill {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(50)
  category  String   @db.VarChar(30)
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  userSkills         UserSkill[]
  projectRequirements ProjectRequirement[]

  @@map("skills")
}

// 用户技能关联表
model UserSkill {
  id        Int      @id @default(autoincrement())
  userId    Int      @map("user_id")
  skillId   Int      @map("skill_id")
  level     String   @db.VarChar(20) // beginner(<1年), skilled(1-3年), senior(3-5年), expert(>5年)
  createdAt DateTime @default(now()) @map("created_at")

  // 外键关联
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  skill Skill @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@unique([userId, skillId])
  @@map("user_skills")
}

// 项目表（扩展版本，支持发布审核流程）
model Project {
  id               Int      @id @default(autoincrement())
  title            String   @db.VarChar(200)
  summary          String   @db.VarChar(100) // 一句话概括
  description      String   @db.Text
  category         String   @db.VarChar(50)
  status           String   @default("draft") @db.VarChar(20) // draft, pending_review, approved, rejected, recruiting, paused, completed
  creatorId        Int      @map("creator_id")
  
  // 审核相关
  reviewStatus     String?  @default("pending") @map("review_status") @db.VarChar(20) // pending, approved, rejected
  reviewerId       Int?     @map("reviewer_id")
  reviewMessage    String?  @map("review_message") @db.Text
  reviewedAt       DateTime? @map("reviewed_at")
  
  // 精选项目
  featured         Boolean  @default(false) // 是否为精选项目
  
  // 项目基本信息
  budgetRange      String?  @map("budget_range") @db.VarChar(50)
  demoUrl          String?  @map("demo_url") @db.Text
  teamSize         Int      @default(1) @map("team_size")
  durationMonths   Int?     @map("duration_months")
  workType         String?  @map("work_type") @db.VarChar(20) // remote, onsite, hybrid
  workLocation     String?  @map("work_location") @db.VarChar(100)
  workArrangement  String?  @map("work_arrangement") @db.VarChar(20) // fullTime, partTime, contract, intern
  
  // 团队和招募信息
  teamInfo         Json?    @map("team_info") // 现有团队信息
  teamInvestment   String?  @map("team_investment") @db.Text // 团队投入情况
  recruitmentInfo  Json?    @map("recruitment_info") // 招募需求信息
  
  // 项目分析信息
  recentProgress   String?  @map("recent_progress") @db.Text // 最近进展
  userAnalysis     String?  @map("user_analysis") @db.Text // 用户分析
  projectOrigin    String?  @map("project_origin") @db.Text // 项目起源
  competitiveAdvantage String? @map("competitive_advantage") @db.Text // 竞争优势
  businessModel    String?  @map("business_model") @db.VarChar(50) // 盈利模式
  businessDescription String? @map("business_description") @db.Text // 商业模式详述

  // 公司注册信息
  isCompanyRegistered Boolean? @default(false) @map("is_company_registered") // 是否已注册公司
  companyName      String?  @map("company_name") @db.VarChar(200) // 公司名称
  
  // 其他信息
  compensationType String?  @map("compensation_type") @db.VarChar(30) // salary, equity, mixed, profit
  prototypeImages  Json?    @map("prototype_images") // 项目图片/原型图
  websiteUrl       String?  @map("website_url") @db.Text
  appUrl           String?  @map("app_url") @db.Text
  
  // 时间戳
  submittedAt      DateTime? @map("submitted_at") // 提交审核时间
  publishedAt      DateTime? @map("published_at") // 发布时间
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 外键关联
  creator User @relation(fields: [creatorId], references: [id], onDelete: Cascade)
  reviewer User? @relation("ProjectReviewer", fields: [reviewerId], references: [id], onDelete: SetNull)

  // 关联关系
  projectRequirements ProjectRequirement[]
  applications        Application[]
  teamMembers         TeamMember[]
  messages            Message[]

  @@map("projects")
}

// 项目技能需求表
model ProjectRequirement {
  id         Int      @id @default(autoincrement())
  projectId  Int      @map("project_id")
  skillId    Int      @map("skill_id")
  level      String   @db.VarChar(20)
  isRequired Boolean  @default(true) @map("is_required")
  createdAt  DateTime @default(now()) @map("created_at")

  // 外键关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  skill   Skill   @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@unique([projectId, skillId])
  @@map("project_requirements")
}

// 申请表
model Application {
  id          Int       @id @default(autoincrement())
  projectId   Int       @map("project_id")
  applicantId Int       @map("applicant_id")
  message     String?   @db.Text
  status      String    @default("pending") @db.VarChar(20)
  appliedAt   DateTime  @default(now()) @map("applied_at")
  respondedAt DateTime? @map("responded_at")

  // 外键关联
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  applicant User    @relation(fields: [applicantId], references: [id], onDelete: Cascade)

  @@unique([projectId, applicantId])
  @@map("applications")
}

// 团队成员表
model TeamMember {
  id        Int      @id @default(autoincrement())
  projectId Int      @map("project_id")
  userId    Int      @map("user_id")
  role      String   @db.VarChar(50)
  joinedAt  DateTime @default(now()) @map("joined_at")
  status    String   @default("active") @db.VarChar(20)

  // 外键关联
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("team_members")
}

// 消息表
model Message {
  id          Int      @id @default(autoincrement())
  senderId    Int      @map("sender_id")
  receiverId  Int      @map("receiver_id")
  projectId   Int?     @map("project_id")
  subject     String   @db.VarChar(200)
  content     String   @db.Text
  messageType String   @default("discussion") @map("message_type") @db.VarChar(20) // invitation, discussion, system
  isRead      Boolean  @default(false) @map("is_read")
  createdAt   DateTime @default(now()) @map("created_at")

  // 外键关联
  sender    User     @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiver  User     @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)
  project   Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)

  @@map("messages")
}

// DAO提案表
model DaoProposal {
  id        String   @id @db.VarChar(50)
  title     String   @db.VarChar(200)
  category  String   @db.VarChar(50)
  content   String   @db.Text
  evidence  String?  @db.Text
  impact    String?  @db.VarChar(50)
  status    String   @default("reviewing") @db.VarChar(20) // reviewing, approved, rejected, voting, completed
  creatorId Int      @map("creator_id")
  deadline  DateTime
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 外键关联
  creator User @relation("ProposalCreator", fields: [creatorId], references: [id], onDelete: Cascade)

  // 关联关系
  votes    DaoVote[]
  comments DaoComment[]

  @@map("dao_proposals")
}

// DAO投票表
model DaoVote {
  id         Int      @id @default(autoincrement())
  proposalId String   @map("proposal_id") @db.VarChar(50)
  userId     Int      @map("user_id")
  voteType   String   @map("vote_type") @db.VarChar(20) // support, oppose, abstain
  weight     Int      @default(1)
  reason     String?  @db.Text
  createdAt  DateTime @default(now()) @map("created_at")

  // 外键关联
  proposal DaoProposal @relation(fields: [proposalId], references: [id], onDelete: Cascade)
  user     User        @relation("UserVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([proposalId, userId])
  @@map("dao_votes")
}

// DAO评论表
model DaoComment {
  id         Int      @id @default(autoincrement())
  proposalId String   @map("proposal_id") @db.VarChar(50)
  userId     Int      @map("user_id")
  content    String   @db.Text
  parentId   Int?     @map("parent_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 外键关联
  proposal DaoProposal @relation(fields: [proposalId], references: [id], onDelete: Cascade)
  user     User        @relation("UserComments", fields: [userId], references: [id], onDelete: Cascade)
  parent   DaoComment? @relation("CommentReplies", fields: [parentId], references: [id], onDelete: SetNull)

  // 关联关系
  replies DaoComment[] @relation("CommentReplies")

  @@map("dao_comments")
}

// DAO成员表
model DaoMember {
  id             Int      @id @default(autoincrement())
  userId         Int      @unique @map("user_id")
  membershipType String   @default("dao_member") @map("membership_type") @db.VarChar(50)
  status         String   @default("active") @db.VarChar(20) // active, inactive, suspended
  votingPower    Int      @default(1) @map("voting_power")
  joinedAt       DateTime @default(now()) @map("joined_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 外键关联
  user User @relation("DaoMembership", fields: [userId], references: [id], onDelete: Cascade)

  @@map("dao_members")
}

// 邮箱验证码表
model EmailVerification {
  id        String   @id @default(cuid())
  email     String   @db.VarChar(100)
  code      String   @db.VarChar(6)
  type      String   @db.VarChar(20) // registration, password_reset, email_verification
  expiresAt DateTime @map("expires_at")
  isUsed    Boolean  @default(false) @map("is_used")
  createdAt DateTime @default(now()) @map("created_at")
  
  @@map("email_verifications")
}

// 密码重置令牌表
model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String   @db.VarChar(100)
  token     String   @db.VarChar(100)
  userId    Int      @map("user_id")
  isUsed    Boolean  @default(false) @map("is_used")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  
  // 外键关联
  user User @relation("UserPasswordResetTokens", fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("password_reset_tokens")
} 