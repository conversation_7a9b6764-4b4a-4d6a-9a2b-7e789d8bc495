import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始数据库种子数据初始化...');

  // 清理现有数据（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.log('🧹 清理现有数据...');
    await prisma.message.deleteMany();
    await prisma.teamMember.deleteMany();
    await prisma.application.deleteMany();
    await prisma.projectRequirement.deleteMany();
    await prisma.userSkill.deleteMany();
    await prisma.project.deleteMany();
    await prisma.user.deleteMany();
    await prisma.skill.deleteMany();
  }

  // 创建技能数据
  console.log('📋 创建技能数据...');
  const skills = await Promise.all([
    // 前端技能
    prisma.skill.create({ data: { name: 'Vue.js', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'React', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'Angular', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'TypeScript', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'JavaScript', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'HTML/CSS', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'Tailwind CSS', category: 'frontend' } }),
    prisma.skill.create({ data: { name: 'Sass/SCSS', category: 'frontend' } }),

    // 后端技能
    prisma.skill.create({ data: { name: 'Node.js', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Express.js', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Nest.js', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Python', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Django', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'FastAPI', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Java', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Spring Boot', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Go', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Gin', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'PHP', category: 'backend' } }),
    prisma.skill.create({ data: { name: 'Laravel', category: 'backend' } }),

    // 移动端技能
    prisma.skill.create({ data: { name: 'React Native', category: 'mobile' } }),
    prisma.skill.create({ data: { name: 'Flutter', category: 'mobile' } }),
    prisma.skill.create({ data: { name: 'Swift', category: 'mobile' } }),
    prisma.skill.create({ data: { name: 'Kotlin', category: 'mobile' } }),
    prisma.skill.create({ data: { name: 'UniApp', category: 'mobile' } }),
    prisma.skill.create({ data: { name: '微信小程序', category: 'mobile' } }),

    // DevOps技能
    prisma.skill.create({ data: { name: 'Docker', category: 'devops' } }),
    prisma.skill.create({ data: { name: 'Kubernetes', category: 'devops' } }),
    prisma.skill.create({ data: { name: 'AWS', category: 'devops' } }),
    prisma.skill.create({ data: { name: '腾讯云', category: 'devops' } }),
    prisma.skill.create({ data: { name: '阿里云', category: 'devops' } }),
    prisma.skill.create({ data: { name: 'Nginx', category: 'devops' } }),
    prisma.skill.create({ data: { name: 'CI/CD', category: 'devops' } }),

    // AI技能
    prisma.skill.create({ data: { name: 'Machine Learning', category: 'ai' } }),
    prisma.skill.create({ data: { name: 'Deep Learning', category: 'ai' } }),
    prisma.skill.create({ data: { name: 'TensorFlow', category: 'ai' } }),
    prisma.skill.create({ data: { name: 'PyTorch', category: 'ai' } }),
    prisma.skill.create({ data: { name: 'Computer Vision', category: 'ai' } }),
    prisma.skill.create({ data: { name: 'NLP', category: 'ai' } }),

    // 设计技能
    prisma.skill.create({ data: { name: 'UI设计', category: 'design' } }),
    prisma.skill.create({ data: { name: 'UX设计', category: 'design' } }),
    prisma.skill.create({ data: { name: 'Figma', category: 'design' } }),
    prisma.skill.create({ data: { name: 'Sketch', category: 'design' } }),
    prisma.skill.create({ data: { name: 'Adobe XD', category: 'design' } }),
    prisma.skill.create({ data: { name: 'Photoshop', category: 'design' } }),

    // 产品技能
    prisma.skill.create({ data: { name: '产品设计', category: 'product' } }),
    prisma.skill.create({ data: { name: '需求分析', category: 'product' } }),
    prisma.skill.create({ data: { name: '用户研究', category: 'product' } }),
    prisma.skill.create({ data: { name: '数据分析', category: 'product' } }),
    prisma.skill.create({ data: { name: '项目管理', category: 'product' } }),

    // 运营技能
    prisma.skill.create({ data: { name: '内容运营', category: 'marketing' } }),
    prisma.skill.create({ data: { name: '用户运营', category: 'marketing' } }),
    prisma.skill.create({ data: { name: '市场推广', category: 'marketing' } }),
    prisma.skill.create({ data: { name: 'SEO/SEM', category: 'marketing' } }),
    prisma.skill.create({ data: { name: '社群运营', category: 'marketing' } }),
  ]);

  console.log(`✅ 创建了 ${skills.length} 个技能`);

  // 创建测试用户
  console.log('👥 创建测试用户...');
  const passwordHash = await bcrypt.hash('Password123', 12);

  const users = await Promise.all([
    prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        passwordHash,
        role: 'admin',
        bio: '平台管理员',
        location: '北京',
        githubUrl: 'https://github.com/admin',
      }
    }),
    prisma.user.create({
      data: {
        username: 'zhangsan',
        email: '<EMAIL>',
        passwordHash,
        role: 'developer',
        bio: '全栈开发工程师，熟悉Vue.js和Node.js技术栈',
        location: '上海',
        githubUrl: 'https://github.com/zhangsan',
      }
    }),
    prisma.user.create({
      data: {
        username: 'lisi',
        email: '<EMAIL>',
        passwordHash,
        role: 'developer',
        bio: 'UI/UX设计师，专注于用户体验设计',
        location: '深圳',
        portfolioUrl: 'https://lisi.design',
      }
    }),
    prisma.user.create({
      data: {
        username: 'wangwu',
        email: '<EMAIL>',
        passwordHash,
        role: 'developer',
        bio: '产品经理，有5年B端产品经验',
        location: '杭州',
      }
    })
  ]);

  console.log(`✅ 创建了 ${users.length} 个用户`);

  // 为用户添加技能
  console.log('🎯 为用户添加技能...');
  await Promise.all([
    // 张三的技能
    prisma.userSkill.create({
      data: {
        userId: users[1].id,
        skillId: skills.find(s => s.name === 'Vue.js').id,
        level: 'senior'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[1].id,
        skillId: skills.find(s => s.name === 'Node.js').id,
        level: 'skilled'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[1].id,
        skillId: skills.find(s => s.name === 'TypeScript').id,
        level: 'skilled'
      }
    }),

    // 李四的技能
    prisma.userSkill.create({
      data: {
        userId: users[2].id,
        skillId: skills.find(s => s.name === 'UI设计').id,
        level: 'expert'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[2].id,
        skillId: skills.find(s => s.name === 'UX设计').id,
        level: 'senior'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[2].id,
        skillId: skills.find(s => s.name === 'Figma').id,
        level: 'expert'
      }
    }),

    // 王五的技能
    prisma.userSkill.create({
      data: {
        userId: users[3].id,
        skillId: skills.find(s => s.name === '产品设计').id,
        level: 'expert'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[3].id,
        skillId: skills.find(s => s.name === '需求分析').id,
        level: 'senior'
      }
    }),
    prisma.userSkill.create({
      data: {
        userId: users[3].id,
        skillId: skills.find(s => s.name === '用户研究').id,
        level: 'skilled'
      }
    }),
  ]);

  // 创建示例项目
  console.log('📦 创建示例项目...');
  const projects = await Promise.all([
    prisma.project.create({
      data: {
        title: '智能任务管理平台',
        summary: '基于AI的智能任务管理平台',
        description: '基于AI的智能任务管理平台，帮助团队提高工作效率。采用Vue3+Node.js技术栈，需要前端、后端和UI设计师。',
        category: 'web',
        status: 'recruiting',
        creatorId: users[1].id,
        teamSize: 4,
        durationMonths: 6,
        workType: 'remote',
        compensationType: 'equity',
        budgetRange: '10-20万',
      }
    }),
    prisma.project.create({
      data: {
        title: '在线教育移动应用',
        summary: '面向K12学生的在线教育App',
        description: '面向K12学生的在线教育移动应用，包含直播课程、作业系统等功能。',
        category: 'mobile',
        status: 'recruiting',
        creatorId: users[3].id,
        teamSize: 5,
        durationMonths: 8,
        workType: 'hybrid',
        compensationType: 'salary_equity',
        budgetRange: '30-50万',
      }
    })
  ]);

  console.log(`✅ 创建了 ${projects.length} 个项目`);

  // 为项目添加技能需求
  console.log('⚡ 为项目添加技能需求...');
  await Promise.all([
    // 智能任务管理平台的需求
    prisma.projectRequirement.create({
      data: {
        projectId: projects[0].id,
        skillId: skills.find(s => s.name === 'Vue.js').id,
        level: 'skilled',
        isRequired: true
      }
    }),
    prisma.projectRequirement.create({
      data: {
        projectId: projects[0].id,
        skillId: skills.find(s => s.name === 'Node.js').id,
        level: 'skilled',
        isRequired: true
      }
    }),
    prisma.projectRequirement.create({
      data: {
        projectId: projects[0].id,
        skillId: skills.find(s => s.name === 'UI设计').id,
        level: 'skilled',
        isRequired: true
      }
    }),

    // 在线教育移动应用的需求
    prisma.projectRequirement.create({
      data: {
        projectId: projects[1].id,
        skillId: skills.find(s => s.name === 'React Native').id,
        level: 'senior',
        isRequired: true
      }
    }),
    prisma.projectRequirement.create({
      data: {
        projectId: projects[1].id,
        skillId: skills.find(s => s.name === 'Node.js').id,
        level: 'skilled',
        isRequired: true
      }
    }),
    prisma.projectRequirement.create({
      data: {
        projectId: projects[1].id,
        skillId: skills.find(s => s.name === 'UI设计').id,
        level: 'senior',
        isRequired: true
      }
    }),
  ]);

  console.log('🎉 数据库种子数据初始化完成！');
  console.log('\n📊 初始化数据统计:');
  console.log(`- 用户: ${users.length} 个`);
  console.log(`- 技能: ${skills.length} 个`);
  console.log(`- 项目: ${projects.length} 个`);
  console.log('\n🔑 测试账户信息:');
  console.log('管理员: <EMAIL> / Password123');
  console.log('开发者: <EMAIL> / Password123');
  console.log('设计师: <EMAIL> / Password123');
  console.log('产品经理: <EMAIL> / Password123');
}

main()
  .catch((e) => {
    console.error('❌ 种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 