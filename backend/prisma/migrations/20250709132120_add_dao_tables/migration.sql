/*
  Warnings:

  - You are about to drop the column `prd_document_url` on the `projects` table. All the data in the column will be lost.
  - Added the required column `summary` to the `projects` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "projects" DROP COLUMN "prd_document_url",
ADD COLUMN     "business_description" TEXT,
ADD COLUMN     "business_model" VARCHAR(50),
ADD COLUMN     "competitive_advantage" TEXT,
ADD COLUMN     "demo_url" TEXT,
ADD COLUMN     "project_origin" TEXT,
ADD COLUMN     "published_at" TIMESTAMP(3),
ADD COLUMN     "recent_progress" TEXT,
ADD COLUMN     "recruitment_info" JSONB,
ADD COLUMN     "review_message" TEXT,
ADD COLUMN     "review_status" VARCHAR(20) DEFAULT 'pending',
ADD COLUMN     "reviewed_at" TIMESTAMP(3),
ADD COLUMN     "reviewer_id" INTEGER,
ADD COLUMN     "submitted_at" TIMESTAMP(3),
ADD COLUMN     "summary" VARCHAR(100) NOT NULL,
ADD COLUMN     "team_info" JSONB,
ADD COLUMN     "team_investment" TEXT,
ADD COLUMN     "user_analysis" TEXT,
ADD COLUMN     "work_arrangement" VARCHAR(20),
ADD COLUMN     "work_location" VARCHAR(100),
ALTER COLUMN "status" SET DEFAULT 'draft',
ALTER COLUMN "team_size" SET DEFAULT 1;

-- CreateTable
CREATE TABLE "dao_proposals" (
    "id" VARCHAR(50) NOT NULL,
    "title" VARCHAR(200) NOT NULL,
    "category" VARCHAR(50) NOT NULL,
    "content" TEXT NOT NULL,
    "evidence" TEXT,
    "impact" VARCHAR(50),
    "status" VARCHAR(20) NOT NULL DEFAULT 'reviewing',
    "creator_id" INTEGER NOT NULL,
    "deadline" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dao_proposals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dao_votes" (
    "id" SERIAL NOT NULL,
    "proposal_id" VARCHAR(50) NOT NULL,
    "user_id" INTEGER NOT NULL,
    "vote_type" VARCHAR(20) NOT NULL,
    "weight" INTEGER NOT NULL DEFAULT 1,
    "reason" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "dao_votes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dao_comments" (
    "id" SERIAL NOT NULL,
    "proposal_id" VARCHAR(50) NOT NULL,
    "user_id" INTEGER NOT NULL,
    "content" TEXT NOT NULL,
    "parent_id" INTEGER,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dao_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "dao_members" (
    "id" SERIAL NOT NULL,
    "user_id" INTEGER NOT NULL,
    "membership_type" VARCHAR(50) NOT NULL DEFAULT 'dao_member',
    "status" VARCHAR(20) NOT NULL DEFAULT 'active',
    "voting_power" INTEGER NOT NULL DEFAULT 1,
    "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "dao_members_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "dao_votes_proposal_id_user_id_key" ON "dao_votes"("proposal_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "dao_members_user_id_key" ON "dao_members"("user_id");

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_reviewer_id_fkey" FOREIGN KEY ("reviewer_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_proposals" ADD CONSTRAINT "dao_proposals_creator_id_fkey" FOREIGN KEY ("creator_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_votes" ADD CONSTRAINT "dao_votes_proposal_id_fkey" FOREIGN KEY ("proposal_id") REFERENCES "dao_proposals"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_votes" ADD CONSTRAINT "dao_votes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_comments" ADD CONSTRAINT "dao_comments_proposal_id_fkey" FOREIGN KEY ("proposal_id") REFERENCES "dao_proposals"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_comments" ADD CONSTRAINT "dao_comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_comments" ADD CONSTRAINT "dao_comments_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "dao_comments"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "dao_members" ADD CONSTRAINT "dao_members_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
