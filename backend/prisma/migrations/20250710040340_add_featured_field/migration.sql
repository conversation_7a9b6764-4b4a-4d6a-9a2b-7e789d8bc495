/*
  Warnings:

  - You are about to drop the `dao_comments` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `dao_members` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `dao_proposals` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `dao_votes` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "dao_comments" DROP CONSTRAINT "dao_comments_parent_id_fkey";

-- DropForeignKey
ALTER TABLE "dao_comments" DROP CONSTRAINT "dao_comments_proposal_id_fkey";

-- DropForeignKey
ALTER TABLE "dao_comments" DROP CONSTRAINT "dao_comments_user_id_fkey";

-- DropForeignKey
ALTER TABLE "dao_members" DROP CONSTRAINT "dao_members_user_id_fkey";

-- DropForeignKey
ALTER TABLE "dao_proposals" DROP CONSTRAINT "dao_proposals_creator_id_fkey";

-- DropFore<PERSON><PERSON>ey
ALTER TABLE "dao_votes" DROP CONSTRAINT "dao_votes_proposal_id_fkey";

-- DropForeignKey
ALTER TABLE "dao_votes" DROP CONSTRAINT "dao_votes_user_id_fkey";

-- AlterTable
ALTER TABLE "projects" ADD COLUMN     "featured" BOOLEAN NOT NULL DEFAULT false;

-- DropTable
DROP TABLE "dao_comments";

-- DropTable
DROP TABLE "dao_members";

-- DropTable
DROP TABLE "dao_proposals";

-- DropTable
DROP TABLE "dao_votes";
