import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const prisma = new PrismaClient();

async function updateTestUserPassword() {
  try {
    console.log('🔧 开始更新测试用户密码...');
    
    // 使用与注册服务相同的12 salt rounds加密密码
    const passwordHash = await bcrypt.hash('password123', 12);
    
    // 更新测试用户密码
    const user = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { passwordHash }
    });
    
    console.log('✅ 测试用户密码更新成功:');
    console.log(`   用户名: ${user.username}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   密码: password123`);
    console.log(`   ID: ${user.id}`);
    
  } catch (error) {
    console.error('❌ 更新测试用户密码失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTestUserPassword();