{"name": "file-stream-rotator", "version": "0.6.1", "description": "Automated stream rotation useful for log files", "main": "FileStreamRotator.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git://github.com/rogerc/file-stream-rotator.git"}, "keywords": ["stream", "express", "restify", "connect", "rotate", "file", "minute", "hourly", "daily", "logrotate"], "author": "<PERSON>", "license": "MIT", "dependencies": {"moment": "^2.29.1"}}