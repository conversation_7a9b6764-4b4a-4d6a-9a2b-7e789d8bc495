import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    // 创建测试用户
    const email = '<EMAIL>';
    const password = 'Test123@';
    
    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });
    
    if (existingUser) {
      console.log('✅ 测试用户已存在:', email);
      return;
    }
    
    // 加密密码
    const passwordHash = await bcrypt.hash(password, 10);
    
    // 创建用户
    const user = await prisma.user.create({
      data: {
        username: 'testuser',
        email,
        passwordHash,
        role: 'developer'
      }
    });
    
    console.log('✅ 测试用户创建成功:');
    console.log('邮箱:', email);
    console.log('密码:', password);
    console.log('用户ID:', user.id);
    
  } catch (error) {
    console.error('❌ 创建测试用户失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();