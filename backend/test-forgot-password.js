// 测试忘记密码功能
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000';

// 测试忘记密码发送验证码
async function testForgotPasswordSendCode() {
  console.log('\n🧪 测试忘记密码发送验证码API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/send-verification-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        type: 'password_reset'
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 密码重置验证码发送成功');
    } else {
      console.log('❌ 密码重置验证码发送失败');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 测试验证码验证
async function testVerifyCode() {
  console.log('\n🧪 测试验证码验证API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/verify-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        code: '123456',  // 需要从邮件获取实际验证码
        type: 'password_reset'
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 验证码验证成功');
    } else {
      console.log('❌ 验证码验证失败 (需要有效验证码)');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 测试密码重置
async function testResetPassword() {
  console.log('\n🧪 测试密码重置API...');
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/reset-password-with-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        code: '123456',  // 需要从邮件获取实际验证码
        password: 'NewTest123@'
      })
    });
    
    const data = await response.json();
    console.log('状态码:', response.status);
    console.log('响应:', data);
    
    if (response.ok) {
      console.log('✅ 密码重置成功');
    } else {
      console.log('❌ 密码重置失败 (需要有效验证码)');
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 运行测试
async function runForgotPasswordTests() {
  console.log('🚀 开始忘记密码功能测试...');
  
  await testForgotPasswordSendCode();
  await testVerifyCode();
  await testResetPassword();
  
  console.log('\n✨ 测试完成');
}

runForgotPasswordTests();