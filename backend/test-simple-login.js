import fetch from 'node-fetch';

const API_BASE = 'http://localhost:8000/api';

async function testSimpleLogin() {
  console.log('🔐 测试简单登录...');
  
  try {
    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    console.log('响应状态:', response.status);
    console.log('响应头:', response.headers.raw());
    
    const text = await response.text();
    console.log('响应内容:', text);
    
    try {
      const data = JSON.parse(text);
      console.log('解析后的JSON:', data);
    } catch (e) {
      console.log('❌ 不是有效的JSON响应');
    }
    
  } catch (error) {
    console.error('请求错误:', error);
  }
}

testSimpleLogin();