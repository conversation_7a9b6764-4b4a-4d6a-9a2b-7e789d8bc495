系统已配置为仅使用数据库数据
系统已配置为仅使用数据库数据
✅ 数据库连接成功
🚀 DevMatch 测试服务器启动成功!
📍 后端服务: http://localhost:8000
💊 健康检查: http://localhost:8000/api/health
📝 数据库: ✅ 真实数据库
🔐 JWT密钥: ✅ 已配置
📊 管理面板: http://localhost:8000/api/admin/dashboard

🔧 可用接口:
  POST /api/auth/register - 用户注册（真实数据库）
  POST /api/auth/login - 用户登录（真实数据库）
  POST /api/projects - 发布项目（待审核状态）
  GET  /api/projects - 项目列表（仅显示审核通过）
  GET  /api/user/projects - 我的项目
  GET  /api/messages - 系统消息
  POST /api/admin/login - 管理员登录
  GET  /api/admin/review/pending - 待审核项目
  POST /api/admin/review/:id/approve - 审核通过
  POST /api/admin/review/:id/reject - 审核拒绝
🕐 启动投票截止时间检查器...
🔍 检查投票截止时间...
📊 当前投票中的提案数量: 0
🔑 JWT decoded: {
  userId: 8,
  email: '<EMAIL>',
  role: 'developer',
  iat: **********,
  exp: **********
}

🔄 正在关闭服务器...
✅ 数据库连接已关闭
