{"name": "devmatch-backend", "version": "1.0.0", "description": "DevMatch独立开发者匹配平台后端API服务", "main": "test-server.js", "type": "module", "scripts": {"start": "node test-server.js", "dev": "node test-server.js"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["nodejs", "express", "api", "devmatch", "developer", "matching", "platform"], "author": "DevMatch Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.9.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"prisma": "^5.9.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}