import axios from 'axios';

const BASE_URL = 'http://localhost:3001/api';

async function testAuth() {
  try {
    console.log('🔧 开始测试认证功能...\n');
    
    // 测试健康检查
    console.log('1. 测试健康检查...');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health`);
      console.log('✅ 健康检查成功:', healthResponse.data.status);
    } catch (error) {
      console.log('❌ 健康检查失败:', error.message);
      return;
    }
    
    // 测试登录
    console.log('\n2. 测试用户登录...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ 登录成功:', loginResponse.data.success);
      console.log('   用户信息:', loginResponse.data.data.user.username);
      console.log('   Token:', loginResponse.data.data.token ? '已生成' : '未生成');
    } catch (error) {
      console.log('❌ 登录失败:', error.response?.data?.message || error.message);
    }
    
    // 测试注册（新用户）
    console.log('\n3. 测试用户注册...');
    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ 注册成功:', registerResponse.data.success);
      console.log('   用户信息:', registerResponse.data.data.user.username);
      console.log('   Token:', registerResponse.data.data.token ? '已生成' : '未生成');
    } catch (error) {
      console.log('❌ 注册失败:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 认证功能测试完成！');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
  }
}

// 等待服务器启动后再测试
setTimeout(testAuth, 3000); 