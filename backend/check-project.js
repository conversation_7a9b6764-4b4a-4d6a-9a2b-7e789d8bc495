import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkProject() {
  try {
    const project = await prisma.project.findUnique({
      where: { id: 1 },
      include: {
        creator: true
      }
    });
    
    console.log('项目详情:', JSON.stringify(project, null, 2));
  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProject();