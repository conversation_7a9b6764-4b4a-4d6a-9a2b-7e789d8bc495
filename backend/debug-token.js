import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

dotenv.config();

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************.wus18-jUszITim09plwpZu4Cwrgn7VwN7EI4IimAfEw';

try {
  const decoded = jwt.verify(token, process.env.JWT_SECRET);
  console.log('解码后的token内容:', decoded);
} catch (error) {
  console.error('JWT解码失败:', error);
}