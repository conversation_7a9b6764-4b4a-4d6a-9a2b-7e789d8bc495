{"environment":"development","level":"info","message":"🚀 DevMatch API服务器启动成功","service":"devmatch-api","timestamp":"2025-07-12 11:53:31"}
{"environment":"development","level":"info","message":"📍 端口: 3001","service":"devmatch-api","timestamp":"2025-07-12 11:53:31"}
{"environment":"development","level":"info","message":"🌍 环境: development","service":"devmatch-api","timestamp":"2025-07-12 11:53:31"}
{"environment":"development","level":"info","message":"📖 API文档: http://localhost:3001/api/docs","service":"devmatch-api","timestamp":"2025-07-12 11:53:31"}
{"environment":"development","level":"info","message":"💊 健康检查: http://localhost:3001/api/health","service":"devmatch-api","timestamp":"2025-07-12 11:53:31"}
{"environment":"development","level":"info","message":"🚀 DevMatch API服务器启动成功","service":"devmatch-api","timestamp":"2025-07-12 11:54:03"}
{"environment":"development","level":"info","message":"📍 端口: 3001","service":"devmatch-api","timestamp":"2025-07-12 11:54:03"}
{"environment":"development","level":"info","message":"🌍 环境: development","service":"devmatch-api","timestamp":"2025-07-12 11:54:03"}
{"environment":"development","level":"info","message":"📖 API文档: http://localhost:3001/api/docs","service":"devmatch-api","timestamp":"2025-07-12 11:54:03"}
{"environment":"development","level":"info","message":"💊 健康检查: http://localhost:3001/api/health","service":"devmatch-api","timestamp":"2025-07-12 11:54:03"}
{"environment":"development","level":"info","message":"🚀 DevMatch API服务器启动成功","service":"devmatch-api","timestamp":"2025-07-12 11:54:22"}
{"environment":"development","level":"info","message":"📍 端口: 8000","service":"devmatch-api","timestamp":"2025-07-12 11:54:22"}
{"environment":"development","level":"info","message":"🌍 环境: development","service":"devmatch-api","timestamp":"2025-07-12 11:54:22"}
{"environment":"development","level":"info","message":"📖 API文档: http://localhost:8000/api/docs","service":"devmatch-api","timestamp":"2025-07-12 11:54:22"}
{"environment":"development","level":"info","message":"💊 健康检查: http://localhost:8000/api/health","service":"devmatch-api","timestamp":"2025-07-12 11:54:22"}
{"0":{"ip":"::1","method":"GET","url":"/api/projects","userAgent":"curl/8.7.1"},"environment":"development","level":"info","message":"请求开始","requestId":"39703649-ca23-498b-9b03-584d313643ee","service":"devmatch-api","timestamp":"2025-07-12 11:56:28"}
{"environment":"development","level":"info","message":"::1 - - [12/Jul/2025:03:56:28 +0000] \"GET /api/projects HTTP/1.1\" 200 599 \"-\" \"curl/8.7.1\"","service":"devmatch-api","timestamp":"2025-07-12 11:56:28"}
{"0":{"contentLength":"599","duration":"27ms","method":"GET","statusCode":200,"url":"/api/projects"},"environment":"development","level":"info","message":"请求完成","requestId":"39703649-ca23-498b-9b03-584d313643ee","service":"devmatch-api","timestamp":"2025-07-12 11:56:28"}
{"0":{"ip":"::1","method":"GET","url":"/api/projects","userAgent":"curl/8.7.1"},"environment":"development","level":"info","message":"请求开始","requestId":"ea08b0a1-69a4-4e2d-abd0-9d7802985874","service":"devmatch-api","timestamp":"2025-07-12 11:56:41"}
{"environment":"development","level":"info","message":"::1 - - [12/Jul/2025:03:56:41 +0000] \"GET /api/projects HTTP/1.1\" 200 599 \"-\" \"curl/8.7.1\"","service":"devmatch-api","timestamp":"2025-07-12 11:56:41"}
{"0":{"contentLength":"599","duration":"6ms","method":"GET","statusCode":200,"url":"/api/projects"},"environment":"development","level":"info","message":"请求完成","requestId":"ea08b0a1-69a4-4e2d-abd0-9d7802985874","service":"devmatch-api","timestamp":"2025-07-12 11:56:41"}
