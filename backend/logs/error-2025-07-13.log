{"environment":"development","level":"error","message":"数据库错误 \nInvalid `prisma.user.findUnique()` invocation:\n\n{\n  where: {\n    id: undefined,\n?   username?: String,\n?   email?: String,\n?   AND?: UserWhereInput | UserWhereInput[],\n?   OR?: UserWhereInput[],\n?   NOT?: UserWhereInput | UserWhereInput[],\n?   passwordHash?: StringFilter | String,\n?   role?: StringFilter | String,\n?   avatarUrl?: StringNullableFilter | String | Null,\n?   bio?: StringNullableFilter | String | Null,\n?   location?: StringNullableFilter | String | Null,\n?   githubUrl?: StringNullableFilter | String | Null,\n?   bilibiliUrl?: StringNullableFilter | String | Null,\n?   portfolioUrl?: StringNullableFilter | String | Null,\n?   createdAt?: DateTimeFilter | DateTime,\n?   updatedAt?: DateTimeFilter | DateTime,\n?   userSkills?: UserSkillListRelationFilter,\n?   projects?: ProjectListRelationFilter,\n?   applications?: ApplicationListRelationFilter,\n?   teamMembers?: TeamMemberListRelationFilter,\n?   sentMessages?: MessageListRelationFilter,\n?   receivedMessages?: MessageListRelationFilter,\n?   reviewedProjects?: ProjectListRelationFilter\n  },\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatarUrl: true,\n    createdAt: true\n  }\n}\n\nArgument `where` of type UserWhereUniqueInput needs at least one of `id`, `username` or `email` arguments. Available options are listed in green.","service":"devmatch-api","target":"user.findUnique","timestamp":"2025-07-13 21:06:16"}
{"environment":"development","level":"error","message":"数据库错误 \nInvalid `prisma.user.findUnique()` invocation:\n\n{\n  where: {\n    id: undefined,\n?   username?: String,\n?   email?: String,\n?   AND?: UserWhereInput | UserWhereInput[],\n?   OR?: UserWhereInput[],\n?   NOT?: UserWhereInput | UserWhereInput[],\n?   passwordHash?: StringFilter | String,\n?   role?: StringFilter | String,\n?   avatarUrl?: StringNullableFilter | String | Null,\n?   bio?: StringNullableFilter | String | Null,\n?   location?: StringNullableFilter | String | Null,\n?   githubUrl?: StringNullableFilter | String | Null,\n?   bilibiliUrl?: StringNullableFilter | String | Null,\n?   portfolioUrl?: StringNullableFilter | String | Null,\n?   createdAt?: DateTimeFilter | DateTime,\n?   updatedAt?: DateTimeFilter | DateTime,\n?   userSkills?: UserSkillListRelationFilter,\n?   projects?: ProjectListRelationFilter,\n?   applications?: ApplicationListRelationFilter,\n?   teamMembers?: TeamMemberListRelationFilter,\n?   sentMessages?: MessageListRelationFilter,\n?   receivedMessages?: MessageListRelationFilter,\n?   reviewedProjects?: ProjectListRelationFilter\n  },\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatarUrl: true,\n    createdAt: true\n  }\n}\n\nArgument `where` of type UserWhereUniqueInput needs at least one of `id`, `username` or `email` arguments. Available options are listed in green.","service":"devmatch-api","target":"user.findUnique","timestamp":"2025-07-13 21:06:51"}
{"environment":"development","level":"error","message":"数据库错误 \nInvalid `prisma.user.findUnique()` invocation:\n\n{\n  where: {\n    id: undefined,\n?   username?: String,\n?   email?: String,\n?   AND?: UserWhereInput | UserWhereInput[],\n?   OR?: UserWhereInput[],\n?   NOT?: UserWhereInput | UserWhereInput[],\n?   passwordHash?: StringFilter | String,\n?   role?: StringFilter | String,\n?   avatarUrl?: StringNullableFilter | String | Null,\n?   bio?: StringNullableFilter | String | Null,\n?   location?: StringNullableFilter | String | Null,\n?   githubUrl?: StringNullableFilter | String | Null,\n?   bilibiliUrl?: StringNullableFilter | String | Null,\n?   portfolioUrl?: StringNullableFilter | String | Null,\n?   createdAt?: DateTimeFilter | DateTime,\n?   updatedAt?: DateTimeFilter | DateTime,\n?   userSkills?: UserSkillListRelationFilter,\n?   projects?: ProjectListRelationFilter,\n?   applications?: ApplicationListRelationFilter,\n?   teamMembers?: TeamMemberListRelationFilter,\n?   sentMessages?: MessageListRelationFilter,\n?   receivedMessages?: MessageListRelationFilter,\n?   reviewedProjects?: ProjectListRelationFilter\n  },\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatarUrl: true,\n    createdAt: true\n  }\n}\n\nArgument `where` of type UserWhereUniqueInput needs at least one of `id`, `username` or `email` arguments. Available options are listed in green.","service":"devmatch-api","target":"user.findUnique","timestamp":"2025-07-13 21:09:27"}
{"environment":"development","level":"error","message":"数据库错误 \nInvalid `prisma.user.findUnique()` invocation:\n\n{\n  where: {\n    id: undefined,\n?   username?: String,\n?   email?: String,\n?   AND?: UserWhereInput | UserWhereInput[],\n?   OR?: UserWhereInput[],\n?   NOT?: UserWhereInput | UserWhereInput[],\n?   passwordHash?: StringFilter | String,\n?   role?: StringFilter | String,\n?   avatarUrl?: StringNullableFilter | String | Null,\n?   bio?: StringNullableFilter | String | Null,\n?   location?: StringNullableFilter | String | Null,\n?   githubUrl?: StringNullableFilter | String | Null,\n?   bilibiliUrl?: StringNullableFilter | String | Null,\n?   portfolioUrl?: StringNullableFilter | String | Null,\n?   createdAt?: DateTimeFilter | DateTime,\n?   updatedAt?: DateTimeFilter | DateTime,\n?   userSkills?: UserSkillListRelationFilter,\n?   projects?: ProjectListRelationFilter,\n?   applications?: ApplicationListRelationFilter,\n?   teamMembers?: TeamMemberListRelationFilter,\n?   sentMessages?: MessageListRelationFilter,\n?   receivedMessages?: MessageListRelationFilter,\n?   reviewedProjects?: ProjectListRelationFilter\n  },\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatarUrl: true,\n    createdAt: true\n  }\n}\n\nArgument `where` of type UserWhereUniqueInput needs at least one of `id`, `username` or `email` arguments. Available options are listed in green.","service":"devmatch-api","target":"user.findUnique","timestamp":"2025-07-13 21:10:02"}
{"environment":"development","level":"error","message":"数据库错误 \nInvalid `prisma.user.findUnique()` invocation:\n\n{\n  where: {\n    id: undefined,\n?   username?: String,\n?   email?: String,\n?   AND?: UserWhereInput | UserWhereInput[],\n?   OR?: UserWhereInput[],\n?   NOT?: UserWhereInput | UserWhereInput[],\n?   passwordHash?: StringFilter | String,\n?   role?: StringFilter | String,\n?   avatarUrl?: StringNullableFilter | String | Null,\n?   bio?: StringNullableFilter | String | Null,\n?   location?: StringNullableFilter | String | Null,\n?   githubUrl?: StringNullableFilter | String | Null,\n?   bilibiliUrl?: StringNullableFilter | String | Null,\n?   portfolioUrl?: StringNullableFilter | String | Null,\n?   createdAt?: DateTimeFilter | DateTime,\n?   updatedAt?: DateTimeFilter | DateTime,\n?   userSkills?: UserSkillListRelationFilter,\n?   projects?: ProjectListRelationFilter,\n?   applications?: ApplicationListRelationFilter,\n?   teamMembers?: TeamMemberListRelationFilter,\n?   sentMessages?: MessageListRelationFilter,\n?   receivedMessages?: MessageListRelationFilter,\n?   reviewedProjects?: ProjectListRelationFilter\n  },\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatarUrl: true,\n    createdAt: true\n  }\n}\n\nArgument `where` of type UserWhereUniqueInput needs at least one of `id`, `username` or `email` arguments. Available options are listed in green.","service":"devmatch-api","target":"user.findUnique","timestamp":"2025-07-13 21:11:37"}
{"date":"Sun Jul 13 2025 21:41:17 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2014:9)","os":{"loadavg":[1.75244140625,1.580078125,1.82373046875],"uptime":777917},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2297219,"heapTotal":32923648,"heapUsed":13949144,"rss":70262784},"pid":9830,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2014:9)","timestamp":"2025-07-13 21:41:17","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2014,"method":null,"native":false}]}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 21:57:37"}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 21:58:00"}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 21:59:42"}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 21:59:50"}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 22:01:38"}
{"clientVersion":"5.22.0","environment":"development","level":"error","message":"获取用户列表失败: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.","name":"PrismaClientValidationError","service":"devmatch-api","stack":"PrismaClientValidationError: \nInvalid `prisma.user.findMany()` invocation:\n\n{\n  select: {\n    id: true,\n    username: true,\n    email: true,\n    role: true,\n    avatar: true,\n    ~~~~~~\n    bio: true,\n    githubUsername: true,\n    location: true,\n    experience: true,\n    createdAt: true,\n    updatedAt: true,\n    _count: {\n      select: {\n        projects: true,\n        applications: true\n      }\n    },\n?   passwordHash?: true,\n?   avatarUrl?: true,\n?   githubUrl?: true,\n?   bilibiliUrl?: true,\n?   portfolioUrl?: true,\n?   userSkills?: true,\n?   projects?: true,\n?   applications?: true,\n?   teamMembers?: true,\n?   sentMessages?: true,\n?   receivedMessages?: true,\n?   reviewedProjects?: true\n  },\n  skip: 0,\n  take: 10,\n  orderBy: {\n    createdAt: \"desc\"\n  }\n}\n\nUnknown field `avatar` for select statement on model `User`. Available options are marked with ?.\n    at wn (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/Downloads/match/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async file:///Users/<USER>/Downloads/match/backend/src/routes/admin.js:449:28","timestamp":"2025-07-13 22:02:58"}
