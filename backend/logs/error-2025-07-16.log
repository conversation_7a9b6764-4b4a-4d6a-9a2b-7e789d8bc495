{"date": "Wed Jul 16 2025 20:00:05 GMT+0800 (中国标准时间)", "environment": "development", "error": {"code": "EPIPE", "errno": -32, "syscall": "write"}, "exception": true, "level": "error", "message": "uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at file:///Users/<USER>/Downloads/match/backend/src/routes/projects.js:86:15", "os": {"loadavg": [6.845703125, 4.0888671875, 3.2353515625], "uptime": 1031047}, "process": {"argv": ["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node", "/Users/<USER>/Downloads/match/backend/test-server.js"], "cwd": "/Users/<USER>/Downloads/match/backend", "execPath": "/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node", "gid": 20, "memoryUsage": {"arrayBuffers": 41195, "external": 2402862, "heapTotal": 19587072, "heapUsed": 17493640, "rss": 75726848}, "pid": 88680, "uid": 501, "version": "v22.14.0"}, "service": "devmatch-api", "stack": "Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.log (node:internal/console/constructor:384:26)\n    at file:///Users/<USER>/Downloads/match/backend/src/routes/projects.js:86:15", "timestamp": "2025-07-16 20:00:05", "trace": [{"column": 15, "file": "node:internal/stream_base_commons", "function": "afterWriteDispatched", "line": 159, "method": null, "native": false}, {"column": 3, "file": "node:internal/stream_base_commons", "function": "writeGeneric", "line": 150, "method": null, "native": false}, {"column": 11, "file": "node:net", "function": "Socket._writeGeneric", "line": 971, "method": "_writeGeneric", "native": false}, {"column": 8, "file": "node:net", "function": "Socket._write", "line": 983, "method": "_write", "native": false}, {"column": 12, "file": "node:internal/streams/writable", "function": "writeOr<PERSON>uffer", "line": 572, "method": null, "native": false}, {"column": 10, "file": "node:internal/streams/writable", "function": "_write", "line": 501, "method": null, "native": false}, {"column": 10, "file": "node:internal/streams/writable", "function": "Writable.write", "line": 510, "method": "write", "native": false}, {"column": 16, "file": "node:internal/console/constructor", "function": "console.value", "line": 298, "method": "value", "native": false}, {"column": 26, "file": "node:internal/console/constructor", "function": "console.log", "line": 384, "method": "log", "native": false}, {"column": 15, "file": "file:///Users/<USER>/Downloads/match/backend/src/routes/projects.js", "function": null, "line": 86, "method": null, "native": false}]}