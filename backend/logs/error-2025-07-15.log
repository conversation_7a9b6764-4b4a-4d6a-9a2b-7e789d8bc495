{"date":"Tue Jul 15 2025 15:24:33 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[2.7158203125,2.712890625,2.39013671875],"uptime":928114},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2328574,"heapTotal":33185792,"heapUsed":14041408,"rss":73682944},"pid":86067,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 15:24:33","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 15:54:10 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[3.07666015625,3.30078125,3.625],"uptime":929891},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2330178,"heapTotal":33185792,"heapUsed":14021664,"rss":72675328},"pid":94925,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 15:54:10","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 15:54:34 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[3.31787109375,3.33935546875,3.62890625],"uptime":929915},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2323279,"heapTotal":33185792,"heapUsed":14047888,"rss":71208960},"pid":95111,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 15:54:34","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 16:04:09 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","os":{"loadavg":[3.0791015625,2.80224609375,3.1025390625],"uptime":930490},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":33147,"external":2253399,"heapTotal":18243584,"heapUsed":16581080,"rss":71319552},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","timestamp":"2025-07-15 16:04:09","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":13,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":null,"line":352,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 16:04:34 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","os":{"loadavg":[2.642578125,2.720703125,3.06396484375],"uptime":930515},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":33171,"external":2257543,"heapTotal":18767872,"heapUsed":16744752,"rss":71536640},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","timestamp":"2025-07-15 16:04:34","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":13,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":null,"line":352,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 16:04:59 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","os":{"loadavg":[2.6943359375,2.712890625,3.0498046875],"uptime":930540},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":45735,"external":2265833,"heapTotal":18767872,"heapUsed":17490712,"rss":71598080},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","timestamp":"2025-07-15 16:04:59","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":13,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":null,"line":352,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 16:10:23 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Function.logerror (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:647:43)","os":{"loadavg":[2.86181640625,2.51611328125,2.81689453125],"uptime":930864},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":37195,"external":2257293,"heapTotal":19030016,"heapUsed":17750072,"rss":71962624},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Function.logerror (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:647:43)","timestamp":"2025-07-15 16:10:23","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":43,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.logerror","line":647,"method":"logerror","native":false}]}
{"date":"Tue Jul 15 2025 16:10:23 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Function.logerror (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:647:43)","os":{"loadavg":[2.86181640625,2.51611328125,2.81689453125],"uptime":930864},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":45387,"external":2265485,"heapTotal":19030016,"heapUsed":17843568,"rss":71970816},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at Function.logerror (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:647:43)","timestamp":"2025-07-15 16:10:23","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":43,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.logerror","line":647,"method":"logerror","native":false}]}
{"date":"Tue Jul 15 2025 16:11:17 GMT+0800 (中国标准时间)","environment":"development","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","os":{"loadavg":[2.94970703125,2.60693359375,2.83203125],"uptime":930918},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":49675,"external":2269773,"heapTotal":19030016,"heapUsed":17529416,"rss":65720320},"pid":95656,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:971:11)\n    at Socket._write (node:net:983:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at console.value (node:internal/console/constructor:298:16)\n    at console.error (node:internal/console/constructor:412:26)\n    at file:///Users/<USER>/Downloads/match/backend/test-server.js:352:13","timestamp":"2025-07-15 16:11:17","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":971,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":983,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":16,"file":"node:internal/console/constructor","function":"console.value","line":298,"method":"value","native":false},{"column":26,"file":"node:internal/console/constructor","function":"console.error","line":412,"method":"error","native":false},{"column":13,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":null,"line":352,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 17:30:58 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[3.6474609375,3.43994140625,3.52685546875],"uptime":935699},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2317819,"heapTotal":33067008,"heapUsed":14121960,"rss":74571776},"pid":20504,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 17:30:58","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 17:37:19 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[2.74853515625,3.19287109375,3.4052734375],"uptime":936080},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2321899,"heapTotal":32804864,"heapUsed":14126408,"rss":72888320},"pid":22209,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 17:37:19","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 17:37:55 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","os":{"loadavg":[3.4892578125,3.302734375,3.4345703125],"uptime":936116},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2321951,"heapTotal":33329152,"heapUsed":14112688,"rss":72384512},"pid":22456,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2066:9)","timestamp":"2025-07-15 17:37:55","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2066,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 18:20:23 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2505:9)","os":{"loadavg":[4.8515625,4.458984375,4.08544921875],"uptime":938664},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2469307,"heapTotal":34123776,"heapUsed":15362784,"rss":77926400},"pid":34250,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2505:9)","timestamp":"2025-07-15 18:20:23","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2505,"method":null,"native":false}]}
{"date":"Tue Jul 15 2025 18:26:59 GMT+0800 (中国标准时间)","environment":"development","error":{"address":"::","code":"EADDRINUSE","errno":-48,"port":8000,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use :::8000\nError: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2505:9)","os":{"loadavg":[3.2978515625,3.94921875,3.93212890625],"uptime":939060},"process":{"argv":["/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","/Users/<USER>/Downloads/match/backend/test-server.js"],"cwd":"/Users/<USER>/Downloads/match/backend","execPath":"/Users/<USER>/.nvm/versions/node/v22.14.0/bin/node","gid":20,"memoryUsage":{"arrayBuffers":16619,"external":2475980,"heapTotal":34385920,"heapUsed":15367600,"rss":77627392},"pid":36141,"uid":501,"version":"v22.14.0"},"service":"devmatch-api","stack":"Error: listen EADDRINUSE: address already in use :::8000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js:635:24)\n    at startServer (file:///Users/<USER>/Downloads/match/backend/test-server.js:2505:9)","timestamp":"2025-07-15 18:26:59","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1937,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1994,"method":null,"native":false},{"column":7,"file":"node:net","function":"Server.listen","line":2099,"method":"listen","native":false},{"column":24,"file":"/Users/<USER>/Downloads/match/backend/node_modules/express/lib/application.js","function":"Function.listen","line":635,"method":"listen","native":false},{"column":9,"file":"file:///Users/<USER>/Downloads/match/backend/test-server.js","function":"startServer","line":2505,"method":null,"native":false}]}
