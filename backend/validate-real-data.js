#!/usr/bin/env node

/**
 * 数据真实性验证脚本
 * 验证数据库中的用户数据是否真实存在并且持久化
 */

import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import axios from 'axios';

// 加载环境变量
dotenv.config({ path: './backend/.env' });

const prisma = new PrismaClient();
const API_BASE = 'http://localhost:3001/api';

async function validateDatabaseConnection() {
  console.log('🔍 1. 验证数据库连接...');
  
  try {
    await prisma.$connect();
    console.log('   ✅ 数据库连接成功');
    
    const userCount = await prisma.user.count();
    console.log(`   📊 数据库中共有 ${userCount} 个用户`);
    
    return true;
  } catch (error) {
    console.error('   ❌ 数据库连接失败:', error.message);
    return false;
  }
}

async function validateUserData() {
  console.log('\n👤 2. 验证用户数据真实性...');
  
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        passwordHash: true,
        role: true,
        bio: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    console.log(`   📋 找到 ${users.length} 个用户:`);
    
    for (const user of users) {
      console.log(`\n   👤 用户 #${user.id}:`);
      console.log(`      用户名: ${user.username}`);
      console.log(`      邮箱: ${user.email}`);
      console.log(`      角色: ${user.role}`);
      console.log(`      简介: ${user.bio || '无'}`);
      console.log(`      密码Hash: ${user.passwordHash ? '已设置' : '未设置'}`);
      console.log(`      创建时间: ${user.createdAt.toISOString()}`);
      console.log(`      更新时间: ${user.updatedAt.toISOString()}`);
      
      // 验证密码Hash
      if (user.passwordHash) {
        const isValidHash = user.passwordHash.startsWith('$2a$') || user.passwordHash.startsWith('$2b$');
        console.log(`      密码加密: ${isValidHash ? '✅ bcrypt格式正确' : '❌ 格式错误'}`);
      }
    }
    
    return users;
  } catch (error) {
    console.error('   ❌ 验证用户数据失败:', error.message);
    return [];
  }
}

async function validatePasswordAuthentication(users) {
  console.log('\n🔐 3. 验证密码认证...');
  
  // 测试已知的测试用户
  const testCases = [
    { email: '<EMAIL>', password: 'password123' },
    { email: '<EMAIL>', password: 'test123456' },
    { email: '<EMAIL>', password: 'test123456' }
  ];
  
  let validAuthCount = 0;
  
  for (const testCase of testCases) {
    const user = users.find(u => u.email === testCase.email);
    if (!user) {
      console.log(`   ⚠️  用户 ${testCase.email} 不存在于数据库中`);
      continue;
    }
    
    try {
      const isValidPassword = await bcrypt.compare(testCase.password, user.passwordHash);
      if (isValidPassword) {
        console.log(`   ✅ ${testCase.email} 密码验证成功`);
        validAuthCount++;
      } else {
        console.log(`   ❌ ${testCase.email} 密码验证失败`);
      }
    } catch (error) {
      console.error(`   💥 ${testCase.email} 密码验证出错:`, error.message);
    }
  }
  
  console.log(`\n   📊 密码验证统计: ${validAuthCount}/${testCases.length} 成功`);
  return validAuthCount;
}

async function validateAPIConsistency(users) {
  console.log('\n🌐 4. 验证API与数据库一致性...');
  
  let consistentCount = 0;
  
  for (const user of users) {
    try {
      // 尝试通过API登录
      const response = await axios.post(`${API_BASE}/auth/login`, {
        email: user.email,
        password: 'password123' // 我们知道的测试密码
      });
      
      if (response.data.success) {
        const apiUser = response.data.data.user;
        
        // 比较数据一致性
        const isConsistent = (
          apiUser.id === user.id &&
          apiUser.username === user.username &&
          apiUser.email === user.email &&
          apiUser.role === user.role
        );
        
        if (isConsistent) {
          console.log(`   ✅ ${user.email} API与数据库数据一致`);
          consistentCount++;
        } else {
          console.log(`   ⚠️  ${user.email} API与数据库数据不一致`);
          console.log(`      数据库: ID=${user.id}, 用户名=${user.username}`);
          console.log(`      API: ID=${apiUser.id}, 用户名=${apiUser.username}`);
        }
      }
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`   🔒 ${user.email} 密码不匹配（正常，测试密码可能不对）`);
      } else {
        console.log(`   ❌ ${user.email} API测试失败:`, error.message);
      }
    }
  }
  
  console.log(`\n   📊 API一致性统计: ${consistentCount}/${users.length} 一致`);
  return consistentCount;
}

async function validateDataPersistence() {
  console.log('\n💾 5. 验证数据持久化...');
  
  try {
    // 创建一个测试用户
    const testUsername = `persist_test_${Date.now()}`;
    const testEmail = `persist_test_${Date.now()}@example.com`;
    const testPassword = 'persistence_test_123';
    
    console.log(`   🔄 创建测试用户: ${testEmail}`);
    
    // 通过API创建用户
    const createResponse = await axios.post(`${API_BASE}/auth/register`, {
      username: testUsername,
      email: testEmail,
      password: testPassword
    });
    
    if (!createResponse.data.success) {
      throw new Error('API注册失败');
    }
    
    const userId = createResponse.data.data.user.id;
    console.log(`   ✅ API创建用户成功，ID: ${userId}`);
    
    // 直接查询数据库验证
    const dbUser = await prisma.user.findUnique({
      where: { id: userId }
    });
    
    if (dbUser) {
      console.log(`   ✅ 数据库中找到用户: ${dbUser.username}`);
      
      // 验证密码是否正确存储
      const isPasswordCorrect = await bcrypt.compare(testPassword, dbUser.passwordHash);
      console.log(`   ${isPasswordCorrect ? '✅' : '❌'} 密码存储验证: ${isPasswordCorrect ? '正确' : '错误'}`);
      
      // 通过API登录验证
      const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
        email: testEmail,
        password: testPassword
      });
      
      if (loginResponse.data.success) {
        console.log(`   ✅ API登录验证成功`);
        
        // 清理测试数据
        await prisma.user.delete({ where: { id: userId } });
        console.log(`   🗑️ 测试用户已清理`);
        
        return true;
      } else {
        console.log(`   ❌ API登录验证失败`);
        return false;
      }
    } else {
      console.log(`   ❌ 数据库中未找到用户`);
      return false;
    }
    
  } catch (error) {
    console.error('   ❌ 数据持久化验证失败:', error.message);
    return false;
  }
}

async function generateValidationReport(results) {
  console.log('\n📊 6. 生成验证报告...');
  
  const report = {
    timestamp: new Date().toISOString(),
    database: {
      connected: results.dbConnected,
      userCount: results.userCount,
      usersValid: results.users.length > 0
    },
    authentication: {
      validPasswords: results.validPasswords,
      totalUsers: results.users.length,
      successRate: results.users.length > 0 ? Math.round((results.validPasswords / results.users.length) * 100) : 0
    },
    api: {
      consistent: results.apiConsistent,
      totalUsers: results.users.length,
      consistencyRate: results.users.length > 0 ? Math.round((results.apiConsistent / results.users.length) * 100) : 0
    },
    persistence: {
      working: results.persistenceTest
    },
    overall: {
      dataIsReal: results.dbConnected && results.users.length > 0 && results.persistenceTest,
      systemWorking: results.validPasswords > 0 && results.persistenceTest
    },
    recommendations: []
  };
  
  // 生成建议
  if (report.overall.systemWorking) {
    report.recommendations.push('✅ 认证系统完全正常，数据真实有效');
  } else {
    report.recommendations.push('❌ 认证系统存在问题');
  }
  
  if (report.database.userCount === 0) {
    report.recommendations.push('⚠️ 数据库中没有用户，需要创建测试数据');
  }
  
  if (report.authentication.successRate < 50) {
    report.recommendations.push('⚠️ 密码认证成功率低，检查密码加密');
  }
  
  if (!report.persistence.working) {
    report.recommendations.push('❌ 数据持久化有问题，检查数据库配置');
  }
  
  const fs = await import('fs');
  fs.writeFileSync('data-validation-report.json', JSON.stringify(report, null, 2));
  console.log('   ✅ 验证报告已保存: data-validation-report.json');
  
  return report;
}

async function runValidation() {
  console.log('🚀 开始数据真实性验证\n');
  
  const results = {};
  
  try {
    // 1. 验证数据库连接
    results.dbConnected = await validateDatabaseConnection();
    if (!results.dbConnected) {
      console.log('\n❌ 数据库连接失败，无法继续验证');
      return;
    }
    
    // 2. 验证用户数据
    results.users = await validateUserData();
    results.userCount = results.users.length;
    
    // 3. 验证密码认证
    results.validPasswords = await validatePasswordAuthentication(results.users);
    
    // 4. 验证API一致性
    results.apiConsistent = await validateAPIConsistency(results.users);
    
    // 5. 验证数据持久化
    results.persistenceTest = await validateDataPersistence();
    
    // 6. 生成验证报告
    const report = await generateValidationReport(results);
    
    console.log('\n🎉 数据验证完成！\n');
    console.log('📋 验证总结:');
    console.log(`   💾 数据库连接: ${results.dbConnected ? '✅ 正常' : '❌ 失败'}`);
    console.log(`   👥 用户数量: ${results.userCount}`);
    console.log(`   🔐 密码验证: ${results.validPasswords}/${results.userCount} 成功`);
    console.log(`   🌐 API一致性: ${results.apiConsistent}/${results.userCount} 一致`);
    console.log(`   💾 数据持久化: ${results.persistenceTest ? '✅ 正常' : '❌ 异常'}`);
    console.log(`\n   🏆 整体评估: ${report.overall.systemWorking ? '✅ 系统正常' : '❌ 系统异常'}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 建议:');
      report.recommendations.forEach(rec => console.log(`   ${rec}`));
    }
    
  } catch (error) {
    console.error('\n💥 验证过程发生错误:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行验证
runValidation(); 