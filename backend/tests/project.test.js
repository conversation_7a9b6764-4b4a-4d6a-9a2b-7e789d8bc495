import { jest } from '@jest/globals';
import request from 'supertest';
import app from '../src/app.js';
import { PrismaClient } from '@prisma/client';

// 使用测试数据库
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL_TEST || process.env.DATABASE_URL
    }
  }
});

// 测试用户数据
const testUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password123'
};

const testAdmin = {
  username: 'testadmin',
  email: '<EMAIL>',
  password: 'password123'
};

// 测试项目数据
const testProject = {
  title: '测试项目',
  summary: '这是一个测试项目',
  category: 'web',
  description: '测试项目的详细描述，需要超过50个字符才能通过验证。这是一个完整的项目发布测试。',
  budgetRange: '10000-50000',
  demoUrl: 'https://example.com/demo',
  teamInfo: JSON.stringify([
    {
      name: '张三',
      role: '项目经理',
      background: '5年项目管理经验',
      introduction: '负责项目整体协调'
    }
  ]),
  teamInvestment: '团队全职投入，预计工作时间每周40小时',
  recruitmentInfo: JSON.stringify([
    {
      position: '前端开发工程师',
      cooperation: 'salary',
      skills: 'Vue.js, React, TypeScript',
      salary: '8000-12000',
      equity: ''
    }
  ]),
  workType: 'remote',
  workArrangement: 'fullTime',
  workLocation: '北京',
  recentProgress: '项目处于概念验证阶段，已完成基础架构设计',
  userAnalysis: '目标用户为中小企业，主要解决项目管理效率问题',
  projectOrigin: '基于团队在项目管理中遇到的实际痛点',
  competitiveAdvantage: '相比现有产品，我们的解决方案更加轻量级和易用',
  businessModel: 'subscription',
  businessDescription: '采用订阅制收费模式，按月收费，提供不同级别的服务',
  durationMonths: 6
};

describe('项目管理 API 测试', () => {
  let userToken;
  let adminToken;
  let testProjectId;

  beforeAll(async () => {
    // 清理测试数据
    await prisma.project.deleteMany({});
    await prisma.user.deleteMany({});
    
    // 创建测试用户
    const userResponse = await request(app)
      .post('/api/users/register')
      .send(testUser);
    
    const userLogin = await request(app)
      .post('/api/users/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });
    
    userToken = userLogin.body.data.token;
    
    // 创建测试管理员
    const adminResponse = await request(app)
      .post('/api/users/register')
      .send(testAdmin);
    
    // 将用户设为管理员
    await prisma.user.update({
      where: { email: testAdmin.email },
      data: { role: 'admin' }
    });
    
    const adminLogin = await request(app)
      .post('/api/users/login')
      .send({
        email: testAdmin.email,
        password: testAdmin.password
      });
    
    adminToken = adminLogin.body.data.token;
  });

  afterAll(async () => {
    // 清理测试数据
    await prisma.project.deleteMany({});
    await prisma.user.deleteMany({});
    await prisma.$disconnect();
  });

  describe('POST /api/projects', () => {
    it('应该成功创建项目', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(testProject);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projectId).toBeDefined();
      expect(response.body.data.status).toBe('pending_review');
      expect(response.body.data.reviewStatus).toBe('pending');
      
      testProjectId = response.body.data.projectId;
    });

    it('应该拒绝未登录用户创建项目', async () => {
      const response = await request(app)
        .post('/api/projects')
        .send(testProject);

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });

    it('应该拒绝缺少必填字段的项目', async () => {
      const invalidProject = { ...testProject };
      delete invalidProject.title;

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(invalidProject);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('必填字段');
    });

    it('应该拒绝描述过短的项目', async () => {
      const shortDescProject = { ...testProject, description: '太短了' };

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(shortDescProject);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/projects/my', () => {
    it('应该返回用户的项目列表', async () => {
      const response = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toBeInstanceOf(Array);
      expect(response.body.data.projects.length).toBeGreaterThan(0);
      expect(response.body.data.projects[0].title).toBe(testProject.title);
    });

    it('应该拒绝未登录用户访问', async () => {
      const response = await request(app)
        .get('/api/projects/my');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/projects/pending', () => {
    it('应该允许管理员获取待审核项目', async () => {
      const response = await request(app)
        .get('/api/projects/pending')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.projects).toBeInstanceOf(Array);
      expect(response.body.data.projects.length).toBeGreaterThan(0);
      expect(response.body.data.projects[0].creator).toBeDefined();
    });

    it('应该拒绝普通用户获取待审核项目', async () => {
      const response = await request(app)
        .get('/api/projects/pending')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('应该拒绝未登录用户访问', async () => {
      const response = await request(app)
        .get('/api/projects/pending');

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/projects/:id/review', () => {
    it('应该允许管理员审核通过项目', async () => {
      const response = await request(app)
        .post(`/api/projects/${testProjectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'approved',
          reviewMessage: '项目质量很好，符合发布要求'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('审核通过');
      expect(response.body.data.project.reviewStatus).toBe('approved');
      expect(response.body.data.project.status).toBe('recruiting');
      expect(response.body.data.project.publishedAt).toBeDefined();
    });

    it('应该允许管理员审核拒绝项目', async () => {
      // 先创建另一个项目用于拒绝测试
      const createResponse = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send({ ...testProject, title: '待拒绝项目' });

      const rejectProjectId = createResponse.body.data.projectId;

      const response = await request(app)
        .post(`/api/projects/${rejectProjectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'rejected',
          reviewMessage: '项目内容不符合平台规范'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('已拒绝');
      expect(response.body.data.project.reviewStatus).toBe('rejected');
      expect(response.body.data.project.status).toBe('rejected');
    });

    it('应该拒绝普通用户审核项目', async () => {
      const response = await request(app)
        .post(`/api/projects/${testProjectId}/review`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          reviewStatus: 'approved',
          reviewMessage: '测试'
        });

      expect(response.status).toBe(403);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('权限不足');
    });

    it('应该拒绝无效的审核状态', async () => {
      const response = await request(app)
        .post(`/api/projects/${testProjectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'invalid_status',
          reviewMessage: '测试'
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('审核状态无效');
    });

    it('应该拒绝审核不存在的项目', async () => {
      const response = await request(app)
        .post('/api/projects/999999/review')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'approved',
          reviewMessage: '测试'
        });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('项目不存在');
    });
  });

  describe('项目状态流转测试', () => {
    let newProjectId;

    beforeEach(async () => {
      // 为每个测试创建新项目
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send({ ...testProject, title: `测试项目${Date.now()}` });
      
      newProjectId = response.body.data.projectId;
    });

    it('项目创建后应该是待审核状态', async () => {
      const response = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      const project = response.body.data.projects.find(p => p.id === newProjectId);
      expect(project.status).toBe('pending_review');
      expect(project.reviewStatus).toBe('pending');
      expect(project.submittedAt).toBeDefined();
    });

    it('审核通过后应该是招募状态', async () => {
      // 审核通过
      await request(app)
        .post(`/api/projects/${newProjectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'approved',
          reviewMessage: '测试通过'
        });

      // 检查状态
      const response = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      const project = response.body.data.projects.find(p => p.id === newProjectId);
      expect(project.status).toBe('recruiting');
      expect(project.reviewStatus).toBe('approved');
      expect(project.publishedAt).toBeDefined();
    });

    it('审核拒绝后应该是拒绝状态', async () => {
      // 审核拒绝
      await request(app)
        .post(`/api/projects/${newProjectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'rejected',
          reviewMessage: '测试拒绝'
        });

      // 检查状态
      const response = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      const project = response.body.data.projects.find(p => p.id === newProjectId);
      expect(project.status).toBe('rejected');
      expect(project.reviewStatus).toBe('rejected');
      expect(project.reviewMessage).toBe('测试拒绝');
    });
  });

  describe('数据验证测试', () => {
    it('应该正确处理JSON字段', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(testProject);

      expect(response.status).toBe(201);
      
      // 验证数据库中的JSON字段
      const project = await prisma.project.findUnique({
        where: { id: response.body.data.projectId }
      });
      
      expect(project.teamInfo).toBeInstanceOf(Array);
      expect(project.teamInfo[0].name).toBe('张三');
      expect(project.recruitmentInfo).toBeInstanceOf(Array);
      expect(project.recruitmentInfo[0].position).toBe('前端开发工程师');
    });

    it('应该正确处理数值字段', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(testProject);

      expect(response.status).toBe(201);
      
      // 验证数据库中的数值字段
      const project = await prisma.project.findUnique({
        where: { id: response.body.data.projectId }
      });
      
      expect(project.durationMonths).toBe(6);
      expect(typeof project.durationMonths).toBe('number');
    });
  });

  describe('权限控制测试', () => {
    it('只有项目创建者能看到完整项目信息', async () => {
      // 创建另一个用户
      const otherUser = {
        username: 'otheruser',
        email: '<EMAIL>',
        password: 'password123'
      };

      await request(app)
        .post('/api/users/register')
        .send(otherUser);

      const otherLogin = await request(app)
        .post('/api/users/login')
        .send({
          email: otherUser.email,
          password: otherUser.password
        });

      const otherToken = otherLogin.body.data.token;

      // 其他用户获取项目列表应该为空
      const response = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${otherToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.projects).toHaveLength(0);
    });

    it('管理员可以看到所有待审核项目', async () => {
      const response = await request(app)
        .get('/api/projects/pending')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data.projects.length).toBeGreaterThan(0);
    });
  });
});

describe('项目业务逻辑测试', () => {
  let userToken;
  let adminToken;

  beforeAll(async () => {
    // 设置测试环境
    const userResponse = await request(app)
      .post('/api/users/register')
      .send(testUser);
    
    const userLogin = await request(app)
      .post('/api/users/login')
      .send({
        email: testUser.email,
        password: testUser.password
      });
    
    userToken = userLogin.body.data.token;
    
    const adminResponse = await request(app)
      .post('/api/users/register')
      .send(testAdmin);
    
    await prisma.user.update({
      where: { email: testAdmin.email },
      data: { role: 'admin' }
    });
    
    const adminLogin = await request(app)
      .post('/api/users/login')
      .send({
        email: testAdmin.email,
        password: testAdmin.password
      });
    
    adminToken = adminLogin.body.data.token;
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('项目发布流程测试', () => {
    it('完整的项目发布流程应该正常工作', async () => {
      // 1. 创建项目
      const createResponse = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(testProject);

      expect(createResponse.status).toBe(201);
      const projectId = createResponse.body.data.projectId;

      // 2. 查看我的项目
      const myProjectsResponse = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      expect(myProjectsResponse.status).toBe(200);
      const myProject = myProjectsResponse.body.data.projects.find(p => p.id === projectId);
      expect(myProject.status).toBe('pending_review');

      // 3. 管理员查看待审核项目
      const pendingResponse = await request(app)
        .get('/api/projects/pending')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(pendingResponse.status).toBe(200);
      const pendingProject = pendingResponse.body.data.projects.find(p => p.id === projectId);
      expect(pendingProject).toBeDefined();

      // 4. 管理员审核通过
      const reviewResponse = await request(app)
        .post(`/api/projects/${projectId}/review`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          reviewStatus: 'approved',
          reviewMessage: '项目符合要求，审核通过'
        });

      expect(reviewResponse.status).toBe(200);
      expect(reviewResponse.body.data.project.status).toBe('recruiting');

      // 5. 再次查看我的项目，状态应该已更新
      const updatedMyProjectsResponse = await request(app)
        .get('/api/projects/my')
        .set('Authorization', `Bearer ${userToken}`);

      expect(updatedMyProjectsResponse.status).toBe(200);
      const updatedProject = updatedMyProjectsResponse.body.data.projects.find(p => p.id === projectId);
      expect(updatedProject.status).toBe('recruiting');
      expect(updatedProject.reviewStatus).toBe('approved');
    });
  });

  describe('边界情况测试', () => {
    it('应该正确处理空的可选字段', async () => {
      const minimalProject = {
        title: '最小项目',
        summary: '最小项目概述',
        category: 'web',
        description: '这是一个最小项目，只包含必填字段。描述需要足够长以满足验证要求。',
        budgetRange: '10000-50000',
        teamInvestment: '团队兼职投入',
        workType: 'remote',
        workArrangement: 'partTime',
        recentProgress: '项目刚开始',
        userAnalysis: '面向开发者',
        projectOrigin: '个人想法',
        competitiveAdvantage: '简单易用',
        businessModel: 'freemium',
        businessDescription: '免费基础版，付费高级版',
        durationMonths: 3
      };

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(minimalProject);

      expect(response.status).toBe(201);
    });

    it('应该正确处理中文字符', async () => {
      const chineseProject = {
        ...testProject,
        title: '中文项目名称测试',
        summary: '这是一个中文项目概述',
        description: '中文项目描述，包含各种中文字符。这个项目是为了测试中文字符在系统中的处理。',
        userAnalysis: '中文用户分析',
        projectOrigin: '中文项目起源',
        competitiveAdvantage: '中文竞争优势',
        businessDescription: '中文商业模式描述'
      };

      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${userToken}`)
        .send(chineseProject);

      expect(response.status).toBe(201);
    });
  });
}); 