export default {
  openapi: '3.0.0',
  info: {
    title: 'DevMatch API',
    version: '1.0.0',
    description: 'DevMatch独立开发者匹配平台API文档',
    contact: {
      name: 'DevMatch团队',
      email: '<EMAIL>',
      url: 'https://aiguess.cn'
    },
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    }
  },
  servers: [
    {
      url: 'http://localhost:3000',
      description: '开发环境'
    },
    {
      url: 'https://api.aiguess.cn',
      description: '生产环境'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT'
      }
    },
    schemas: {
      User: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '用户ID'
          },
          username: {
            type: 'string',
            description: '用户名'
          },
          email: {
            type: 'string',
            format: 'email',
            description: '邮箱'
          },
          role: {
            type: 'string',
            enum: ['developer', 'admin'],
            description: '用户角色'
          },
          avatarUrl: {
            type: 'string',
            format: 'uri',
            description: '头像链接'
          },
          bio: {
            type: 'string',
            description: '个人简介'
          },
          location: {
            type: 'string',
            description: '所在地区'
          },
          githubUrl: {
            type: 'string',
            format: 'uri',
            description: 'GitHub链接'
          },
          bilibiliUrl: {
            type: 'string',
            format: 'uri',
            description: 'B站链接'
          },
          portfolioUrl: {
            type: 'string',
            format: 'uri',
            description: '作品集链接'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: '更新时间'
          }
        }
      },
      Project: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '项目ID'
          },
          title: {
            type: 'string',
            description: '项目标题'
          },
          description: {
            type: 'string',
            description: '项目描述'
          },
          category: {
            type: 'string',
            enum: ['web', 'mobile', 'desktop', 'ai', 'blockchain', 'game', 'tool', 'ecommerce', 'social', 'education', 'other'],
            description: '项目分类'
          },
          status: {
            type: 'string',
            enum: ['recruiting', 'in_progress', 'completed', 'cancelled'],
            description: '项目状态'
          },
          creatorId: {
            type: 'integer',
            description: '创建者ID'
          },
          teamSize: {
            type: 'integer',
            description: '团队规模'
          },
          durationMonths: {
            type: 'integer',
            description: '项目周期（月）'
          },
          workType: {
            type: 'string',
            enum: ['remote', 'onsite', 'hybrid'],
            description: '工作方式'
          },
          compensationType: {
            type: 'string',
            enum: ['salary', 'equity', 'salary_equity', 'revenue_share', 'none'],
            description: '薪酬类型'
          },
          budgetRange: {
            type: 'string',
            description: '预算范围'
          },
          prdDocumentUrl: {
            type: 'string',
            format: 'uri',
            description: 'PRD文档链接'
          },
          websiteUrl: {
            type: 'string',
            format: 'uri',
            description: '项目网站'
          },
          appUrl: {
            type: 'string',
            format: 'uri',
            description: '应用链接'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          },
          updatedAt: {
            type: 'string',
            format: 'date-time',
            description: '更新时间'
          }
        }
      },
      Skill: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '技能ID'
          },
          name: {
            type: 'string',
            description: '技能名称'
          },
          category: {
            type: 'string',
            enum: ['frontend', 'backend', 'mobile', 'devops', 'ai', 'design', 'product', 'marketing', 'other'],
            description: '技能分类'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          }
        }
      },
      Application: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '申请ID'
          },
          projectId: {
            type: 'integer',
            description: '项目ID'
          },
          applicantId: {
            type: 'integer',
            description: '申请者ID'
          },
          message: {
            type: 'string',
            description: '申请消息'
          },
          status: {
            type: 'string',
            enum: ['pending', 'accepted', 'rejected'],
            description: '申请状态'
          },
          appliedAt: {
            type: 'string',
            format: 'date-time',
            description: '申请时间'
          },
          respondedAt: {
            type: 'string',
            format: 'date-time',
            description: '响应时间'
          }
        }
      },
      Message: {
        type: 'object',
        properties: {
          id: {
            type: 'integer',
            description: '消息ID'
          },
          senderId: {
            type: 'integer',
            description: '发送者ID'
          },
          receiverId: {
            type: 'integer',
            description: '接收者ID'
          },
          projectId: {
            type: 'integer',
            description: '关联项目ID'
          },
          subject: {
            type: 'string',
            description: '消息主题'
          },
          content: {
            type: 'string',
            description: '消息内容'
          },
          messageType: {
            type: 'string',
            enum: ['invitation', 'discussion', 'system'],
            description: '消息类型'
          },
          isRead: {
            type: 'boolean',
            description: '是否已读'
          },
          createdAt: {
            type: 'string',
            format: 'date-time',
            description: '创建时间'
          }
        }
      },
      Error: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          error: {
            type: 'object',
            properties: {
              message: {
                type: 'string',
                description: '错误消息'
              },
              code: {
                type: 'string',
                description: '错误代码'
              },
              statusCode: {
                type: 'integer',
                description: 'HTTP状态码'
              }
            }
          }
        }
      },
      Success: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: true
          },
          message: {
            type: 'string',
            description: '成功消息'
          },
          data: {
            type: 'object',
            description: '响应数据'
          }
        }
      }
    },
    responses: {
      UnauthorizedError: {
        description: '认证失败',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            }
          }
        }
      },
      ForbiddenError: {
        description: '权限不足',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            }
          }
        }
      },
      NotFoundError: {
        description: '资源不存在',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            }
          }
        }
      },
      ValidationError: {
        description: '数据验证失败',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            }
          }
        }
      },
      InternalError: {
        description: '服务器内部错误',
        content: {
          'application/json': {
            schema: {
              $ref: '#/components/schemas/Error'
            }
          }
        }
      }
    }
  },
  tags: [
    {
      name: '认证',
      description: '用户认证相关API'
    },
    {
      name: '用户',
      description: '用户管理相关API'
    },
    {
      name: '项目',
      description: '项目管理相关API'
    },
    {
      name: '申请',
      description: '项目申请相关API'
    },
    {
      name: '消息',
      description: '消息系统相关API'
    },
    {
      name: '技能',
      description: '技能管理相关API'
    }
  ]
}; 