import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function seedDatabase() {
  try {
    console.log('🌱 开始创建数据库示例数据...');

    // 创建测试用户
    const testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    let userId;
    if (!testUser) {
      const hashedPassword = await bcrypt.hash('123456', 10);
      const newUser = await prisma.user.create({
        data: {
          username: '测试用户',
          email: '<EMAIL>',
          passwordHash: hashedPassword,
          role: 'developer'
        }
      });
      userId = newUser.id;
      console.log('✅ 创建测试用户成功');
    } else {
      userId = testUser.id;
      console.log('✅ 使用现有测试用户');
    }

    // 删除现有的项目数据
    await prisma.project.deleteMany({});
    console.log('🗑️ 清理现有项目数据');

    // 创建详细的示例项目
    const sampleProjects = [
      {
        title: '仙剑奇侠传',
        summary: '经典武侠RPG游戏重制版，传承经典，创新体验',
        category: 'game',
        description: '基于经典仙剑奇侠传的全新重制版本，采用现代3D技术重新打造，保留原版剧情的同时加入全新的战斗系统和社交功能。项目致力于为玩家带来既怀旧又新鲜的游戏体验，通过精美的画面、动人的音乐和深度的剧情，重新诠释这个经典的武侠世界。',
        budgetRange: '100-500万',
        demoUrl: 'https://demo.xianjian.com',
        teamInfo: [
          { name: '李逍遥', role: 'founder', background: '10年游戏开发经验，曾参与多款知名RPG游戏开发' },
          { name: '赵灵儿', role: 'designer', background: '资深游戏设计师，专注于角色设计和世界观构建' },
          { name: '林月如', role: 'programmer', background: '技术总监，精通Unity引擎和游戏优化技术' },
          { name: '拜月教主', role: 'artist', background: '首席美术师，负责游戏视觉风格和特效设计' }
        ],
        teamInvestment: '核心团队全职投入18个月，已完成概念设计和核心系统开发',
        recruitmentInfo: [
          { role: 'frontend', skillName: ['Unity', 'C#', 'Shader'], cooperation: 'salary_equity', salaryAmount: '25000', equityAmount: '3' },
          { role: 'backend', skillName: ['Node.js', 'MongoDB', 'Redis'], cooperation: 'salary_equity', salaryAmount: '22000', equityAmount: '2' },
          { role: 'ui', skillName: ['Photoshop', 'Sketch', '游戏UI'], cooperation: 'equity', equityAmount: '2' }
        ],
        workType: 'hybrid',
        workArrangement: 'fullTime',
        workLocation: '上海',
        recentProgress: '已完成角色系统、战斗系统基础框架，正在进行场景制作和剧情实现。预计6个月内完成Alpha版本。',
        userAnalysis: '目标用户为25-40岁的RPG游戏爱好者，特别是对仙剑系列有情怀的玩家群体。市场调研显示对经典游戏重制有强烈需求。',
        projectOrigin: '基于对经典游戏的热爱和市场对高质量RPG游戏的需求，团队决定重制这款经典游戏，希望用现代技术重新诠释这个美好的故事。',
        competitiveAdvantage: '拥有原版游戏授权，团队成员均为资深游戏开发者，具备完整的技术栈和丰富的项目经验。',
        businessModel: 'freemium',
        businessDescription: '采用免费游戏+内购模式，通过道具、皮肤、VIP会员等增值服务实现盈利。',
        durationMonths: 18,
        creatorId: userId,
        status: 'recruiting',
        reviewStatus: 'approved',
        featured: true,
        publishedAt: new Date(),
        reviewedAt: new Date()
      },
      {
        title: '智能健康管理平台',
        summary: '基于AI的个人健康数据分析和管理系统',
        category: 'health',
        description: '一个集成了AI分析、健康监测、饮食管理、运动指导于一体的智能健康管理平台。通过穿戴设备数据收集、AI算法分析，为用户提供个性化的健康建议和管理方案。平台支持多种健康指标监测，包括心率、血压、睡眠质量等，并能够根据用户的健康状况制定专属的运动和饮食计划。',
        budgetRange: '50-200万',
        demoUrl: null,
        teamInfo: [
          { name: '张医生', role: 'founder', background: '医学博士，10年临床经验，专注数字医疗' },
          { name: '李工程师', role: 'cto', background: '资深软件架构师，有医疗软件开发经验' }
        ],
        teamInvestment: '创始团队已投入8个月时间进行产品设计和技术验证',
        recruitmentInfo: [
          { role: 'ai', skillName: ['Python', 'TensorFlow', '机器学习'], cooperation: 'salary_equity', salaryAmount: '30000', equityAmount: '5' },
          { role: 'mobile', skillName: ['React Native', 'iOS', 'Android'], cooperation: 'salary', salaryAmount: '20000' },
          { role: 'backend', skillName: ['Java', 'Spring Boot', 'MySQL'], cooperation: 'salary_equity', salaryAmount: '25000', equityAmount: '3' }
        ],
        workType: 'remote',
        workArrangement: 'fullTime',
        workLocation: '远程办公',
        recentProgress: '已完成核心AI算法开发和数据模型设计，正在进行移动端应用开发。',
        userAnalysis: '目标用户为注重健康管理的中高收入人群，年龄段25-50岁，有使用智能设备习惯。',
        projectOrigin: '创始人作为医生，发现传统健康管理方式效率低下，希望通过技术手段提升健康管理效果。',
        competitiveAdvantage: '团队结合了医学专业背景和技术实力，拥有独特的AI算法和丰富的医疗数据。',
        businessModel: 'subscription',
        businessDescription: '采用订阅制模式，提供基础版和专业版服务，专业版包含更详细的健康分析和专家咨询。',
        durationMonths: 12,
        creatorId: userId,
        status: 'recruiting',
        reviewStatus: 'approved',
        featured: true,
        publishedAt: new Date(),
        reviewedAt: new Date()
      },
      {
        title: '区块链碳交易平台',
        summary: '基于区块链技术的碳信用交易和管理平台',
        category: 'blockchain',
        description: '利用区块链技术构建透明、可信的碳信用交易平台，为企业和个人提供碳足迹追踪、碳信用购买、碳中和认证等服务。平台采用智能合约确保交易的透明性和安全性，支持多种碳减排项目的认证和交易，致力于推动全球碳中和目标的实现。通过区块链技术确保碳信用的真实性和不可篡改性。',
        budgetRange: '200-1000万',
        demoUrl: 'https://demo.carbon-chain.com',
        teamInfo: [
          { name: '王总', role: 'founder', background: '环保行业12年经验，曾任职于联合国环境署' },
          { name: '刘技术', role: 'cto', background: '区块链技术专家，参与过多个DeFi项目开发' },
          { name: '陈博士', role: 'scientist', background: '环境科学博士，专注碳排放量化研究' }
        ],
        teamInvestment: '核心团队全职投入15个月，已获得种子轮投资',
        recruitmentInfo: [
          { role: 'blockchain', skillName: ['Solidity', 'Web3', 'Ethereum'], cooperation: 'salary_equity', salaryAmount: '35000', equityAmount: '4' },
          { role: 'frontend', skillName: ['React', 'Web3.js', 'TypeScript'], cooperation: 'salary_equity', salaryAmount: '25000', equityAmount: '2' },
          { role: 'backend', skillName: ['Go', 'Kubernetes', 'PostgreSQL'], cooperation: 'salary', salaryAmount: '28000' }
        ],
        workType: 'hybrid',
        workArrangement: 'fullTime',
        workLocation: '深圳',
        recentProgress: '智能合约开发完成80%，正在进行安全审计。平台前端基本完成，准备内测。',
        userAnalysis: '主要面向有碳中和需求的大中型企业，以及关注环保的投资机构和个人用户。',
        projectOrigin: '随着全球对碳中和的重视，传统碳交易市场存在透明度不足、流程复杂等问题，区块链技术能够很好地解决这些痛点。',
        competitiveAdvantage: '拥有环保行业资源和顶尖区块链技术团队，已与多家环保机构建立合作关系。',
        businessModel: 'commission',
        businessDescription: '通过交易手续费、认证服务费、技术服务费等方式盈利，预计毛利率40%以上。',
        durationMonths: 24,
        creatorId: userId,
        status: 'recruiting',
        reviewStatus: 'approved',
        featured: true,
        publishedAt: new Date(),
        reviewedAt: new Date()
      },
      {
        title: '在线教育互动平台',
        summary: '面向K12的沉浸式在线教育平台',
        category: 'education',
        description: '专为K12学生设计的沉浸式在线教育平台，集成了VR/AR技术、AI个性化学习、实时互动教学等功能。平台提供丰富的互动课程内容，支持多人在线协作学习，通过游戏化的学习方式提高学生的学习兴趣和效果。教师可以通过平台进行直播教学、作业布置、学习效果追踪等操作。',
        budgetRange: '300-800万',
        demoUrl: null,
        teamInfo: [
          { name: '刘校长', role: 'founder', background: '教育行业20年经验，曾任知名中学校长' },
          { name: '周老师', role: 'educator', background: '资深教育专家，专注K12课程设计' }
        ],
        teamInvestment: '教育团队已投入1年时间进行教学内容设计和用户调研',
        recruitmentInfo: [
          { role: 'frontend', skillName: ['Vue.js', 'WebGL', 'Three.js'], cooperation: 'salary_equity', salaryAmount: '23000', equityAmount: '3' },
          { role: 'backend', skillName: ['Python', 'Django', 'WebRTC'], cooperation: 'salary_equity', salaryAmount: '26000', equityAmount: '3' },
          { role: 'mobile', skillName: ['Flutter', 'AR', 'Unity'], cooperation: 'equity', equityAmount: '4' },
          { role: 'ai', skillName: ['机器学习', 'NLP', '推荐算法'], cooperation: 'salary_equity', salaryAmount: '32000', equityAmount: '4' }
        ],
        workType: 'remote',
        workArrangement: 'fullTime',
        workLocation: '北京/远程',
        recentProgress: '完成了核心教学模块设计，正在开发VR互动功能，已与3所学校签署试点协议。',
        userAnalysis: '目标用户为K12学生、家长和教师，重点关注一二线城市的中产家庭。',
        projectOrigin: '疫情期间在线教育需求激增，但现有平台缺乏真正的互动性和沉浸感，我们希望用新技术改变这一现状。',
        competitiveAdvantage: '团队具备丰富的教育行业经验和技术实力，已获得多项教育内容版权。',
        businessModel: 'subscription',
        businessDescription: '面向学校B2B订阅服务+面向家庭B2C付费课程的混合模式。',
        durationMonths: 18,
        creatorId: userId,
        status: 'recruiting',
        reviewStatus: 'approved',
        featured: false,
        publishedAt: new Date(),
        reviewedAt: new Date()
      },
      {
        title: '智能供应链管理系统',
        summary: '基于AI和IoT的智能供应链优化平台',
        category: 'enterprise',
        description: '为制造业和零售业提供智能化供应链管理解决方案，集成IoT设备监控、AI预测分析、区块链溯源等技术。系统能够实时监控库存状态、预测需求变化、优化配送路线、追踪产品流向，帮助企业降低运营成本、提高效率、确保产品质量。支持多级供应商管理和全链条数据可视化。',
        budgetRange: '500-1500万',
        demoUrl: 'https://demo.smartsupply.com',
        teamInfo: [
          { name: '马总', role: 'founder', background: '供应链管理专家，曾任职于世界500强企业' },
          { name: '陈工', role: 'cto', background: '企业软件架构师，有ERP系统开发经验' },
          { name: '李博士', role: 'ai_lead', background: 'AI算法专家，专注供应链优化研究' }
        ],
        teamInvestment: '核心团队全职投入2年，已完成MVP版本和客户验证',
        recruitmentInfo: [
          { role: 'backend', skillName: ['Java', 'Spring Cloud', 'Kafka'], cooperation: 'salary_equity', salaryAmount: '30000', equityAmount: '2' },
          { role: 'frontend', skillName: ['React', 'D3.js', 'Antd'], cooperation: 'salary', salaryAmount: '24000' },
          { role: 'data', skillName: ['Python', 'Spark', '数据挖掘'], cooperation: 'salary_equity', salaryAmount: '28000', equityAmount: '3' }
        ],
        workType: 'onsite',
        workArrangement: 'fullTime',
        workLocation: '广州',
        recentProgress: '已完成核心算法开发，正在进行大客户试点，预计下季度正式商业化。',
        userAnalysis: '主要客户为年营收5亿以上的制造业和零售业企业，对数字化转型有迫切需求。',
        projectOrigin: '传统供应链管理存在信息不透明、反应速度慢、成本高等问题，智能化技术能够显著改善这些痛点。',
        competitiveAdvantage: '团队具备深厚的行业背景和技术实力，已获得多项核心算法专利。',
        businessModel: 'enterprise',
        businessDescription: 'SaaS订阅模式+定制开发服务，预计客单价50-200万元/年。',
        durationMonths: 15,
        creatorId: userId,
        status: 'recruiting',
        reviewStatus: 'approved',
        featured: false,
        publishedAt: new Date(),
        reviewedAt: new Date()
      }
    ];

    // 创建项目
    for (const projectData of sampleProjects) {
      await prisma.project.create({
        data: projectData
      });
    }

    console.log(`✅ 成功创建了 ${sampleProjects.length} 个示例项目`);
    console.log('🎉 数据库初始化完成！');
    
    // 显示创建的项目列表
    const createdProjects = await prisma.project.findMany({
      select: {
        id: true,
        title: true,
        category: true,
        featured: true,
        status: true,
        reviewStatus: true
      }
    });
    
    console.log('\n📋 创建的项目列表:');
    createdProjects.forEach(project => {
      console.log(`- [${project.id}] ${project.title} (${project.category}) ${project.featured ? '🌟' : ''}`);
    });

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedDatabase();