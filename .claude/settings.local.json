{"permissions": {"allow": ["Bash(grep:*)", "Bash(./scripts/dev-start.sh:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep -n \"app.post.*admin.*projects\" /Users/<USER>/Downloads/match/backend/test-server.js)", "Bash(find:*)", "<PERSON><PERSON>(killall:*)", "Bash(npm run dev:*)", "Bash(npm install:*)", "Bash(npm start)", "Bash(node:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(pkill:*)", "Bash(lsof:*)", "<PERSON><PERSON>(cat:*)", "Bash(npm run start:*)", "<PERSON><PERSON>(true)", "Bash(npx prisma db seed:*)", "Bash(rm:*)", "Bash(curl -s http://localhost:8000/api/user/certification-status )", "Bash(-H \"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************.1ZqpoQFalKLutiEB0yymxCL6npZczodEINbjvH933ao\")", "Bash(kill:*)", "Bash(curl -X POST http://localhost:8000/api/auth/register -H \"Content-Type: application/json\" -d '{\"\"email\"\":\"\"<EMAIL>\"\",\"\"password\"\":\"\"123456\"\",\"\"confirmPassword\"\":\"\"123456\"\"}')", "Bash(psql:*)", "Bash(npx prisma generate:*)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma:*)", "Bash(pkill -f \"test-server.js\")", "<PERSON><PERSON>(chmod:*)", "Bash(./dev-start.sh:*)", "Bash(echo $PORT)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(touch:*)", "Bash(ls:*)", "Bash(npm run typecheck:*)", "Bash(npm run type-check:*)", "Bash(npm run build:*)", "Bash(npm run restart:*)"], "deny": []}}