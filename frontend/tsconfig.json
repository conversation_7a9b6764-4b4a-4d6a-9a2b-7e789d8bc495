{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/views/*": ["./src/views/*"], "@/assets/*": ["./src/assets/*"], "@/utils/*": ["./src/utils/*"], "@/stores/*": ["./src/stores/*"], "@/types/*": ["./src/types/*"], "@/api/*": ["./src/api/*"]}, "types": ["vite/client"], "strict": true, "jsx": "preserve", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}}