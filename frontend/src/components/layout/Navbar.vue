<template>
  <nav class="navbar">
    <div class="nav-container">
      <!-- Logo -->
      <router-link to="/" class="logo">
        小概率
      </router-link>

      <!-- 导航菜单 -->
      <ul class="nav-menu">
        <li>
          <router-link to="/" class="nav-link">首页</router-link>
        </li>
        <li>
          <router-link to="/projects" class="nav-link">项目</router-link>
        </li>
        <li>
          <router-link to="/academy" class="nav-link">学院</router-link>
        </li>
        <li>
          <router-link to="/investment" class="nav-link">投资</router-link>
        </li>
        <li>
          <router-link to="/advice" class="nav-link">自治</router-link>
        </li>
      </ul>

      <!-- 用户操作区 -->
      <div class="nav-actions">
        <template v-if="!isAuthenticated">
          <router-link to="/auth?mode=register" class="nav-btn btn-ghost">注册</router-link>
          <router-link to="/auth?mode=login" class="nav-btn btn-primary">登录</router-link>
        </template>
        <template v-else>
          <!-- 消息按钮 -->
          <div class="message-button-container">
            <router-link to="/messages" class="message-button">
              <i class="fas fa-bell"></i>
              <div 
                v-if="unreadCount > 0" 
                class="message-badge"
                :class="{ 'large-count': unreadCount > 99 }"
              >
                {{ unreadCount > 99 ? '99+' : unreadCount }}
              </div>
            </router-link>
          </div>
          
          <div class="nav-profile" @click="toggleProfileMenu">
            <div class="nav-avatar">
              <img v-if="userAvatar" :src="userAvatar" :alt="displayUsername" />
              <span v-else>{{ getAvatarInitial() }}</span>
            </div>
            <span class="nav-username">{{ displayUsername }}</span>
            <div class="nav-dropdown" v-if="showProfileMenu">
              <router-link to="/settings" class="dropdown-item">
                <i class="fas fa-cog"></i>
                个人资料
              </router-link>
              <router-link to="/resume" class="dropdown-item">
                <i class="fas fa-file-alt"></i>
                我的简历
              </router-link>
              <router-link to="/referral" class="dropdown-item">
                <i class="fas fa-gift"></i>
                邀请好友
              </router-link>
              <div class="dropdown-divider"></div>
              <button @click="handleLogout" class="dropdown-item logout-item">
                <i class="fas fa-sign-out-alt"></i>
                退出登录
              </button>
            </div>
          </div>
        </template>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../../stores/user'
import { http } from '../../utils/request'

const router = useRouter()
const userStore = useUserStore()

// 计算属性
const isAuthenticated = computed(() => userStore.isAuthenticated)
const user = computed(() => userStore.user)
const userAvatar = computed(() => {
  if (!user.value?.avatar) return null
  // 确保头像URL是完整的路径
  if (user.value.avatar.startsWith('http')) {
    return user.value.avatar
  }
  return `http://localhost:8000${user.value.avatar}`
})
const displayUsername = computed(() => {
  if (!isAuthenticated.value || !user.value) {
    return '加载中...'
  }

  // 优先显示用户名，如果没有用户名则显示截断的邮箱
  if (user.value.username && user.value.username.trim()) {
    return user.value.username.trim()
  }

  if (user.value.email && user.value.email.trim()) {
    // 邮箱截断显示逻辑
    const email = user.value.email.trim()
    const [localPart, domain] = email.split('@')

    // 如果本地部分太长，截断显示
    if (localPart && localPart.length > 8) {
      return `${localPart.substring(0, 6)}...@${domain}`
    }

    return email
  }

  // 如果既没有用户名也没有邮箱，说明数据有问题，应该重新获取用户信息
  console.warn('⚠️ 用户数据不完整，尝试刷新用户信息')
  userStore.refreshUserInfo?.()
  return '用户'
})

// 获取头像初始字母
const getAvatarInitial = () => {
  if (!user.value) return '?'

  // 优先使用用户名的首字母
  if (user.value.username && user.value.username.trim()) {
    return user.value.username.trim().charAt(0).toUpperCase()
  }

  // 如果没有用户名，使用邮箱的首字母
  if (user.value.email && user.value.email.trim()) {
    return user.value.email.trim().charAt(0).toUpperCase()
  }

  // 如果都没有，返回用户的默认字母
  return 'U'
}

// 下拉菜单状态
const showProfileMenu = ref(false)

// 消息相关状态
const unreadCount = ref(0)

// 获取未读消息数量
const fetchUnreadCount = async () => {
  if (!isAuthenticated.value) return
  
  try {
    const response = await http.get('/messages')
    if (response.success) {
      unreadCount.value = response.data?.unreadCount || 0
    }
  } catch (error) {
    console.error('获取消息数量失败:', error)
  }
}

// 切换个人资料菜单
const toggleProfileMenu = () => {
  showProfileMenu.value = !showProfileMenu.value
}

// 处理登出
const handleLogout = async () => {
  try {
    await userStore.logout()
    showProfileMenu.value = false
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.nav-profile')) {
    showProfileMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  
  // 获取未读消息数量
  fetchUnreadCount()
  
  // 每30秒刷新一次未读消息数量
  const interval = setInterval(fetchUnreadCount, 30000)
  
  // 监听认证状态变化
  watch(isAuthenticated, (newValue) => {
    if (!newValue) {
      // 用户退出登录或token过期，清除相关状态
      unreadCount.value = 0
      showProfileMenu.value = false
    } else {
      // 用户登录，重新获取数据
      fetchUnreadCount()
    }
  })
  
  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(interval)
  })
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.navbar {
  @apply fixed top-0 left-0 right-0 z-50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
}

.nav-container {
  @apply max-w-6xl mx-auto flex items-center justify-between;
  height: 72px;
}

.logo {
  @apply text-2xl font-bold text-primary no-underline;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.nav-menu {
  @apply flex items-center gap-8;
}

.nav-link {
  @apply text-gray-600 font-medium transition-colors duration-200 no-underline;
  position: relative;
}

.nav-link:hover {
  @apply text-primary;
}

.nav-link.router-link-active {
  @apply text-primary;
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.nav-actions {
  @apply flex items-center gap-4;
}

.nav-btn {
  @apply px-6 py-2 font-medium transition-all duration-300 no-underline;
  border-radius: 6px; /* 精确设置为设计系统规定的nav-btn圆角 */
}

.btn-ghost {
  @apply text-gray-600 hover:text-primary hover:bg-gray-50;
  border-radius: 6px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  @apply text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5;
  border-radius: 6px;
}

.nav-profile {
  @apply relative flex items-center gap-3 cursor-pointer p-2 rounded-xl hover:bg-gray-50 transition-colors;
}

.nav-avatar {
  @apply w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-medium overflow-hidden;
}

.nav-avatar img {
  @apply w-full h-full object-cover;
}

.nav-username {
  @apply text-gray-700 font-medium;
}

.nav-dropdown {
  @apply absolute top-full right-0 mt-2 py-2 bg-white rounded-xl shadow-lg border border-gray-100 min-w-48;
}

.dropdown-item {
  @apply block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors no-underline;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.dropdown-item:hover {
  @apply text-primary;
}

.dropdown-item i {
  width: 16px;
  font-size: 14px;
  color: #9ca3af;
}

.dropdown-item:hover i {
  color: #667eea;
}

/* 子菜单样式 */
.dropdown-submenu {
  position: relative;
}

.submenu-title {
  position: relative;
  cursor: pointer;
}

.submenu-arrow {
  margin-left: auto !important;
  font-size: 12px;
  transition: transform 0.2s;
}

.dropdown-submenu:hover .submenu-arrow {
  transform: rotate(90deg);
}

.submenu-content {
  position: absolute;
  left: 100%;
  top: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  min-width: 180px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-10px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
}

.dropdown-submenu:hover .submenu-content {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.submenu-content .dropdown-item {
  padding: 8px 12px;
  font-size: 13px;
}

/* 认证相关样式 */
.certification-badge {
  margin-left: auto;
  color: #059669;
  font-size: 12px;
}

.certification-status {
  margin-left: auto;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.certification-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.certification-status.certified {
  background: #d1fae5;
  color: #059669;
}

.dropdown-divider {
  @apply my-2 border-t border-gray-100;
}

button.dropdown-item {
  @apply w-full text-left bg-transparent border-none cursor-pointer;
}

.logout-item {
  @apply text-red-600;
}

.logout-item:hover {
  @apply text-red-700 bg-red-50;
}

.logout-item i {
  color: #dc2626 !important;
}

/* 消息按钮样式 */
.message-button-container {
  @apply relative;
}

.message-button {
  @apply relative flex items-center justify-center w-10 h-10 transition-all duration-300 no-underline;
  color: #6b7280;
  background: transparent;
}

.message-button:hover {
  color: #667eea;
  transform: translateY(-1px);
}

.message-button.router-link-active {
  color: #667eea;
}

.message-button i {
  font-size: 16px;
}

.message-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full min-w-5 h-5 flex items-center justify-center px-1;
  font-size: 10px;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  animation: pulse-badge 2s infinite;
}

.message-badge.large-count {
  @apply min-w-6 h-5 px-1;
  font-size: 9px;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .nav-menu {
    @apply hidden;
  }
  
  .nav-container {
    @apply px-4;
  }
  
  .nav-actions {
    @apply gap-2;
  }
  
  .nav-btn {
    @apply px-4 py-2 text-sm;
  }
  
  .message-button {
    @apply w-9 h-9;
  }
  
  .message-button i {
    font-size: 14px;
  }
  
  .nav-username {
    @apply hidden;
  }
}
</style> 