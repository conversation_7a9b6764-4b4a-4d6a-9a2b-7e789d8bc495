import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import Navbar from '@/components/layout/Navbar.vue'

// 模拟路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/auth', name: 'auth', component: { template: '<div>Auth</div>' } },
    { path: '/projects', name: 'projects', component: { template: '<div>Projects</div>' } }
  ]
})

describe('Navbar.vue', () => {
  let wrapper: any

  beforeEach(async () => {
    const pinia = createPinia()
    await router.push('/')
    await router.isReady()

    wrapper = mount(Navbar, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  it('renders properly', () => {
    expect(wrapper.find('.navbar').exists()).toBe(true)
    expect(wrapper.find('.logo').text()).toContain('小概率')
  })

  it('contains navigation links', () => {
    const navLinks = wrapper.findAll('.nav-link')
    expect(navLinks.length).toBeGreaterThan(0)
    
    // 检查是否包含主要导航链接
    const linkTexts = navLinks.map((link: any) => link.text())
    expect(linkTexts).toContain('首页')
    expect(linkTexts).toContain('项目')
    expect(linkTexts).toContain('学院')
  })

  it('shows login button when user is not authenticated', () => {
    const loginButton = wrapper.find('.btn-ghost')
    expect(loginButton.exists()).toBe(true)
    expect(loginButton.text()).toBe('登录')
  })

  it('has proper navigation structure', () => {
    expect(wrapper.find('nav').exists()).toBe(true)
    expect(wrapper.find('.nav-menu').exists()).toBe(true)
    expect(wrapper.find('.nav-actions').exists()).toBe(true)
  })

  it('applies glass morphism effect', () => {
    const navbar = wrapper.find('.navbar')
    expect(navbar.classes()).toContain('navbar')
    // 检查是否有固定定位和背景样式（毛玻璃效果通过CSS实现）
    expect(navbar.exists()).toBe(true)
  })
}) 