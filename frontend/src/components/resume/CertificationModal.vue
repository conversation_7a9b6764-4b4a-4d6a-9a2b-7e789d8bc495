<template>
  <div class="certification-modal-overlay" @click="handleOverlayClick">
    <div class="certification-modal" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-shield-alt"></i>
          简历认证
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <!-- 支付步骤指示器 -->
        <div class="payment-steps">
          <div :class="['step', { active: currentStep >= 1, completed: currentStep > 1 }]">
            <div class="step-circle">1</div>
            <span>选择支付</span>
          </div>
          <div class="step-line"></div>
          <div :class="['step', { active: currentStep >= 2, completed: currentStep > 2 }]">
            <div class="step-circle">2</div>
            <span>扫码支付</span>
          </div>
          <div class="step-line"></div>
          <div :class="['step', { active: currentStep >= 3, completed: currentStep > 3 }]">
            <div class="step-circle">3</div>
            <span>支付完成</span>
          </div>
        </div>

        <!-- 第一步：认证介绍和支付方式选择 -->
        <div v-if="currentStep === 1" class="step-content">
          <!-- 认证说明 -->
          <div class="certification-intro">
            <div class="intro-icon">
              <i class="fas fa-certificate"></i>
            </div>
            <div class="intro-content">
              <h3>简历认证服务</h3>
              <ul>
                <li>专业团队人工审核，确保简历信息真实性</li>
                <li>获得"已认证"金色标识，提升项目申请成功率</li>
                <li>认证后简历被锁定保护，增强可信度</li>
                <li>审核时间：1-3个工作日，认证永久有效</li>
              </ul>
            </div>
          </div>

          <!-- 费用说明 -->
          <div class="fee-info">
            <div class="fee-display">
              <span class="fee-label">认证费用</span>
              <span class="fee-amount">¥{{ certificationFee }}</span>
            </div>
            <p class="fee-desc">一次认证，永久有效</p>
          </div>

          <!-- 支付方式选择 -->
          <div class="payment-section">
            <h4>选择支付方式</h4>
            <div class="payment-methods">
              <div 
                v-for="method in paymentMethods" 
                :key="method.id"
                :class="['payment-method', { active: selectedPayment === method.id }]"
                @click="selectedPayment = method.id"
              >
                <div class="method-icon">
                  <i :class="method.icon"></i>
                </div>
                <div class="method-info">
                  <h5>{{ method.name }}</h5>
                  <p>{{ method.description }}</p>
                </div>
                <div class="method-radio">
                  <div :class="['radio', { checked: selectedPayment === method.id }]"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二步：微信支付二维码 -->
        <div v-if="currentStep === 2" class="step-content">
          <div class="payment-qr-section">
            <div class="qr-header">
              <h3>请使用微信扫码支付</h3>
              <p class="payment-amount">支付金额：¥{{ certificationFee }}</p>
            </div>

            <div class="qr-container">
              <div v-if="paymentLoading" class="qr-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <p>正在生成支付二维码...</p>
              </div>
              
              <div v-else-if="qrCodeUrl" class="qr-code">
                <img :src="qrCodeUrl" alt="微信支付二维码" />
                <p class="qr-tips">打开微信扫一扫，扫描上方二维码</p>
              </div>

              <div v-else class="qr-error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>生成支付二维码失败</p>
                <button @click="createPaymentOrder" class="btn btn-primary btn-sm">重新生成</button>
              </div>
            </div>

            <!-- 订单信息 -->
            <div v-if="currentOrder" class="order-info">
              <div class="order-item">
                <span>订单号：</span>
                <span>{{ currentOrder.orderId }}</span>
              </div>
              <div class="order-item">
                <span>订单状态：</span>
                <span :class="getOrderStatusClass(orderStatus)">{{ getOrderStatusText(orderStatus) }}</span>
              </div>
              <div class="order-item">
                <span>有效时间：</span>
                <span class="countdown">{{ formatTime(remainingTime) }}</span>
              </div>
            </div>

            <!-- 支付状态提示 -->
            <div class="payment-status">
              <div v-if="orderStatus === 'USERPAYING'" class="status-paying">
                <i class="fas fa-spinner fa-spin"></i>
                <span>支付中，请稍候...</span>
              </div>
              <div v-else-if="orderStatus === 'NOTPAY'" class="status-waiting">
                <i class="fas fa-qrcode"></i>
                <span>等待扫码支付</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三步：支付成功 -->
        <div v-if="currentStep === 3" class="step-content">
          <div class="payment-success">
            <div class="success-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <h3>支付成功！</h3>
            <p>您的简历认证申请已提交，我们将在1-3个工作日内完成审核。</p>
            
            <div class="success-details">
              <div class="detail-item">
                <span>支付金额：</span>
                <span class="amount">¥{{ certificationFee }}</span>
              </div>
              <div class="detail-item">
                <span>交易单号：</span>
                <span>{{ transactionId || currentOrder?.orderId }}</span>
              </div>
              <div class="detail-item">
                <span>支付时间：</span>
                <span>{{ formatDateTime(paidAt) }}</span>
              </div>
            </div>

            <div class="next-steps">
              <h4>接下来的步骤：</h4>
              <ul>
                <li>我们的专业团队将审核您的简历信息</li>
                <li>审核期间您可以继续编辑简历</li>
                <li>审核完成后将通过邮箱和站内信通知您</li>
                <li>认证通过后简历将被锁定并显示认证标识</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 支付失败 -->
        <div v-if="currentStep === -1" class="step-content">
          <div class="payment-failed">
            <div class="failed-icon">
              <i class="fas fa-times-circle"></i>
            </div>
            <h3>支付失败</h3>
            <p>{{ errorMessage || '支付过程中出现错误，请重试或选择其他支付方式。' }}</p>
            <div class="failed-actions">
              <button @click="resetPayment" class="btn btn-secondary">重新支付</button>
              <button @click="$emit('close')" class="btn btn-primary">稍后支付</button>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button 
          v-if="currentStep === 1" 
          @click="$emit('close')" 
          class="btn btn-secondary"
        >
          取消
        </button>
        <button 
          v-if="currentStep === 1"
          @click="startPayment" 
          class="btn btn-primary"
          :disabled="!selectedPayment || paymentLoading"
        >
          <i v-if="paymentLoading" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-credit-card"></i>
          立即支付 ¥{{ certificationFee }}
        </button>

        <button 
          v-if="currentStep === 2"
          @click="cancelPayment" 
          class="btn btn-secondary"
          :disabled="paymentLoading"
        >
          取消支付
        </button>

        <button 
          v-if="currentStep === 3"
          @click="$emit('success')" 
          class="btn btn-primary"
        >
          <i class="fas fa-check"></i>
          完成
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { wechatPayService, ORDER_TYPE, PAYMENT_STATUS, type WechatPayOrderResponse, type PaymentStatusResponse } from '../../utils/wechatPay'

const emit = defineEmits<{
  close: []
  success: []
}>()

// 响应式数据
const certificationFee = ref(199)
const currentStep = ref(1) // 1: 选择支付, 2: 扫码支付, 3: 支付成功, -1: 支付失败
const paymentLoading = ref(false)
const selectedPayment = ref('wechat')
const remainingTime = ref(900) // 15分钟倒计时
const orderStatus = ref<string>('NOTPAY')
const qrCodeUrl = ref('')
const errorMessage = ref('')
const transactionId = ref('')
const paidAt = ref('')

// 订单相关
const currentOrder = ref<WechatPayOrderResponse['data'] | null>(null)

// 定时器
let countdownTimer: ReturnType<typeof setInterval> | null = null
let statusPollingTimer: ReturnType<typeof setInterval> | null = null

// 支付方式
const paymentMethods = ref([
  {
    id: 'wechat',
    name: '微信支付',
    description: '使用微信扫码支付，安全便捷',
    icon: 'fab fa-weixin'
  }
])

// 计算属性
const isPaymentTimeout = computed(() => remainingTime.value <= 0)

// 方法
const handleOverlayClick = () => {
  if (currentStep.value !== 2) { // 支付中不允许点击遮罩关闭
    emit('close')
  }
}

const startPayment = async () => {
  if (!selectedPayment.value) return
  
  paymentLoading.value = true
  
  try {
    // 创建支付订单
    await createPaymentOrder()
    
    if (currentOrder.value && qrCodeUrl.value) {
      currentStep.value = 2
      startCountdown()
      startStatusPolling()
    }
  } catch (error) {
    console.error('启动支付失败:', error)
    showPaymentError('启动支付失败，请重试')
  } finally {
    paymentLoading.value = false
  }
}

const createPaymentOrder = async () => {
  try {
    const orderData = {
      amount: certificationFee.value,
      description: `简历认证服务 - ¥${certificationFee.value}`,
      orderType: ORDER_TYPE.CERTIFICATION as const,
      userId: 1, // 应该从用户store获取
      metadata: {
        service: 'resume_certification',
        version: '1.0'
      }
    }

    const result = await wechatPayService.createPayOrder(orderData)
    
    if (result.success && result.data.codeUrl) {
      currentOrder.value = result.data
      qrCodeUrl.value = wechatPayService.generateQRCodeUrl(result.data.codeUrl)
      orderStatus.value = result.data.orderStatus
      
      console.log('支付订单创建成功:', result.data)
    } else {
      throw new Error(result.message || '创建支付订单失败')
    }
  } catch (error) {
    console.error('创建支付订单失败:', error)
    throw error
  }
}

const startCountdown = () => {
  remainingTime.value = 900 // 15分钟
  countdownTimer = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      clearInterval(countdownTimer!)
      handlePaymentTimeout()
    }
  }, 1000)
}

const startStatusPolling = () => {
  if (!currentOrder.value) return
  
  // 每2秒查询一次支付状态
  statusPollingTimer = setInterval(async () => {
    if (!currentOrder.value) return
    
    try {
      const result = await wechatPayService.queryPaymentStatus(currentOrder.value.orderId)
      
      if (result.success) {
        orderStatus.value = result.data.status
        
        if (result.data.status === PAYMENT_STATUS.SUCCESS) {
          // 支付成功
          transactionId.value = result.data.transactionId || ''
          paidAt.value = result.data.paidAt || new Date().toISOString()
          handlePaymentSuccess()
        } else if ([PAYMENT_STATUS.CLOSED, PAYMENT_STATUS.REVOKED, PAYMENT_STATUS.PAYERROR].includes(result.data.status)) {
          // 支付失败
          handlePaymentFailure('支付失败或已取消')
        }
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
    }
  }, 2000)
}

const handlePaymentSuccess = () => {
  clearTimers()
  currentStep.value = 3
  
  console.log('支付成功')
}

const handlePaymentFailure = (message: string) => {
  clearTimers()
  showPaymentError(message)
}

const handlePaymentTimeout = () => {
  clearTimers()
  showPaymentError('支付超时，请重新发起支付')
}

const showPaymentError = (message: string) => {
  errorMessage.value = message
  currentStep.value = -1
}

const cancelPayment = async () => {
  if (currentOrder.value) {
    try {
      await wechatPayService.cancelOrder(currentOrder.value.orderId)
    } catch (error) {
      console.error('取消订单失败:', error)
    }
  }
  
  clearTimers()
  emit('close')
}

const resetPayment = () => {
  clearTimers()
  currentStep.value = 1
  currentOrder.value = null
  qrCodeUrl.value = ''
  errorMessage.value = ''
  orderStatus.value = 'NOTPAY'
}

const clearTimers = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer)
    statusPollingTimer = null
  }
}

const getOrderStatusClass = (status: string) => {
  switch (status) {
    case PAYMENT_STATUS.SUCCESS:
      return 'status-success'
    case PAYMENT_STATUS.USERPAYING:
      return 'status-paying'
    case PAYMENT_STATUS.NOTPAY:
      return 'status-waiting'
    default:
      return 'status-error'
  }
}

const getOrderStatusText = (status: string) => {
  switch (status) {
    case PAYMENT_STATUS.SUCCESS:
      return '支付成功'
    case PAYMENT_STATUS.USERPAYING:
      return '支付中'
    case PAYMENT_STATUS.NOTPAY:
      return '等待支付'
    case PAYMENT_STATUS.CLOSED:
      return '已关闭'
    case PAYMENT_STATUS.PAYERROR:
      return '支付失败'
    default:
      return '未知状态'
  }
}

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

onMounted(() => {
  // 默认选择微信支付
  selectedPayment.value = 'wechat'
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style scoped>
.certification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.certification-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title i {
  color: #059669;
}

.close-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 支付步骤指示器 */
.payment-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #94a3b8;
  font-size: 0.875rem;
  font-weight: 500;
}

.step.active {
  color: #3b82f6;
}

.step.completed {
  color: #059669;
}

.step-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s;
}

.step.active .step-circle {
  background: #3b82f6;
  color: white;
}

.step.completed .step-circle {
  background: #059669;
  color: white;
}

.step-line {
  width: 60px;
  height: 2px;
  background: #e2e8f0;
  margin: 0 20px;
}

/* 模态框内容 */
.modal-content {
  padding: 24px;
}

.step-content {
  min-height: 300px;
}

/* 认证说明 */
.certification-intro {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
  background: #f0fdf4;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #bbf7d0;
}

.intro-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: #059669;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.intro-content h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #065f46;
  margin-bottom: 12px;
}

.intro-content ul {
  margin: 0;
  padding-left: 16px;
  color: #047857;
}

.intro-content li {
  margin-bottom: 4px;
  font-size: 0.9rem;
}

/* 费用信息 */
.fee-info {
  background: #fef7cd;
  border: 1px solid #fbbf24;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 32px;
  text-align: center;
}

.fee-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.fee-label {
  font-size: 1.1rem;
  color: #92400e;
  font-weight: 500;
}

.fee-amount {
  font-size: 2rem;
  font-weight: 700;
  color: #92400e;
}

.fee-desc {
  color: #a16207;
  margin: 0;
  font-size: 0.9rem;
}

/* 支付方式 */
.payment-section h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.payment-methods {
  display: grid;
  gap: 12px;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method:hover {
  border-color: #d1d5db;
}

.payment-method.active {
  border-color: #059669;
  background: #f0fdf4;
}

.method-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
  color: #09bb07;
}

.method-info {
  flex: 1;
}

.method-info h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 2px;
}

.method-info p {
  font-size: 0.8rem;
  color: #64748b;
  margin: 0;
}

.method-radio {
  flex-shrink: 0;
}

.radio {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s;
}

.radio.checked {
  border-color: #059669;
}

.radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background: #059669;
  border-radius: 50%;
}

/* 二维码支付 */
.payment-qr-section {
  text-align: center;
}

.qr-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.payment-amount {
  font-size: 1.1rem;
  color: #059669;
  font-weight: 600;
  margin-bottom: 32px;
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32px;
}

.qr-loading,
.qr-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  color: #64748b;
}

.qr-loading i {
  font-size: 2rem;
  color: #3b82f6;
}

.qr-error i {
  font-size: 2rem;
  color: #ef4444;
}

.qr-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.qr-code img {
  width: 200px;
  height: 200px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.qr-tips {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

/* 订单信息 */
.order-info {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 0.9rem;
}

.order-item:first-child {
  font-family: monospace;
}

.countdown {
  color: #ef4444;
  font-weight: 600;
}

.status-success {
  color: #059669;
  font-weight: 600;
}

.status-paying {
  color: #3b82f6;
  font-weight: 600;
}

.status-waiting {
  color: #f59e0b;
  font-weight: 600;
}

.status-error {
  color: #ef4444;
  font-weight: 600;
}

/* 支付状态 */
.payment-status {
  padding: 16px;
}

.status-paying,
.status-waiting {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #64748b;
}

/* 支付成功页面 */
.payment-success {
  text-align: center;
}

.success-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #059669;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 2.5rem;
}

.payment-success h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.payment-success > p {
  color: #64748b;
  margin-bottom: 32px;
  line-height: 1.6;
}

.success-details {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 32px;
  text-align: left;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 0.9rem;
}

.detail-item .amount {
  color: #059669;
  font-weight: 600;
}

.next-steps {
  text-align: left;
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 20px;
}

.next-steps h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #065f46;
  margin-bottom: 12px;
}

.next-steps ul {
  margin: 0;
  padding-left: 16px;
  color: #047857;
}

.next-steps li {
  margin-bottom: 8px;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 支付失败页面 */
.payment-failed {
  text-align: center;
}

.failed-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #ef4444;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-size: 2.5rem;
}

.payment-failed h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.payment-failed > p {
  color: #64748b;
  margin-bottom: 32px;
  line-height: 1.6;
}

.failed-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  border: none;
  text-decoration: none;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
}

.btn-secondary {
  background: #f3f4f6;
  color: #4a5568;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .certification-modal-overlay {
    padding: 10px;
  }
  
  .certification-modal {
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 16px;
  }
  
  .certification-intro {
    flex-direction: column;
    text-align: center;
  }
  
  .payment-steps {
    padding: 16px;
  }
  
  .step {
    font-size: 0.75rem;
  }
  
  .step-circle {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }
  
  .step-line {
    width: 40px;
    margin: 0 10px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .failed-actions {
    flex-direction: column;
  }
  
  .qr-code img {
    width: 160px;
    height: 160px;
  }
}
</style>