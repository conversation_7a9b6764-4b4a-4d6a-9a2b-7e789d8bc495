<template>
  <div class="resume-display" :class="{ 'certified': certified }">
    <!-- 认证标识 -->
    <div v-if="certified" class="certification-overlay">
      <div class="certification-badge">
        <i class="fas fa-shield-alt"></i>
        <span>已认证简历</span>
      </div>
    </div>

    <!-- 基本信息 -->
    <section class="resume-section personal-info">
      <div class="personal-header">
        <div class="personal-avatar">
          {{ data.personalInfo.name ? data.personalInfo.name.charAt(0) : '?' }}
        </div>
        <div class="personal-details">
          <h1 class="personal-name">{{ data.personalInfo.name || '未填写' }}</h1>
          <p class="personal-title">{{ data.personalInfo.title || '未填写职位' }}</p>
          <div class="personal-contact">
            <div v-if="data.personalInfo.email" class="contact-item">
              <i class="fas fa-envelope"></i>
              <span>{{ data.personalInfo.email }}</span>
            </div>
            <div v-if="data.personalInfo.phone" class="contact-item">
              <i class="fas fa-phone"></i>
              <span>{{ data.personalInfo.phone }}</span>
            </div>
            <div v-if="data.personalInfo.location" class="contact-item">
              <i class="fas fa-map-marker-alt"></i>
              <span>{{ data.personalInfo.location }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="data.personalInfo.summary" class="personal-summary">
        <h3>个人简介</h3>
        <p>{{ data.personalInfo.summary }}</p>
      </div>
    </section>

    <!-- 技能专长 -->
    <section v-if="data.skills.length > 0" class="resume-section skills-section">
      <h2 class="section-title">
        <i class="fas fa-code"></i>
        技能专长
      </h2>
      <div class="skills-grid">
        <span v-for="skill in data.skills" :key="skill" class="skill-item">
          {{ skill }}
        </span>
      </div>
    </section>

    <!-- 工作经验 -->
    <section v-if="data.experience.length > 0" class="resume-section experience-section">
      <h2 class="section-title">
        <i class="fas fa-briefcase"></i>
        工作经验
      </h2>
      <div class="timeline">
        <div v-for="(exp, index) in data.experience" :key="index" class="timeline-item">
          <div class="timeline-marker"></div>
          <div class="timeline-content">
            <div class="timeline-header">
              <h3 class="timeline-title">{{ exp.position }}</h3>
              <span class="timeline-period">{{ exp.period }}</span>
            </div>
            <p class="timeline-company">{{ exp.company }}</p>
            <p class="timeline-description">{{ exp.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 项目经历 -->
    <section v-if="data.projects.length > 0" class="resume-section projects-section">
      <h2 class="section-title">
        <i class="fas fa-project-diagram"></i>
        项目经历
      </h2>
      <div class="projects-grid">
        <div v-for="(project, index) in data.projects" :key="index" class="project-item">
          <div class="project-header">
            <h3 class="project-name">{{ project.name }}</h3>
            <span class="project-period">{{ project.period }}</span>
          </div>
          <p class="project-description">{{ project.description }}</p>
          <div v-if="project.technologies" class="project-technologies">
            <span v-for="tech in getTechnologies(project.technologies)" :key="tech" class="tech-tag">
              {{ tech }}
            </span>
          </div>
        </div>
      </div>
    </section>

    <!-- 教育经历 -->
    <section v-if="data.education.length > 0" class="resume-section education-section">
      <h2 class="section-title">
        <i class="fas fa-graduation-cap"></i>
        教育经历
      </h2>
      <div class="education-list">
        <div v-for="(edu, index) in data.education" :key="index" class="education-item">
          <div class="education-header">
            <h3 class="education-school">{{ edu.school }}</h3>
            <span class="education-period">{{ edu.period }}</span>
          </div>
          <p class="education-degree">{{ edu.degree }}</p>
          <p v-if="edu.description" class="education-description">{{ edu.description }}</p>
        </div>
      </div>
    </section>

    <!-- 认证水印 -->
    <div v-if="certified" class="certification-watermark">
      <i class="fas fa-shield-check"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ResumeData {
  personalInfo: {
    name: string
    email: string
    phone: string
    location: string
    title: string
    summary: string
  }
  education: Array<{
    school: string
    degree: string
    period: string
    description: string
  }>
  experience: Array<{
    company: string
    position: string
    period: string
    description: string
  }>
  skills: string[]
  projects: Array<{
    name: string
    period: string
    description: string
    technologies: string
  }>
}

defineProps<{
  data: ResumeData
  certified?: boolean
}>()

// 方法
const getTechnologies = (technologies: string) => {
  return technologies.split(',').map(tech => tech.trim()).filter(Boolean)
}
</script>

<style scoped>
.resume-display {
  background: white;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.resume-display.certified {
  border: 2px solid #059669;
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(5, 150, 105, 0.1);
}

/* 认证标识 */
.certification-overlay {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
}

.certification-badge {
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  padding: 8px 16px;
  border-bottom-left-radius: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

.certification-badge i {
  font-size: 0.9rem;
}

/* 认证水印 */
.certification-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 8rem;
  color: rgba(5, 150, 105, 0.05);
  pointer-events: none;
  z-index: 1;
}

/* 简历区块 */
.resume-section {
  padding: 24px;
  position: relative;
  z-index: 2;
}

.resume-section:not(:last-child) {
  border-bottom: 1px solid #f1f5f9;
}

/* 区块标题 */
.section-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.section-title i {
  color: #667eea;
}

/* 基本信息 */
.personal-info {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.personal-header {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  align-items: flex-start;
}

.personal-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 600;
  flex-shrink: 0;
  border: 4px solid white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.personal-details {
  flex: 1;
}

.personal-name {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.personal-title {
  font-size: 1.1rem;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 16px;
}

.personal-contact {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 0.9rem;
}

.contact-item i {
  color: #94a3b8;
  width: 16px;
}

.personal-summary h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.personal-summary p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* 技能专长 */
.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-item {
  background: #e0f2fe;
  color: #0369a1;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid #7dd3fc;
}

/* 时间线样式 */
.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.timeline-item {
  position: relative;
  margin-bottom: 32px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -37px;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #667eea;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #667eea;
}

.timeline-content {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border-left: 3px solid #667eea;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 16px;
}

.timeline-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.timeline-period {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.timeline-company {
  color: #667eea;
  font-weight: 500;
  margin-bottom: 12px;
}

.timeline-description {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* 项目经历 */
.projects-grid {
  display: grid;
  gap: 20px;
}

.project-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.project-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 16px;
}

.project-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.project-period {
  background: #e2e8f0;
  color: #64748b;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.project-description {
  color: #4a5568;
  line-height: 1.6;
  margin-bottom: 16px;
}

.project-technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tag {
  background: white;
  color: #64748b;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid #e2e8f0;
}

/* 教育经历 */
.education-list {
  display: grid;
  gap: 20px;
}

.education-item {
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border-left: 3px solid #f59e0b;
}

.education-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 16px;
}

.education-school {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.education-period {
  background: #f59e0b;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.education-degree {
  color: #f59e0b;
  font-weight: 500;
  margin-bottom: 8px;
}

.education-description {
  color: #4a5568;
  line-height: 1.6;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resume-section {
    padding: 16px;
  }
  
  .personal-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .personal-contact {
    justify-content: center;
    flex-direction: column;
    gap: 8px;
  }
  
  .timeline {
    padding-left: 20px;
  }
  
  .timeline::before {
    left: 10px;
  }
  
  .timeline-marker {
    left: -27px;
  }
  
  .timeline-header,
  .project-header,
  .education-header {
    flex-direction: column;
    gap: 8px;
  }
  
  .timeline-period,
  .project-period,
  .education-period {
    align-self: flex-start;
  }
}
</style>