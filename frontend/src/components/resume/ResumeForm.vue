<template>
  <div class="resume-form">
    <!-- 基本信息 -->
    <section class="form-section">
      <h3 class="section-title">
        <i class="fas fa-user"></i>
        基本信息
      </h3>
      <div class="form-grid">
        <div class="form-group">
          <label>姓名 *</label>
          <input 
            v-model="formData.personalInfo.name" 
            type="text" 
            placeholder="请输入您的姓名"
            required
          />
        </div>
        <div class="form-group">
          <label>邮箱 *</label>
          <input 
            v-model="formData.personalInfo.email" 
            type="email" 
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div class="form-group">
          <label>手机号码 *</label>
          <input 
            v-model="formData.personalInfo.phone" 
            type="tel" 
            placeholder="请输入手机号码"
            required
          />
        </div>
        <div class="form-group">
          <label>所在地区</label>
          <input 
            v-model="formData.personalInfo.location" 
            type="text" 
            placeholder="如：北京市朝阳区"
          />
        </div>
        <div class="form-group full-width">
          <label>职业标题</label>
          <input 
            v-model="formData.personalInfo.title" 
            type="text" 
            placeholder="如：全栈开发工程师"
          />
        </div>
        <div class="form-group full-width">
          <label>个人简介</label>
          <textarea 
            v-model="formData.personalInfo.summary" 
            placeholder="简单介绍您的职业背景、技能特长和职业目标..."
            rows="4"
          ></textarea>
        </div>
      </div>
    </section>

    <!-- 教育经历 -->
    <section class="form-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="fas fa-graduation-cap"></i>
          教育经历
        </h3>
        <button @click="addEducation" type="button" class="btn btn-sm btn-secondary">
          <i class="fas fa-plus"></i>
          添加
        </button>
      </div>
      
      <div v-if="formData.education.length === 0" class="empty-section">
        <p>暂无教育经历，点击上方"添加"按钮开始添加</p>
      </div>
      
      <div v-for="(edu, index) in formData.education" :key="index" class="list-item">
        <div class="item-header">
          <span class="item-number">{{ index + 1 }}</span>
          <button @click="removeEducation(index)" type="button" class="btn-remove">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="form-grid">
          <div class="form-group">
            <label>学校名称</label>
            <input v-model="edu.school" type="text" placeholder="如：北京理工大学" />
          </div>
          <div class="form-group">
            <label>专业/学位</label>
            <input v-model="edu.degree" type="text" placeholder="如：计算机科学与技术学士" />
          </div>
          <div class="form-group">
            <label>就读时间</label>
            <input v-model="edu.period" type="text" placeholder="如：2016-2020" />
          </div>
          <div class="form-group full-width">
            <label>详细描述</label>
            <textarea v-model="edu.description" placeholder="主修课程、GPA、获奖情况等..." rows="2"></textarea>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作经验 -->
    <section class="form-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="fas fa-briefcase"></i>
          工作经验
        </h3>
        <button @click="addExperience" type="button" class="btn btn-sm btn-secondary">
          <i class="fas fa-plus"></i>
          添加
        </button>
      </div>
      
      <div v-if="formData.experience.length === 0" class="empty-section">
        <p>暂无工作经验，点击上方"添加"按钮开始添加</p>
      </div>
      
      <div v-for="(exp, index) in formData.experience" :key="index" class="list-item">
        <div class="item-header">
          <span class="item-number">{{ index + 1 }}</span>
          <button @click="removeExperience(index)" type="button" class="btn-remove">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="form-grid">
          <div class="form-group">
            <label>公司名称</label>
            <input v-model="exp.company" type="text" placeholder="如：字节跳动" />
          </div>
          <div class="form-group">
            <label>职位名称</label>
            <input v-model="exp.position" type="text" placeholder="如：高级全栈工程师" />
          </div>
          <div class="form-group">
            <label>工作时间</label>
            <input v-model="exp.period" type="text" placeholder="如：2021.03 - 至今" />
          </div>
          <div class="form-group full-width">
            <label>工作描述</label>
            <textarea v-model="exp.description" placeholder="主要职责、工作内容、取得成就等..." rows="3"></textarea>
          </div>
        </div>
      </div>
    </section>

    <!-- 技能专长 -->
    <section class="form-section">
      <h3 class="section-title">
        <i class="fas fa-code"></i>
        技能专长
      </h3>
      <div class="skills-input">
        <div class="form-group">
          <label>技能标签</label>
          <div class="skill-input-wrapper">
            <input 
              v-model="skillInput" 
              @keyup.enter="addSkill"
              type="text" 
              placeholder="输入技能后按回车添加，如：JavaScript"
            />
            <button @click="addSkill" type="button" class="btn btn-sm btn-primary">添加</button>
          </div>
        </div>
        <div class="skills-list">
          <span 
            v-for="(skill, index) in formData.skills" 
            :key="index"
            class="skill-tag"
            @click="removeSkill(index)"
          >
            {{ skill }}
            <i class="fas fa-times"></i>
          </span>
        </div>
        <div v-if="formData.skills.length === 0" class="empty-skills">
          <p>暂无技能标签，请在上方添加您的专业技能</p>
        </div>
      </div>
    </section>

    <!-- 项目经历 -->
    <section class="form-section">
      <div class="section-header">
        <h3 class="section-title">
          <i class="fas fa-project-diagram"></i>
          项目经历
        </h3>
        <button @click="addProject" type="button" class="btn btn-sm btn-secondary">
          <i class="fas fa-plus"></i>
          添加
        </button>
      </div>
      
      <div v-if="formData.projects.length === 0" class="empty-section">
        <p>暂无项目经历，点击上方"添加"按钮开始添加</p>
      </div>
      
      <div v-for="(project, index) in formData.projects" :key="index" class="list-item">
        <div class="item-header">
          <span class="item-number">{{ index + 1 }}</span>
          <button @click="removeProject(index)" type="button" class="btn-remove">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="form-grid">
          <div class="form-group">
            <label>项目名称</label>
            <input v-model="project.name" type="text" placeholder="如：企业级CRM系统" />
          </div>
          <div class="form-group">
            <label>项目周期</label>
            <input v-model="project.period" type="text" placeholder="如：2023.08 - 2024.01" />
          </div>
          <div class="form-group full-width">
            <label>项目描述</label>
            <textarea v-model="project.description" placeholder="项目背景、主要功能、技术难点、个人贡献等..." rows="3"></textarea>
          </div>
          <div class="form-group full-width">
            <label>技术栈</label>
            <input v-model="project.technologies" type="text" placeholder="请用逗号分隔，如：React, Node.js, PostgreSQL" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// Props
interface ResumeData {
  personalInfo: {
    name: string
    email: string
    phone: string
    location: string
    title: string
    summary: string
  }
  education: Array<{
    school: string
    degree: string
    period: string
    description: string
  }>
  experience: Array<{
    company: string
    position: string
    period: string
    description: string
  }>
  skills: string[]
  projects: Array<{
    name: string
    period: string
    description: string
    technologies: string
  }>
}

const props = defineProps<{
  modelValue: ResumeData
}>()

const emit = defineEmits<{
  'update:modelValue': [value: ResumeData]
  save: []
}>()

// 响应式数据
const formData = ref<ResumeData>({ ...props.modelValue })
const skillInput = ref('')

// 监听数据变化
watch(formData, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  formData.value = { ...newValue }
}, { deep: true })

// 教育经历方法
const addEducation = () => {
  formData.value.education.push({
    school: '',
    degree: '',
    period: '',
    description: ''
  })
}

const removeEducation = (index: number) => {
  formData.value.education.splice(index, 1)
}

// 工作经验方法
const addExperience = () => {
  formData.value.experience.push({
    company: '',
    position: '',
    period: '',
    description: ''
  })
}

const removeExperience = (index: number) => {
  formData.value.experience.splice(index, 1)
}

// 技能方法
const addSkill = () => {
  const skill = skillInput.value.trim()
  if (skill && !formData.value.skills.includes(skill)) {
    formData.value.skills.push(skill)
    skillInput.value = ''
  }
}

const removeSkill = (index: number) => {
  formData.value.skills.splice(index, 1)
}

// 项目经历方法
const addProject = () => {
  formData.value.projects.push({
    name: '',
    period: '',
    description: '',
    technologies: ''
  })
}

const removeProject = (index: number) => {
  formData.value.projects.splice(index, 1)
}
</script>

<style scoped>
.resume-form {
  max-width: 800px;
}

/* 表单区块 */
.form-section {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 24px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-title i {
  color: #667eea;
  font-size: 1rem;
}

/* 表单网格 */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  resize: vertical;
  font-family: inherit;
}

/* 列表项目 */
.list-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  position: relative;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.item-number {
  background: #667eea;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.btn-remove {
  background: #fee2e2;
  color: #dc2626;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.btn-remove:hover {
  background: #fecaca;
  transform: scale(1.1);
}

/* 技能相关 */
.skills-input {
  max-width: 600px;
}

.skill-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.skill-input-wrapper input {
  flex: 1;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 16px;
}

.skill-tag {
  background: #e0f2fe;
  color: #0369a1;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.skill-tag:hover {
  background: #fecaca;
  color: #dc2626;
}

.skill-tag i {
  font-size: 11px;
}

/* 空状态 */
.empty-section,
.empty-skills {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #e5e7eb;
}

.empty-section p,
.empty-skills p {
  margin: 0;
  font-size: 0.9rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  cursor: pointer;
  border: none;
  text-decoration: none;
  transition: all 0.2s;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a67d8;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .skill-input-wrapper {
    flex-direction: column;
    align-items: stretch;
  }
  
  .list-item {
    padding: 16px;
  }
}
</style>