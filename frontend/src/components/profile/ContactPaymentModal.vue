<template>
  <div class="contact-payment-modal-overlay" @click="handleOverlayClick">
    <div class="contact-payment-modal" @click.stop>
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-eye"></i>
          查看联系方式
        </h2>
        <button @click="$emit('close')" class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="modal-content">
        <!-- 用户信息 -->
        <div class="user-info">
          <div class="user-avatar">
            {{ userName.charAt(0) }}
          </div>
          <div class="user-details">
            <h3>{{ userName }}</h3>
            <p>查看TA的联系方式</p>
          </div>
        </div>

        <!-- 价格说明 -->
        <div class="price-info">
          <div class="price-display">
            <span class="currency">¥</span>
            <span class="amount">{{ price }}</span>
          </div>
          <p class="price-desc">一次付费，永久查看</p>
        </div>

        <!-- 功能说明 */
        <div class="features">
          <div class="feature-item">
            <i class="fas fa-envelope"></i>
            <span>查看邮箱地址</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-phone"></i>
            <span>查看手机号码</span>
          </div>
          <div class="feature-item">
            <i class="fab fa-weixin"></i>
            <span>查看微信号</span>
          </div>
          <div class="feature-item">
            <i class="fas fa-bell"></i>
            <span>发送查看通知</span>
          </div>
        </div>

        <!-- 支付方式 -->
        <div class="payment-section">
          <h4>选择支付方式</h4>
          <div class="payment-methods">
            <div 
              v-for="method in paymentMethods" 
              :key="method.id"
              :class="['payment-method', { active: selectedPayment === method.id }]"
              @click="selectedPayment = method.id"
            >
              <div class="method-icon">
                <i :class="method.icon"></i>
              </div>
              <div class="method-info">
                <h5>{{ method.name }}</h5>
              </div>
              <div class="method-radio">
                <div :class="['radio', { checked: selectedPayment === method.id }]"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 二维码支付 -->
        <div v-if="showPaymentCode" class="payment-code">
          <div class="qr-code">
            <div class="qr-placeholder">
              <i class="fas fa-qrcode"></i>
              <p>扫码支付 ¥{{ price }}</p>
            </div>
          </div>
          <div class="payment-tips">
            <p>请使用{{ getPaymentName() }}扫码支付</p>
            <p class="timer">支付剩余时间：{{ formatTime(remainingTime) }}</p>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">
          取消
        </button>
        <button 
          v-if="!showPaymentCode"
          @click="startPayment" 
          class="btn btn-primary"
          :disabled="!selectedPayment || loading"
        >
          <i v-if="loading" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-credit-card"></i>
          立即支付 ¥{{ price }}
        </button>
        <button 
          v-else
          @click="simulatePaymentSuccess" 
          class="btn btn-success"
        >
          <i class="fas fa-check"></i>
          模拟支付成功
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps<{
  userName: string
  price: number
}>()

const emit = defineEmits<{
  close: []
  success: []
}>()

// 响应式数据
const loading = ref(false)
const showPaymentCode = ref(false)
const selectedPayment = ref('')
const remainingTime = ref(300) // 5分钟倒计时
let paymentTimer: ReturnType<typeof setInterval> | null = null

// 支付方式
const paymentMethods = ref([
  {
    id: 'wechat',
    name: '微信支付',
    icon: 'fab fa-weixin'
  },
  {
    id: 'alipay',
    name: '支付宝',
    icon: 'fab fa-alipay'
  }
])

// 方法
const handleOverlayClick = () => {
  emit('close')
}

const startPayment = () => {
  if (!selectedPayment.value) return
  
  loading.value = true
  
  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    showPaymentCode.value = true
    startCountdown()
  }, 1000)
}

const startCountdown = () => {
  remainingTime.value = 300
  paymentTimer = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      clearInterval(paymentTimer!)
      handlePaymentTimeout()
    }
  }, 1000)
}

const handlePaymentTimeout = () => {
  showPaymentCode.value = false
  alert('支付超时，请重新发起支付')
}

const simulatePaymentSuccess = () => {
  if (paymentTimer) {
    clearInterval(paymentTimer)
  }
  
  // 模拟支付成功延迟
  setTimeout(() => {
    emit('success')
  }, 500)
}

const getPaymentName = () => {
  const method = paymentMethods.value.find(m => m.id === selectedPayment.value)
  return method ? method.name : ''
}

const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

onMounted(() => {
  // 默认选择微信支付
  selectedPayment.value = 'wechat'
})

onUnmounted(() => {
  if (paymentTimer) {
    clearInterval(paymentTimer)
  }
})
</script>

<style scoped>
.contact-payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.contact-payment-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-title i {
  color: #f59e0b;
}

.close-btn {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 模态框内容 */
.modal-content {
  padding: 24px;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  flex-shrink: 0;
}

.user-details h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.user-details p {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

/* 价格显示 */
.price-info {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #fef7cd, #fbbf24);
  border-radius: 12px;
}

.price-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  color: #92400e;
}

.amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: #92400e;
}

.price-desc {
  color: #a16207;
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 功能说明 */
.features {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: #4a5568;
  font-size: 0.9rem;
}

.feature-item i {
  width: 20px;
  color: #667eea;
  text-align: center;
}

/* 支付方式 */
.payment-section h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.payment-methods {
  display: grid;
  gap: 12px;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.payment-method:hover {
  border-color: #d1d5db;
}

.payment-method.active {
  border-color: #667eea;
  background: #f8fafc;
}

.method-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.payment-method:nth-child(1) .method-icon {
  color: #09bb07;
}

.payment-method:nth-child(2) .method-icon {
  color: #00a0e9;
}

.method-info {
  flex: 1;
}

.method-info h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.method-radio {
  flex-shrink: 0;
}

.radio {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  position: relative;
  transition: all 0.2s;
}

.radio.checked {
  border-color: #667eea;
}

.radio.checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #667eea;
  border-radius: 50%;
}

/* 支付二维码 */
.payment-code {
  text-align: center;
  margin-top: 24px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.qr-code {
  margin-bottom: 20px;
}

.qr-placeholder {
  width: 180px;
  height: 180px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  gap: 12px;
}

.qr-placeholder i {
  font-size: 2.5rem;
  color: #9ca3af;
}

.qr-placeholder p {
  color: #64748b;
  font-size: 0.8rem;
  margin: 0;
}

.payment-tips p {
  margin: 8px 0;
  color: #64748b;
  font-size: 0.85rem;
}

.timer {
  color: #dc2626 !important;
  font-weight: 600;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e5e7eb;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  border: none;
  text-decoration: none;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #f3f4f6;
  color: #4a5568;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-success {
  background: #059669;
  color: white;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-success:hover {
  background: #047857;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-payment-modal-overlay {
    padding: 10px;
  }
  
  .contact-payment-modal {
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 16px;
  }
  
  .user-info {
    flex-direction: column;
    text-align: center;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .qr-placeholder {
    width: 140px;
    height: 140px;
  }
}
</style>