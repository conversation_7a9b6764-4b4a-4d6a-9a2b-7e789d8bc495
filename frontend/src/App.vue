<template>
  <div id="app">
    <RouterView />
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onMounted(() => {
  // 初始化用户信息
  userStore.initUser()
  
  // 监听token过期事件
  window.addEventListener('auth-expired', userStore.handleAuthExpired)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('auth-expired', userStore.handleAuthExpired)
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#app {
  height: 100%;
  font-family: 'Inter', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

body {
  font-size: 14px;
  line-height: 1.6;
  color: #1a1a1a;
  background: #ffffff;
}

a {
  text-decoration: none;
  color: inherit;
}

ul, ol {
  list-style: none;
}
</style> 