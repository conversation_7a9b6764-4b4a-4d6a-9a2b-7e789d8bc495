import { http } from '@/utils/request'
import type { User, UserFilter } from '@/types/user'
import type { PaginatedResponse } from '@/types/api'

// 用户相关API
export const userApi = {
  // 获取用户列表
  getUsers: (params?: UserFilter): Promise<PaginatedResponse<User>> => {
    return http.get('/users', { params })
  },

  // 获取用户详情
  getUser: (id: string): Promise<User> => {
    return http.get(`/users/${id}`)
  },

  // 搜索用户
  searchUsers: (query: string, filters?: UserFilter): Promise<PaginatedResponse<User>> => {
    return http.get('/users/search', { params: { q: query, ...filters } })
  },

  // 关注用户
  followUser: (id: string): Promise<void> => {
    return http.post(`/users/${id}/follow`)
  },

  // 取消关注用户
  unfollowUser: (id: string): Promise<void> => {
    return http.delete(`/users/${id}/follow`)
  },

  // 获取关注列表
  getFollowing: (id: string, params?: { page?: number; pageSize?: number }): Promise<PaginatedResponse<User>> => {
    return http.get(`/users/${id}/following`, { params })
  },

  // 获取粉丝列表
  getFollowers: (id: string, params?: { page?: number; pageSize?: number }): Promise<PaginatedResponse<User>> => {
    return http.get(`/users/${id}/followers`, { params })
  },

  // 更新用户头像
  updateAvatar: (file: File): Promise<{ url: string }> => {
    return http.upload('/users/avatar', file)
  },

  // 获取用户统计
  getUserStats: (id: string): Promise<any> => {
    return http.get(`/users/${id}/stats`)
  },

  // 获取用户经历
  getUserExperience: (id: string): Promise<any[]> => {
    return http.get(`/users/${id}/experience`)
  },

  // 验证用户名是否可用
  checkUsername: (username: string): Promise<{ available: boolean }> => {
    return http.get('/users/check-username', { params: { username } })
  },

  // 验证邮箱是否可用
  checkEmail: (email: string): Promise<{ available: boolean }> => {
    return http.get('/users/check-email', { params: { email } })
  }
} 