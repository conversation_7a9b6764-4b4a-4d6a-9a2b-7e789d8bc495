import { http } from '@/utils/request'
import type { User, LoginCredentials, RegisterData } from '@/types/user'
import type { AuthResponse } from '@/types/api'

// 认证相关API
export const authApi = {
  // 登录
  login: (credentials: LoginCredentials): Promise<AuthResponse> => {
    return http.post('/auth/login', credentials)
  },

  // 注册
  register: (userData: RegisterData): Promise<AuthResponse> => {
    return http.post('/auth/register', userData)
  },

  // 获取用户信息
  getProfile: (): Promise<User> => {
    return http.get('/auth/profile')
  },

  // 更新用户信息
  updateProfile: (profileData: Partial<User>): Promise<User> => {
    return http.put('/auth/profile', profileData)
  },

  // 修改密码
  changePassword: (data: { currentPassword: string; newPassword: string }): Promise<void> => {
    return http.post('/auth/change-password', data)
  },

  // 忘记密码
  forgotPassword: (email: string): Promise<void> => {
    return http.post('/auth/forgot-password', { email })
  },

  // 重置密码
  resetPassword: (data: { token: string; password: string }): Promise<void> => {
    return http.post('/auth/reset-password', data)
  },

  // 刷新token
  refreshToken: (refreshToken: string): Promise<AuthResponse> => {
    return http.post('/auth/refresh', { refreshToken })
  },

  // 登出
  logout: (): Promise<void> => {
    return http.post('/auth/logout')
  },

  // 发送验证邮件
  sendVerificationEmail: (): Promise<void> => {
    return http.post('/auth/send-verification-email')
  },

  // 验证邮箱
  verifyEmail: (token: string): Promise<void> => {
    return http.post('/auth/verify-email', { token })
  },

  // 绑定手机号
  bindPhone: (data: { phone: string; code: string }): Promise<void> => {
    return http.post('/auth/bind-phone', data)
  },

  // 发送手机验证码
  sendSmsCode: (phone: string): Promise<void> => {
    return http.post('/auth/send-sms-code', { phone })
  },

  // 验证手机号
  verifyPhone: (data: { phone: string; code: string }): Promise<void> => {
    return http.post('/auth/verify-phone', data)
  },

  // 第三方登录
  socialLogin: (provider: string, code: string): Promise<AuthResponse> => {
    return http.post(`/auth/social/${provider}`, { code })
  },

  // 获取社交登录URL
  getSocialLoginUrl: (provider: string): Promise<{ url: string }> => {
    return http.get(`/auth/social/${provider}/url`)
  },

  // 发送邮箱验证码
  sendVerificationCode: (data: { email: string; type?: string }): Promise<{ message: string }> => {
    return http.post('/auth/send-verification-code', data)
  },

  // 验证邮箱验证码
  verifyCode: (data: { email: string; code: string; type?: string }): Promise<{ message: string }> => {
    return http.post('/auth/verify-code', data)
  },

  // 使用验证码重置密码
  resetPasswordWithCode: (data: { email: string; code: string; password: string }): Promise<{ success: boolean; message: string }> => {
    return http.post('/auth/reset-password-with-code', data)
  }
} 