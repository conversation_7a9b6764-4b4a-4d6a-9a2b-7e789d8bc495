import { http } from '@/utils/request'
import type { 
  Project, 
  ProjectFilter, 
  ProjectSearchResult, 
  ProjectApplication 
} from '@/types/project'
import type { PaginatedResponse } from '@/types/api'

// 项目发布数据接口
export interface ProjectPublishData {
  title: string
  summary: string
  category: string
  description: string
  budgetRange: string
  demoUrl?: string
  teamInfo: string // JSON字符串
  teamInvestment: string
  recruitmentInfo: string // JSON字符串
  workType: string
  workArrangement: string
  workLocation?: string
  recentProgress: string
  userAnalysis: string
  projectOrigin: string
  competitiveAdvantage: string
  businessModel: string
  businessDescription: string
  durationMonths: number
}

// 项目审核数据接口
export interface ProjectReviewData {
  reviewStatus: 'approved' | 'rejected'
  reviewMessage?: string
}

// 项目基本信息接口
export interface ProjectInfo {
  id: number
  title: string
  summary: string
  category: string
  status: string
  reviewStatus: string
  reviewMessage?: string
  submittedAt?: string
  reviewedAt?: string
  publishedAt?: string
  createdAt: string
  updatedAt: string
}

// 项目详细信息接口
export interface ProjectDetails extends ProjectInfo {
  description: string
  budgetRange: string
  demoUrl?: string
  teamInfo?: any
  teamInvestment: string
  recruitmentInfo?: any
  workType: string
  workArrangement: string
  workLocation?: string
  recentProgress: string
  userAnalysis: string
  projectOrigin: string
  competitiveAdvantage: string
  businessModel: string
  businessDescription: string
  durationMonths: number
  creator?: {
    id: number
    username: string
    email: string
    avatarUrl?: string
  }
}

// 项目相关API
export const projectApi = {
  // 获取项目列表
  getProjects: (params?: ProjectFilter): Promise<ProjectSearchResult> => {
    return http.get('/projects', { params })
  },

  // 获取项目详情
  getProject: (id: string): Promise<Project> => {
    return http.get(`/projects/${id}`)
  },

  // 创建项目
  createProject: (projectData: Partial<Project>): Promise<Project> => {
    return http.post('/projects', projectData)
  },

  // 更新项目
  updateProject: (id: string, projectData: Partial<Project>): Promise<Project> => {
    return http.put(`/projects/${id}`, projectData)
  },

  // 删除项目
  deleteProject: (id: string): Promise<void> => {
    return http.delete(`/projects/${id}`)
  },

  // 发布项目
  publishProject: (id: string): Promise<Project> => {
    return http.post(`/projects/${id}/publish`)
  },

  // 暂停项目
  pauseProject: (id: string): Promise<Project> => {
    return http.post(`/projects/${id}/pause`)
  },

  // 完成项目
  completeProject: (id: string): Promise<Project> => {
    return http.post(`/projects/${id}/complete`)
  },

  // 申请项目
  applyProject: (id: string, applicationData: {
    message: string
    proposedBudget?: number
    proposedDuration?: number
    coverLetter?: string
    portfolio?: string[]
  }): Promise<ProjectApplication> => {
    return http.post(`/projects/${id}/apply`, applicationData)
  },

  // 获取项目申请列表
  getProjectApplications: (id: string, params?: {
    status?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<ProjectApplication>> => {
    return http.get(`/projects/${id}/applications`, { params })
  },

  // 处理项目申请
  handleProjectApplication: (id: string, applicationId: string, action: 'accept' | 'reject', message?: string): Promise<void> => {
    return http.post(`/projects/${id}/applications/${applicationId}/${action}`, { message })
  },

  // 获取我的项目
  getMyProjects: (params?: {
    status?: string
    type?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<Project>> => {
    return http.get('/projects/my', { params })
  },

  // 获取我参与的项目
  getMyParticipatedProjects: (params?: {
    status?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<Project>> => {
    return http.get('/projects/participated', { params })
  },

  // 获取我的申请
  getMyApplications: (params?: {
    status?: string
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<ProjectApplication>> => {
    return http.get('/projects/applications/my', { params })
  },

  // 收藏项目
  bookmarkProject: (id: string): Promise<void> => {
    return http.post(`/projects/${id}/bookmark`)
  },

  // 取消收藏项目
  unbookmarkProject: (id: string): Promise<void> => {
    return http.delete(`/projects/${id}/bookmark`)
  },

  // 获取收藏的项目
  getBookmarkedProjects: (params?: {
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<Project>> => {
    return http.get('/projects/bookmarked', { params })
  },

  // 点赞项目
  likeProject: (id: string): Promise<void> => {
    return http.post(`/projects/${id}/like`)
  },

  // 取消点赞项目
  unlikeProject: (id: string): Promise<void> => {
    return http.delete(`/projects/${id}/like`)
  },

  // 分享项目
  shareProject: (id: string, platform: string): Promise<{ url: string }> => {
    return http.post(`/projects/${id}/share`, { platform })
  },

  // 获取项目统计
  getProjectStats: (id: string): Promise<any> => {
    return http.get(`/projects/${id}/stats`)
  },

  // 获取项目评论
  getProjectComments: (id: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<any>> => {
    return http.get(`/projects/${id}/comments`, { params })
  },

  // 添加项目评论
  addProjectComment: (id: string, content: string): Promise<any> => {
    return http.post(`/projects/${id}/comments`, { content })
  },

  // 搜索项目
  searchProjects: (query: string, filters?: ProjectFilter): Promise<ProjectSearchResult> => {
    return http.get('/projects/search', { params: { q: query, ...filters } })
  },

  // 获取推荐项目
  getRecommendedProjects: (params?: {
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<Project>> => {
    return http.get('/projects/recommended', { params })
  },

  // 获取热门项目
  getPopularProjects: (params?: {
    period?: 'day' | 'week' | 'month'
    page?: number
    pageSize?: number
  }): Promise<PaginatedResponse<Project>> => {
    return http.get('/projects/popular', { params })
  },

  // 获取项目分类
  getProjectCategories: (): Promise<any[]> => {
    return http.get('/projects/categories')
  },

  // 获取项目标签
  getProjectTags: (): Promise<string[]> => {
    return http.get('/projects/tags')
  },

  // 上传项目文件
  uploadProjectFile: (id: string, file: File): Promise<any> => {
    return http.upload(`/projects/${id}/files`, file)
  },

  // 删除项目文件
  deleteProjectFile: (id: string, fileId: string): Promise<void> => {
    return http.delete(`/projects/${id}/files/${fileId}`)
  }
}

/**
 * 发布新项目
 */
export const createProject = async (data: ProjectPublishData) => {
  return http.post('/projects', data)
}

/**
 * 获取我的项目列表
 */
export const getMyProjects = async () => {
  return http.get('/user/projects')
}

/**
 * 获取待审核项目列表（管理员专用）
 */
export const getPendingProjects = async () => {
  return http.get('/admin/review/pending')
}

/**
 * 审核项目（管理员专用）
 */
export const reviewProject = async (projectId: number, data: ProjectReviewData) => {
  return http.post(`/admin/review/${projectId}/approve`, data)
}

/**
 * 获取项目详情
 */
export const getProjectDetail = async (projectId: number) => {
  return http.get(`/projects/${projectId}`)
}

/**
 * 获取项目列表（公开）
 */
export const getProjects = async (params?: {
  page?: number
  limit?: number
  category?: string
  status?: string
  search?: string
}) => {
  return http.get('/projects', { params })
}

/**
 * 获取精选项目
 */
export const getFeaturedProjects = async () => {
  return http.get('/projects/featured')
}

/**
 * 申请加入项目
 */
export const applyToProject = async (projectId: number, data: {
  message: string
  expectedSalary?: number
  portfolio?: string
  coverLetter?: string
}) => {
  return http.post(`/projects/${projectId}/apply`, data)
}

export default {
  createProject,
  getMyProjects,
  getPendingProjects,
  reviewProject,
  getProjectDetail,
  getProjects,
  getFeaturedProjects,
  applyToProject
} 