import { http } from '@/utils/request'

// 消息接口类型定义
export interface Message {
  id: number
  senderId: number
  receiverId: number
  projectId?: number
  subject: string
  content: string
  messageType: 'invitation' | 'discussion' | 'system'
  isRead: boolean
  createdAt: string
  sender?: {
    id: number
    username: string
    email: string
    avatarUrl?: string
  }
  receiver?: {
    id: number
    username: string
    email: string
    avatarUrl?: string
  }
  project?: {
    id: number
    title: string
  }
}

export interface SendMessageData {
  receiverId: number
  subject: string
  content: string
  projectId?: number
  messageType?: 'invitation' | 'discussion' | 'system'
}

export interface MessageListResponse {
  messages: Message[]
  total: number
  unreadCount: number
}

// 消息相关API
export const messageApi = {
  // 获取消息列表
  getMessages: (params?: {
    type?: 'sent' | 'received'
    isRead?: boolean
  }): Promise<MessageListResponse> => {
    return http.get('/messages', { params })
  },

  // 获取消息详情
  getMessage: (id: number): Promise<Message> => {
    return http.get(`/messages/${id}`)
  },

  // 发送消息
  sendMessage: (data: SendMessageData): Promise<Message> => {
    return http.post('/messages', data)
  },

  // 标记消息为已读
  markAsRead: (id: number): Promise<void> => {
    return http.put(`/messages/${id}/read`)
  },

  // 获取未读消息数量
  getUnreadCount: (): Promise<{ count: number }> => {
    return http.get('/messages/unread-count')
  }
}

/**
 * 发送项目申请消息
 */
export const sendProjectApplicationMessage = async (data: {
  projectId: number
  projectTitle: string
  projectCreatorId: number
  applicantName: string
  message: string
  role?: string
}) => {
  const messageData: SendMessageData = {
    receiverId: data.projectCreatorId,
    subject: `项目申请：${data.projectTitle}`,
    content: `
用户 ${data.applicantName} 申请加入您的项目"${data.projectTitle}"${data.role ? `，申请职位：${data.role}` : ''}。

申请消息：
${data.message}

请登录系统查看详细信息并处理此申请。
    `.trim(),
    projectId: data.projectId,
    messageType: 'invitation'
  }
  
  return messageApi.sendMessage(messageData)
}

export default messageApi
