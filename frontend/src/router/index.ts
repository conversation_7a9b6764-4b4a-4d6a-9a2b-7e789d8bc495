import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('@/views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/auth',
    name: 'auth',
    component: () => import('@/views/Auth.vue'),
    meta: { title: '登录', requiresGuest: true }
  },
  {
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/views/ForgotPassword.vue'),
    meta: { title: '忘记密码', requiresGuest: true }
  },
  {
    path: '/projects',
    name: 'projects',
    component: () => import('@/views/Projects.vue'),
    meta: { title: '项目广场' }
  },
  {
    path: '/projects-simple',
    name: 'projects-simple',
    component: () => import('@/views/Projects.vue'),
    meta: { title: '项目广场(简化版)' }
  },
  {
    path: '/project/:id',
    name: 'project-detail',
    component: () => import('@/views/ProjectDetail.vue'),
    meta: { title: '项目详情' },
    props: true
  },
  {
    path: '/publish',
    name: 'publish',
    component: () => import('@/views/ProjectPublish.vue'),
    meta: { title: '发布项目', requiresAuth: true }
  },
  {
    path: '/academy',
    name: 'academy',
    component: () => import('@/views/Academy.vue'),
    meta: { title: '学院' }
  },
  {
    path: '/investment',
    name: 'investment',
    component: () => import('@/views/Investment.vue'),
    meta: { title: '投资生态' }
  },
  {
    path: '/profile/:id?',
    name: 'profile',
    component: () => import('@/views/Profile.vue'),
    meta: { title: '个人资料' },
    props: true
  },
  {
    path: '/resume',
    name: 'resume',
    component: () => import('@/views/Resume.vue'),
    meta: { title: '简历管理', requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('@/views/Settings.vue'),
    meta: { title: '账户设置', requiresAuth: true }
  },
  {
    path: '/advice',
    name: 'advice',
    component: () => import('@/views/Advice.vue'),
    meta: { title: 'DAO治理' }
  },
  {
    path: '/messages',
    name: 'messages',
    component: () => import('@/views/Messages.vue'),
    meta: { title: '消息中心', requiresAuth: true }
  },
  {
    path: '/help',
    name: 'help',
    component: () => import('@/views/HelpCenter.vue'),
    meta: { title: '帮助中心' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 小概率` : '小概率'
  
  // 如果需要认证的页面
  if (to.meta.requiresAuth) {
    // 如果有token但没有用户信息，尝试初始化
    if (userStore.token && !userStore.user) {
      console.log('🔄 检测到需要认证的页面，正在初始化用户信息...')
      const userData = await userStore.initUser()
      
      // 如果初始化失败（token无效），会自动清除token和跳转登录
      if (!userData) {
        console.log('🚫 用户初始化失败，跳转到登录页')
        next({ name: 'auth', query: { redirect: to.fullPath } })
        return
      }
    }
    
    // 最终检查认证状态
    if (!userStore.isAuthenticated) {
      console.log('🚫 用户未认证，跳转到登录页')
      next({ name: 'auth', query: { redirect: to.fullPath } })
      return
    }
  }
  
  // 如果是访客页面但用户已登录
  if (to.meta.requiresGuest && userStore.isAuthenticated) {
    console.log('👤 用户已登录，跳转到首页')
    next({ name: 'home' })
    return
  }
  
  // 允许访问
  next()
})

export default router 