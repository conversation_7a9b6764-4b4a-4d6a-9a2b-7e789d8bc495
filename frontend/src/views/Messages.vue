<template>
  <div class="messages-page">
    <Navbar />
    
    <main class="messages-content">
      <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-content">
            <h1 class="page-title">消息中心</h1>
            <p class="page-subtitle">查看来自系统和其他用户的重要通知</p>
          </div>
          <div class="header-actions">
            <button 
              @click="markAllAsRead" 
              class="btn btn-secondary"
              :disabled="loading || !hasUnreadMessages"
            >
              <i class="fas fa-check-double"></i>
              全部标记为已读
            </button>
            <button @click="loadMessages" class="btn btn-primary" :disabled="loading">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
              刷新
            </button>
          </div>
        </div>

        <!-- 消息筛选器 -->
        <div class="message-filters">
          <div class="filter-tabs">
            <button 
              v-for="filter in messageFilters" 
              :key="filter.key"
              :class="['filter-tab', { active: activeFilter === filter.key }]"
              @click="setActiveFilter(filter.key)"
            >
              <i :class="filter.icon"></i>
              {{ filter.label }}
              <span v-if="filter.count > 0" class="filter-count">{{ filter.count }}</span>
            </button>
          </div>
        </div>

        <!-- 消息列表 -->
        <div class="messages-container">
          <div v-if="loading && messages.length === 0" class="loading-state">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>正在加载消息...</p>
          </div>

          <div v-else-if="messages.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-inbox"></i>
            </div>
            <h3>暂无消息</h3>
            <p>{{ getEmptyStateText() }}</p>
          </div>

          <div v-else class="messages-list">
            <div 
              v-for="message in filteredMessages" 
              :key="message.id"
              :class="['message-item', { 
                'unread': !message.read,
                'priority-high': message.priority === 'high',
                'priority-urgent': message.priority === 'urgent'
              }]"
              @click="handleMessageClick(message)"
            >
              <!-- 消息图标 -->
              <div class="message-icon">
                <i :class="getMessageIcon(message.type)"></i>
              </div>

              <!-- 消息内容 -->
              <div class="message-content">
                <div class="message-header">
                  <h4 class="message-title">{{ message.title }}</h4>
                  <div class="message-meta">
                    <span class="message-time">{{ formatTime(message.createdAt) }}</span>
                    <span v-if="message.priority === 'high'" class="priority-badge high">重要</span>
                    <span v-if="message.priority === 'urgent'" class="priority-badge urgent">紧急</span>
                  </div>
                </div>
                
                <p class="message-text">{{ message.content }}</p>
                
                <div v-if="message.sender && message.sender.id !== 0" class="message-sender">
                  <span>来自：{{ message.sender.name }}</span>
                </div>
              </div>

              <!-- 消息操作 -->
              <div class="message-actions">
                <button 
                  v-if="!message.read"
                  @click.stop="markAsRead(message.id)"
                  class="action-btn mark-read"
                  title="标记为已读"
                >
                  <i class="fas fa-check"></i>
                </button>
                
                <button 
                  v-if="message.actionUrl"
                  @click.stop="handleActionClick(message)"
                  class="action-btn view-details"
                  :title="message.actionText || '查看详情'"
                >
                  <i class="fas fa-arrow-right"></i>
                </button>
              </div>

              <!-- 未读指示器 -->
              <div v-if="!message.read" class="unread-indicator"></div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.total > pagination.pageSize" class="pagination-container">
          <div class="pagination">
            <button 
              @click="goToPage(pagination.page - 1)"
              :disabled="pagination.page <= 1"
              class="pagination-btn"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            
            <span class="pagination-info">
              第 {{ pagination.page }} 页，共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
            </span>
            
            <button 
              @click="goToPage(pagination.page + 1)"
              :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
              class="pagination-btn"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
// @ts-ignore
import Navbar from '../components/layout/Navbar.vue'
import { http } from '../utils/request'

// 消息类型接口
interface Message {
  id: number
  title: string
  content: string
  type: string
  read: boolean // API返回的是read而不是isRead
  priority?: 'normal' | 'high' | 'urgent'
  userId: number
  sender: {
    id: number
    name: string
  } | null
  projectInfo?: {
    id: number
    title: string
  }
  actionUrl?: string
  actionText?: string
  createdAt: string
}

// 响应式数据
const router = useRouter()
const loading = ref(false)
const messages = ref<Message[]>([])
const activeFilter = ref('all')

const pagination = ref({
  page: 1,
  pageSize: 20,
  total: 0
})

// 消息筛选选项
const messageFilters = ref([
  { key: 'all', label: '全部消息', icon: 'fas fa-inbox', count: 0 },
  { key: 'unread', label: '未读消息', icon: 'fas fa-envelope', count: 0 },
  { key: 'system', label: '系统通知', icon: 'fas fa-cog', count: 0 },
  { key: 'application', label: '申请消息', icon: 'fas fa-user-plus', count: 0 },
  { key: 'project', label: '项目消息', icon: 'fas fa-project-diagram', count: 0 }
])

// 计算属性
const filteredMessages = computed(() => {
  if (activeFilter.value === 'all') {
    return messages.value
  } else if (activeFilter.value === 'unread') {
    return messages.value.filter(m => !m.read)
  } else {
    return messages.value.filter(m => m.type === activeFilter.value)
  }
})

const hasUnreadMessages = computed(() => {
  return messages.value.some(m => !m.read)
})

// 方法
const loadMessages = async () => {
  try {
    loading.value = true
    const response = await http.get('/messages', {
      params: {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize
      }
    })
    
    if (response.success) {
      messages.value = response.data.messages || []
      pagination.value.total = response.data.total || 0
      
      // 更新筛选器计数
      updateFilterCounts()
    }
  } catch (error) {
    console.error('加载消息失败:', error)
  } finally {
    loading.value = false
  }
}

const updateFilterCounts = () => {
  const filters = messageFilters.value
  filters.find(f => f.key === 'all')!.count = messages.value.length
  filters.find(f => f.key === 'unread')!.count = messages.value.filter(m => !m.read).length
  filters.find(f => f.key === 'system')!.count = messages.value.filter(m => m.type === 'system').length
  filters.find(f => f.key === 'application')!.count = messages.value.filter(m => m.type === 'project_application').length
  filters.find(f => f.key === 'project')!.count = messages.value.filter(m => m.type.includes('project')).length
}

const setActiveFilter = (filterKey: string) => {
  activeFilter.value = filterKey
}

const markAsRead = async (messageId: number) => {
  try {
    const response = await http.post(`/messages/${messageId}/read`)
    if (response.success) {
      const message = messages.value.find(m => m.id === messageId)
      if (message) {
        message.read = true
        updateFilterCounts()
      }
    }
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

const markAllAsRead = async () => {
  try {
    loading.value = true
    const unreadIds = messages.value.filter(m => !m.read).map(m => m.id)
    
    for (const id of unreadIds) {
      await markAsRead(id)
    }
  } catch (error) {
    console.error('标记全部已读失败:', error)
  } finally {
    loading.value = false
  }
}

const handleMessageClick = (message: Message) => {
  if (!message.read) {
    markAsRead(message.id)
  }
  
  // 如果是项目申请消息，跳转到申请人的个人资料页
  if (message.type === 'project_application' && message.sender?.id) {
    router.push(`/profile/${message.sender.id}`)
    return
  }
  
  if (message.actionUrl) {
    handleActionClick(message)
  }
}

const handleActionClick = (message: Message) => {
  if (message.actionUrl) {
    if (message.actionUrl.startsWith('http')) {
      window.open(message.actionUrl, '_blank')
    } else {
      router.push(message.actionUrl)
    }
  }
}

const goToPage = (page: number) => {
  pagination.value.page = page
  loadMessages()
}

const getMessageIcon = (type: string) => {
  const icons = {
    'system': 'fas fa-cog',
    'project_application': 'fas fa-user-plus',
    'project_invitation': 'fas fa-handshake',
    'project_update': 'fas fa-project-diagram',
    'payment': 'fas fa-credit-card',
    'achievement': 'fas fa-trophy'
  }
  return icons[type as keyof typeof icons] || 'fas fa-bell'
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const getEmptyStateText = () => {
  switch (activeFilter.value) {
    case 'unread': return '没有未读消息'
    case 'system': return '没有系统通知'
    case 'application': return '没有申请消息'
    case 'project': return '没有项目消息'
    default: return '暂时没有任何消息'
  }
}

// 初始化
onMounted(() => {
  loadMessages()
})
</script>

<style scoped>
/* CSS变量 */
.messages-page {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --surface-bg: #f7f9fc;
  --card-bg: #ffffff;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --text-muted: #a0aec0;
  --border-color: #e2e8f0;
  --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  --border-radius: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.messages-page {
  background: var(--surface-bg);
  min-height: 100vh;
}

.messages-content {
  padding-top: 120px;
  padding-bottom: 40px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 20px;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-content p {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  border: none;
  transition: var(--transition);
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
  background: #cbd5e0;
  transform: translateY(-1px);
}

/* 消息筛选器 */
.message-filters {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: var(--shadow-soft);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 10px;
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
}

.filter-tab:hover {
  background: #f8fafc;
  color: var(--text-primary);
}

.filter-tab.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.filter-tab.active .filter-count {
  background: rgba(255, 255, 255, 0.2);
}

.filter-tab:not(.active) .filter-count {
  background: #667eea;
  color: white;
}

/* 消息容器 */
.messages-container {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-soft);
  overflow: hidden;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 16px;
  color: #667eea;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 24px;
  color: var(--text-muted);
}

.empty-state h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

/* 消息列表 */
.messages-list {
  position: relative;
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:hover {
  background: #f8fafc;
}

.message-item.unread {
  background: #f8fafc;
  border-left: 4px solid #667eea;
}

.message-item.priority-high {
  border-left-color: #f59e0b;
}

.message-item.priority-urgent {
  border-left-color: #ef4444;
  background: #fef2f2;
}

/* 消息图标 */
.message-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

/* 消息内容 */
.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 8px;
}

.message-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.message-time {
  color: var(--text-muted);
  font-size: 13px;
  white-space: nowrap;
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high {
  background: #fef3c7;
  color: #92400e;
}

.priority-badge.urgent {
  background: #fee2e2;
  color: #991b1b;
}

.message-text {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 12px 0;
}

.message-sender {
  font-size: 13px;
  color: var(--text-muted);
}

/* 消息操作 */
.message-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex-shrink: 0;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  transform: translateY(-1px);
}

/* 未读指示器 */
.unread-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

/* 分页 */
.pagination-container {
  padding: 24px;
  border-top: 1px solid var(--border-color);
  background: var(--card-bg);
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background: white;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-btn:hover:not(:disabled) {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .filter-tabs {
    justify-content: center;
  }
  
  .message-item {
    padding: 16px;
    gap: 12px;
  }
  
  .message-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .message-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .message-actions {
    flex-direction: row;
  }
  
  .action-btn {
    width: 32px;
    height: 32px;
  }
}
</style>