<template>
  <div class="settings-page">
    <!-- 导航栏 -->
    <Navbar />

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 页面标题 -->
      <!-- <div class="page-header">
        <h1 class="page-title">个人设置</h1>
        <p class="page-subtitle">管理您的个人信息和偏好设置</p>
      </div> -->

      <!-- 设置表单 -->
      <div class="settings-form">
        <form @submit.prevent="handleSave">
          <!-- 基本信息 -->
          <div class="form-section">
            <h2 class="section-title">基本信息</h2>
            
            <!-- 头像上传 -->
            <div class="form-group avatar-upload-group">
              <label class="form-label">头像</label>
              <div class="avatar-upload-container">
                <div class="avatar-preview" @click="triggerFileUpload">
                  <img v-if="formData.avatarUrl" :src="formData.avatarUrl" alt="头像预览" />
                  <div v-else class="avatar-placeholder">
                    <i class="fas fa-camera"></i>
                    <span>点击上传头像</span>
                  </div>
                </div>
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  @change="handleAvatarUpload"
                />
                <div class="avatar-upload-text">
                  <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
                </div>
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">昵称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  class="form-input"
                  :class="{ error: errors.name }"
                  placeholder="请输入您的昵称"
                  @blur="validateField('name')"
                />
                <div v-if="errors.name" class="form-error">{{ errors.name }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">性别</label>
                <select 
                  v-model="formData.gender" 
                  class="form-input"
                  :class="{ error: errors.gender }"
                  @blur="validateField('gender')"
                >
                  <option value="">请选择性别</option>
                  <option value="male">男</option>
                  <option value="female">女</option>
                  <option value="private">不公开</option>
                </select>
                <div v-if="errors.gender" class="form-error">{{ errors.gender }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">所在城市</label>
                <input
                  v-model="formData.location"
                  type="text"
                  class="form-input"
                  :class="{ error: errors.location }"
                  placeholder="请输入您所在的城市，如：北京"
                  @blur="validateField('location')"
                />
                <div v-if="errors.location" class="form-error">{{ errors.location }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">职业</label>
                <input
                  v-model="formData.profession"
                  type="text"
                  class="form-input"
                  :class="{ error: errors.profession }"
                  placeholder="如：前端开发工程师"
                  @blur="validateField('profession')"
                />
                <div v-if="errors.profession" class="form-error">{{ errors.profession }}</div>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">个人简介</label>
              <textarea
                v-model="formData.bio"
                class="form-textarea"
                :class="{ error: errors.bio }"
                placeholder="简单介绍一下您的专业技能和工作经验..."
                rows="4"
                @blur="validateField('bio')"
              ></textarea>
              <div v-if="errors.bio" class="form-error">{{ errors.bio }}</div>
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="form-section">
            <h2 class="section-title">联系方式</h2>
            
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">邮箱地址</label>
                <input
                  v-model="formData.email"
                  type="email"
                  class="form-input disabled"
                  disabled
                  readonly
                />
                <div class="form-help">邮箱地址为注册邮箱，不可更改</div>
              </div>

              <div class="form-group">
                <label class="form-label">手机号</label>
                <input
                  v-model="formData.phone"
                  type="tel"
                  class="form-input"
                  :class="{ error: errors.phone }"
                  placeholder="请输入您的手机号"
                  @blur="validateField('phone')"
                />
                <div v-if="errors.phone" class="form-error">{{ errors.phone }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">微信号</label>
                <input
                  v-model="formData.wechat"
                  type="text"
                  class="form-input"
                  :class="{ error: errors.wechat }"
                  placeholder="请输入您的微信号"
                  @blur="validateField('wechat')"
                />
                <div v-if="errors.wechat" class="form-error">{{ errors.wechat }}</div>
              </div>
            </div>
          </div>

          <!-- 工作偏好 -->
          <div class="form-section">
            <h2 class="section-title">工作偏好</h2>
            
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">工作地点</label>
                <select 
                  v-model="formData.workType" 
                  class="form-input"
                  :class="{ error: errors.workType }"
                  @blur="validateField('workType')"
                >
                  <option value="">请选择工作地点</option>
                  <option value="remote">远程办公</option>
                  <option value="onsite">现场办公</option>
                  <option value="hybrid">混合办公</option>
                </select>
                <div v-if="errors.workType" class="form-error">{{ errors.workType }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">工作时间</label>
                <select 
                  v-model="formData.workTime" 
                  class="form-input"
                  :class="{ error: errors.workTime }"
                  @blur="validateField('workTime')"
                >
                  <option value="">请选择工作时间</option>
                  <option value="everyday">每一天</option>
                  <option value="weekends_only">仅周末</option>
                  <option value="evenings_weekends">晚上下班后+周末</option>
                </select>
                <div v-if="errors.workTime" class="form-error">{{ errors.workTime }}</div>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">合作方式</label>
              <select 
                v-model="formData.cooperationMode" 
                class="form-input"
                :class="{ error: errors.cooperationMode }"
                @blur="validateField('cooperationMode')"
              >
                <option value="">请选择合作方式</option>
                <option value="salary_required">必须薪资保障</option>
                <option value="profit_sharing_only">接收仅分成利润</option>
                <option value="both_acceptable">都可以接受</option>
              </select>
              <div v-if="errors.cooperationMode" class="form-error">{{ errors.cooperationMode }}</div>
            </div>
          </div>

          <!-- 保存按钮 -->
          <div class="save-button-container">
            <button 
              type="submit" 
              class="save-button"
              :disabled="loading"
            >
              <i v-if="loading" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-save"></i>
              {{ loading ? '保存中...' : '保存设置' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="showSuccess" class="success-toast">
      <i class="fas fa-check-circle"></i>
      <span>设置保存成功！</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import Navbar from '@/components/layout/Navbar.vue'
import { http } from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const formData = reactive({
  name: '',
  gender: '',
  location: '',
  profession: '',
  bio: '',
  email: '',
  phone: '',
  wechat: '',
  workType: '',
  workTime: '',
  cooperationMode: '',
  avatarUrl: ''
})

// 表单验证错误
const errors = reactive({
  name: '',
  gender: '',
  location: '',
  profession: '',
  bio: '',
  phone: '',
  wechat: '',
  workType: '',
  workTime: '',
  cooperationMode: ''
})

// 状态
const loading = ref(false)
const showSuccess = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const uploading = ref(false)

// 验证规则
const validationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 20,
    message: '昵称为必填项，长度在2-20个字符之间'
  },
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入正确的手机号格式'
  },
  wechat: {
    minLength: 6,
    maxLength: 20,
    message: '微信号长度在6-20个字符之间'
  }
}

// 验证单个字段
const validateField = (field: string) => {
  errors[field as keyof typeof errors] = ''
  
  const value = formData[field as keyof typeof formData]
  const rule = validationRules[field as keyof typeof validationRules]
  
  if (!rule) return true
  
  // 必填验证
  if (rule.required && (!value || value.toString().trim() === '')) {
    errors[field as keyof typeof errors] = rule.message || `${field}为必填项`
    return false
  }
  
  // 如果值为空且不是必填，跳过其他验证
  if (!value || value.toString().trim() === '') {
    return true
  }
  
  const stringValue = value.toString().trim()
  
  // 长度验证
  if (rule.minLength && stringValue.length < rule.minLength) {
    errors[field as keyof typeof errors] = rule.message
    return false
  }
  
  if (rule.maxLength && stringValue.length > rule.maxLength) {
    errors[field as keyof typeof errors] = rule.message
    return false
  }
  
  // 正则验证
  if (rule.pattern && !rule.pattern.test(stringValue)) {
    errors[field as keyof typeof errors] = rule.message
    return false
  }
  
  return true
}

// 验证所有字段
const validateAll = () => {
  let isValid = true
  
  // 验证必填字段
  if (!validateField('name')) isValid = false
  
  // 验证其他字段
  Object.keys(validationRules).forEach(field => {
    if (field !== 'name' && !validateField(field)) {
      isValid = false
    }
  })
  
  return isValid
}

// 加载用户数据
const loadUserProfile = async () => {
  try {
    loading.value = true
    console.log('🔄 开始加载用户资料...')
    
    // 使用当前登录用户的信息
    if (userStore.user) {
      console.log('✅ 从store获取用户信息:', userStore.user)
      
      // 填充表单数据
      formData.name = userStore.user.username || ''
      formData.email = userStore.user.email || ''
      formData.location = userStore.user.location || ''
      formData.bio = userStore.user.bio || ''
      formData.profession = userStore.user.profile?.position || ''
      // 处理头像URL，确保是完整路径
      let avatarUrl = userStore.user.avatar || ''
      if (avatarUrl && !avatarUrl.startsWith('http')) {
        avatarUrl = `http://localhost:8000${avatarUrl}`
      }
      formData.avatarUrl = avatarUrl
      
      // 从API获取更详细的用户资料
      try {
        const response = await http.get('/auth/profile')
        if (response.success && response.data) {
          const userData = response.data
          console.log('✅ 从API获取详细用户信息:', userData)
          
          // 更新表单数据
          formData.name = userData.username || formData.name
          formData.email = userData.email || formData.email
          formData.location = userData.location || formData.location
          formData.bio = userData.bio || formData.bio
          formData.phone = userData.phone || ''
          formData.wechat = userData.wechat || ''
          formData.profession = userData.profession || ''
          formData.workType = userData.workType || ''
          formData.workTime = userData.workTime || ''
          formData.cooperationMode = userData.cooperationMode || ''
          formData.gender = userData.gender || ''
          // 处理API返回的头像URL
          let apiAvatarUrl = userData.avatar || userData.avatarUrl || ''
          if (apiAvatarUrl && !apiAvatarUrl.startsWith('http')) {
            apiAvatarUrl = `http://localhost:8000${apiAvatarUrl}`
          }
          formData.avatarUrl = apiAvatarUrl || formData.avatarUrl
        }
      } catch (apiError) {
        console.log('⚠️ API获取用户详情失败，使用store数据:', apiError)
      }
    } else {
      console.log('⚠️ 用户未登录，重定向到登录页')
      router.push('/auth')
      return
    }
    
  } catch (error) {
    console.error('❌ 加载用户资料失败:', error)
  } finally {
    loading.value = false
  }
}

// 触发文件选择
const triggerFileUpload = () => {
  fileInput.value?.click()
}

// 处理头像上传
const handleAvatarUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (!file) return
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    alert('请选择图片文件')
    return
  }
  
  // 验证文件大小 (2MB)
  if (file.size > 2 * 1024 * 1024) {
    alert('图片大小不能超过 2MB')
    return
  }
  
  try {
    uploading.value = true
    
    // 创建FormData用于上传
    const formData_upload = new FormData()
    formData_upload.append('avatar', file)
    
    // 上传头像
    const response = await http.post('/auth/upload-avatar', formData_upload, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    if (response.success && response.data?.avatarUrl) {
      console.log('🔍 头像上传响应数据:', response.data)
      
      // 构建完整的头像URL
      const baseUrl = 'http://localhost:8000'
      const avatarUrl = response.data.avatarUrl.startsWith('http') 
        ? response.data.avatarUrl 
        : `${baseUrl}${response.data.avatarUrl}`
      
      // 立即更新formData.avatarUrl，触发avatar-preview重新渲染
      formData.avatarUrl = avatarUrl
      console.log('🔄 更新formData.avatarUrl:', formData.avatarUrl)

      // 立即更新store中的用户头像信息，这样导航栏会立即更新
      await userStore.updateAvatar(avatarUrl)
      console.log('🔄 已通过updateAvatar更新头像:', avatarUrl)
      
      console.log('✅ 头像上传成功，已更新formData.avatarUrl:', formData.avatarUrl)
      console.log('✅ 已更新userStore.user.avatar:', userStore.user?.avatar)
      
      // 显示成功提示
      alert('头像上传成功！')
    } else {
      console.error('❌ 头像上传响应数据无效:', response)
      throw new Error(response.message || '头像上传失败')
    }
    
  } catch (error: any) {
    console.error('❌ 头像上传失败:', error)
    alert(error.message || '头像上传失败，请稍后重试')
  } finally {
    uploading.value = false
    // 清空input值，允许重新选择同一个文件
    if (target) target.value = ''
  }
}

// 保存设置
const handleSave = async () => {
  if (!validateAll()) {
    console.log('❌ 表单验证失败')
    return
  }
  
  try {
    loading.value = true
    console.log('💾 开始保存用户设置...')
    
    const updateData = {
      username: formData.name,
      location: formData.location,
      bio: formData.bio,
      phone: formData.phone,
      wechat: formData.wechat,
      profession: formData.profession,
      workType: formData.workType,
      workTime: formData.workTime,
      cooperationMode: formData.cooperationMode,
      gender: formData.gender,
      avatarUrl: formData.avatarUrl
    }
    
    console.log('📝 保存数据:', updateData)
    
    // 调用API保存用户设置
    const response = await http.put('/auth/profile', updateData)
    
    if (response.success) {
      console.log('✅ 用户设置保存成功')
      
      // 更新store中的用户信息
      if (userStore.user) {
        userStore.user.username = formData.name
        userStore.user.location = formData.location
        userStore.user.bio = formData.bio
        userStore.user.avatar = formData.avatarUrl
      }
      
      // 显示成功提示
      showSuccess.value = true
      setTimeout(() => {
        showSuccess.value = false
      }, 3000)
    } else {
      throw new Error(response.message || '保存失败')
    }
    
  } catch (error: any) {
    console.error('❌ 保存用户设置失败:', error)
    alert(error.message || '保存失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadUserProfile()
  document.title = '个人设置 - 小概率'
})
</script>

<style scoped>
.settings-page {
  background: #f8fafc;
  min-height: 100vh;
  padding-top: 64px;
}

.main-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px 24px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.page-subtitle {
  font-size: 16px;
  color: #64748b;
}

.settings-form {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-section {
  padding: 32px;
  border-bottom: 1px solid #e2e8f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 24px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.form-input,
.form-textarea {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  background: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error,
.form-textarea.error {
  border-color: #ef4444;
}

.form-input.disabled {
  background: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.form-help {
  color: #6b7280;
  font-size: 12px;
  margin-top: 4px;
}

/* 头像上传样式 */
.avatar-upload-group {
  margin-bottom: 32px;
}

.avatar-upload-container {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid #e2e8f0;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
}

.avatar-preview:hover {
  border-color: #3b82f6;
  transform: scale(1.02);
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #9ca3af;
  font-size: 12px;
  padding: 8px;
}

.avatar-placeholder i {
  font-size: 20px;
  margin-bottom: 4px;
  color: #6b7280;
}

.avatar-upload-text {
  flex: 1;
  padding-top: 8px;
}

.avatar-upload-text p {
  margin: 0;
  color: #6b7280;
  font-size: 12px;
  line-height: 1.4;
}

.skill-input-group {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.add-skill-btn {
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
  white-space: nowrap;
}

.add-skill-btn:hover {
  background: #2563eb;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: #eff6ff;
  color: #3b82f6;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dbeafe;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.skill-tag:hover {
  background: #fecaca;
  color: #dc2626;
  border-color: #fca5a5;
}

.save-button-container {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
}

.save-button {
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.save-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.save-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.success-toast {
  position: fixed;
  top: 100px;
  right: 32px;
  background: #10b981;
  color: white;
  padding: 16px 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
  z-index: 2000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    padding: 20px 16px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .form-section {
    padding: 24px 20px;
  }
  
  .save-button-container {
    bottom: 20px;
    right: 20px;
  }
  
  .save-button {
    padding: 14px 24px;
    font-size: 14px;
  }
  
  .success-toast {
    right: 20px;
    left: 20px;
    text-align: center;
  }
}
</style>