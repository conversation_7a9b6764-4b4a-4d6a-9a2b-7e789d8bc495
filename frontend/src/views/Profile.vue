<template>
  <div class="profile-page">
    <!-- 导航栏 -->
    <Navbar />

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 左侧栏 -->
      <aside class="sidebar">
        <!-- 个人信息卡片 -->
        <div class="profile-card">
          <!-- 认证标识 -->
          <div v-if="userProfile.certificationStatus === 'certified'" class="certification-badge">
            <i class="fas fa-shield-alt"></i>
            <span>已认证简历</span>
          </div>
          
          <div class="profile-avatar">
            {{ userProfile.name ? userProfile.name.charAt(0) : '?' }}
          </div>
          <h1 class="profile-name">
            {{ userProfile.name || '请完善个人信息' }}
          </h1>
          <p class="profile-title">
            {{ userProfile.title || '请设置职位' }}
          </p>
          <div class="profile-location">
            <span>📍</span>
            <span>{{ userProfile.location || '请设置位置' }}</span>
          </div>
          
          <!-- 联系方式区域 -->
          <div v-if="!isOwnProfile" class="contact-section">
            <div v-if="!hasViewedContact" class="contact-locked">
              <div class="lock-icon">
                <i class="fas fa-lock"></i>
              </div>
              <p class="lock-text">联系方式已隐藏</p>
              <button @click="showContactPayment" class="btn btn-contact">
                <i class="fas fa-eye"></i>
                查看联系方式 ¥{{ contactViewPrice }}
              </button>
            </div>
            <div v-else class="contact-info">
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span>{{ userProfile.email }}</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>{{ userProfile.phone }}</span>
              </div>
              <div v-if="userProfile.wechat" class="contact-item">
                <i class="fab fa-weixin"></i>
                <span>{{ userProfile.wechat }}</span>
              </div>
            </div>
          </div>
          
          <!-- 工作偏好信息 -->
          <div class="work-preferences">
            <div class="preference-grid">
              <div class="preference-item">
                <div class="preference-label">工作性质</div>
                <div class="preference-value tag">
                  {{ userProfile.workType || '未设置' }}
                </div>
              </div>
              
              <div class="preference-item">
                <div class="preference-label">合作方式</div>
                <div class="preference-value paid">
                  {{ userProfile.cooperationType || '未设置' }}
                </div>
              </div>
              
              <div class="preference-item preference-full">
                <div class="preference-label">工作时间</div>
                <div class="preference-value">
                  {{ userProfile.workTime || '未设置' }}
                </div>
              </div>
              
              <div class="preference-item preference-full">
                <div class="preference-label">工作地点</div>
                <div class="preference-value remote">
                  {{ userProfile.workLocation || '未设置' }}
                </div>
              </div>
            </div>
          </div>
          
          <div class="profile-stats">
            <div class="stat-item">
              <div class="stat-number">{{ userProfile.completedProjects || 0 }}</div>
              <div class="stat-label">完成项目</div>
            </div>
          </div>
          
          <div class="profile-actions">
            <button v-if="isOwnProfile" @click="editBasicInfo" class="btn btn-primary">
              {{ userProfile.name ? '编辑资料' : '完善资料' }}
            </button>
            <div v-else class="visitor-actions">
              <button @click="contactUser" class="btn btn-primary">
                <i class="fas fa-comments"></i>
                联系TA
              </button>
              <button @click="inviteToProject" class="btn btn-secondary">
                <i class="fas fa-handshake"></i>
                邀请合作
              </button>
            </div>
          </div>
        </div>
        
        <!-- 技能卡片 -->
        <div class="skills-card">
          <h3 class="card-title">技能专长</h3>
          <div class="skills-list" v-if="userProfile.skills && userProfile.skills.length > 0">
            <span 
              v-for="skill in userProfile.skills" 
              :key="skill" 
              class="skill-tag"
              :class="{ 'certified': userProfile.certificationStatus === 'certified' }"
            >
              {{ skill }}
              <i v-if="userProfile.certificationStatus === 'certified'" class="fas fa-shield-check skill-verified"></i>
            </span>
          </div>
          <div v-else class="empty-state">
            <p>还没有添加技能</p>
            <button v-if="isOwnProfile" @click="editSkills" class="btn-link">添加技能</button>
          </div>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="main-content">
        <!-- 关于我 -->
        <section class="about-section">
          <div class="section-header">
            <h2 class="section-title">关于我</h2>
            <a v-if="isOwnProfile" href="#" @click.prevent="editAbout" class="edit-btn">编辑</a>
          </div>
          <div v-if="userProfile.about">
            <p class="about-text">{{ userProfile.about }}</p>
          </div>
          <div v-else class="empty-state">
            <p>还没有添加个人介绍</p>
            <button v-if="isOwnProfile" @click="editAbout" class="btn-link">添加介绍</button>
          </div>
        </section>

        <!-- 工作经验 -->
        <section class="experience-section">
          <div class="section-header">
            <h2 class="section-title">工作经验</h2>
            <a v-if="isOwnProfile" href="#" @click.prevent="editExperience" class="edit-btn">编辑</a>
            <div v-if="!isOwnProfile && userProfile.certificationStatus === 'certified'" class="verified-badge">
              <i class="fas fa-shield-check"></i>
              <span>已认证</span>
            </div>
          </div>
          <div v-if="userProfile.experiences && userProfile.experiences.length > 0" class="experience-list">
            <div 
              v-for="exp in userProfile.experiences" 
              :key="exp.id" 
              class="experience-item"
              :class="{ 'certified': userProfile.certificationStatus === 'certified' }"
            >
              <h3 class="experience-title">{{ exp.title }}</h3>
              <p class="experience-company">{{ exp.company }}</p>
              <p class="experience-duration">{{ exp.duration }}</p>
              <p class="experience-desc">{{ exp.description }}</p>
            </div>
          </div>
          <div v-else class="empty-state">
            <p>还没有添加工作经验</p>
            <button v-if="isOwnProfile" @click="editExperience" class="btn-link">添加经验</button>
          </div>
        </section>

        <!-- 项目时间线 -->
        <section class="timeline-section">
          <div class="section-header">
            <h2 class="section-title">项目时间线</h2>
            <a v-if="isOwnProfile" href="#" @click.prevent="editProjects" class="edit-btn">编辑</a>
            <div v-if="!isOwnProfile && userProfile.certificationStatus === 'certified'" class="verified-badge">
              <i class="fas fa-shield-check"></i>
              <span>已认证</span>
            </div>
          </div>
          <div v-if="userProfile.projects && userProfile.projects.length > 0" class="timeline">
            <div 
              v-for="project in userProfile.projects" 
              :key="project.id" 
              class="timeline-item"
              :class="{ 'certified': userProfile.certificationStatus === 'certified' }"
            >
              <div class="timeline-date">{{ project.date }}</div>
              <h3 class="timeline-title">{{ project.title }}</h3>
              <p class="timeline-desc">{{ project.description }}</p>
              <div v-if="project.technologies" class="timeline-metrics">
                <span 
                  v-for="tech in project.technologies" 
                  :key="tech" 
                  class="timeline-metric"
                >
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>
          <div v-else class="empty-state">
            <p>还没有添加项目经历</p>
            <button v-if="isOwnProfile" @click="editProjects" class="btn-link">添加项目</button>
          </div>
        </section>
      </main>
    </div>

    <!-- 联系方式支付弹窗 -->
    <ContactPaymentModal 
      v-if="showContactModal"
      :user-name="userProfile.name"
      :price="contactViewPrice"
      @close="showContactModal = false"
      @success="handleContactPaymentSuccess"
    />

    <!-- 编辑弹窗 -->
    <div v-if="editMode.show" class="edit-modal">
      <div class="edit-modal-content">
        <div class="edit-modal-header">
          <h3>{{ editMode.title }}</h3>
          <button @click="closeEdit" class="close-btn">&times;</button>
        </div>
        <div class="edit-modal-body">
          <!-- 基本信息编辑 -->
          <div v-if="editMode.type === 'basic'">
            <div class="form-group">
              <label>姓名</label>
              <input v-model="editData.name" type="text" placeholder="请输入姓名" />
            </div>
            <div class="form-group">
              <label>职位</label>
              <input v-model="editData.title" type="text" placeholder="如：全栈开发工程师" />
            </div>
            <div class="form-group">
              <label>位置</label>
              <input v-model="editData.location" type="text" placeholder="如：北京·朝阳区" />
            </div>
            <div class="form-group">
              <label>工作性质</label>
              <select v-model="editData.workType">
                <option value="">请选择</option>
                <option value="全职">全职</option>
                <option value="兼职">兼职</option>
                <option value="实习">实习</option>
              </select>
            </div>
            <div class="form-group">
              <label>合作方式</label>
              <select v-model="editData.cooperationType">
                <option value="">请选择</option>
                <option value="付费合作">付费合作</option>
                <option value="股权合作">股权合作</option>
                <option value="免费合作">免费合作</option>
              </select>
            </div>
            <div class="form-group">
              <label>工作时间</label>
              <input v-model="editData.workTime" type="text" placeholder="如：下班后+周末" />
            </div>
            <div class="form-group">
              <label>工作地点</label>
              <select v-model="editData.workLocation">
                <option value="">请选择</option>
                <option value="远程协作">远程协作</option>
                <option value="现场办公">现场办公</option>
                <option value="混合办公">混合办公</option>
              </select>
            </div>
          </div>

          <!-- 关于我编辑 -->
          <div v-if="editMode.type === 'about'">
            <div class="form-group">
              <label>个人介绍</label>
              <textarea 
                v-model="editData.about" 
                placeholder="介绍一下你的经验、技能和职业目标..."
                rows="6"
              ></textarea>
            </div>
          </div>

          <!-- 技能编辑 -->
          <div v-if="editMode.type === 'skills'">
            <div class="form-group">
              <label>技能专长</label>
              <input 
                v-model="skillInput" 
                @keyup.enter="addSkill"
                type="text" 
                placeholder="输入技能后按回车添加" 
              />
              <div class="skills-list" style="margin-top: 10px;">
                <span 
                  v-for="(skill, index) in editData.skills" 
                  :key="index"
                  class="skill-tag editable"
                  @click="removeSkill(index)"
                >
                  {{ skill }} ×
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="edit-modal-footer">
          <button @click="closeEdit" class="btn btn-secondary">取消</button>
          <button @click="saveEdit" class="btn btn-primary">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import Navbar from '../components/layout/Navbar.vue'
import ContactPaymentModal from '../components/profile/ContactPaymentModal.vue'
import { http } from '../utils/request'

const route = useRoute()

// 获取路由参数中的用户ID
const profileUserId = computed(() => {
  return route.params.id ? parseInt(route.params.id as string) : null
})

// 判断是否是本人的个人资料页
const isOwnProfile = computed(() => {
  // 如果没有ID参数，默认显示当前用户的资料
  if (!profileUserId.value) return true
  
  // 这里应该与当前登录用户的ID比较
  const currentUserId = 1 // 临时硬编码，实际应从用户store获取
  return profileUserId.value === currentUserId
})

// 用户资料数据
const userProfile = reactive({
  name: '',
  title: '',
  location: '',
  email: '',
  phone: '',
  wechat: '',
  workType: '',
  cooperationType: '',
  workTime: '',
  workLocation: '',
  completedProjects: 0,
  about: '',
  skills: [] as string[],
  experiences: [] as any[],
  projects: [] as any[],
  certificationStatus: 'none' // 'none', 'pending', 'certified'
})

// 联系方式相关
const hasViewedContact = ref(false)
const showContactModal = ref(false)
const contactViewPrice = ref(19)

// 编辑模式
const editMode = reactive({
  show: false,
  type: '',
  title: ''
})

// 编辑数据
const editData = reactive({
  name: '',
  title: '',
  location: '',
  workType: '',
  cooperationType: '',
  workTime: '',
  workLocation: '',
  about: '',
  skills: [] as string[]
})

const skillInput = ref('')

// 加载用户资料数据
const loadUserProfile = async () => {
  try {
    const userId = profileUserId.value || 1 // 如果没有ID参数，加载当前用户数据
    const response = await http.get(`/users/${userId}`)
    
    if (response.success) {
      Object.assign(userProfile, response.data.profile)
      
      // 如果是访客视图，检查是否已经查看过联系方式
      if (!isOwnProfile.value) {
        hasViewedContact.value = response.data.hasViewedContact || false
      }
    }
  } catch (error) {
    console.error('加载用户资料失败:', error)
    // 使用模拟数据
    loadMockData()
  }
}

const loadMockData = () => {
  // 模拟认证用户数据
  userProfile.name = '张雅婷'
  userProfile.title = '全栈开发工程师'
  userProfile.location = '北京·朝阳区'
  userProfile.email = '<EMAIL>'
  userProfile.phone = '138****8888'
  userProfile.wechat = 'zhang_dev_2024'
  userProfile.workType = '兼职'
  userProfile.cooperationType = '付费合作'
  userProfile.workTime = '下班后+周末'
  userProfile.workLocation = '远程协作'
  userProfile.completedProjects = 15
  userProfile.certificationStatus = 'certified' // 设置为已认证
  userProfile.about = `5年全栈开发经验，专注于Web应用开发和微服务架构设计。熟练掌握前端React生态和后端Node.js技术栈，有丰富的云服务部署经验。

喜欢挑战复杂的技术问题，擅长团队协作和项目管理。目前正在寻找有趣的创业项目或开源项目进行合作。

我相信技术的力量可以改变世界，也期待与志同道合的伙伴一起创造有价值的产品。`
  userProfile.skills = ['JavaScript', 'React', 'Node.js', 'Python', 'MySQL', 'MongoDB', 'AWS', 'Docker']
  userProfile.experiences = [
    {
      id: 1,
      title: '高级全栈工程师',
      company: '北京字节跳动科技有限公司',
      duration: '2021年3月 - 至今 · 3年',
      description: '负责公司内部管理系统的全栈开发，使用React + Node.js技术栈。主导了微服务架构改造，系统性能提升40%。带领3人团队完成多个核心业务模块开发。'
    },
    {
      id: 2,
      title: '前端开发工程师',
      company: '美团点评',
      duration: '2019年7月 - 2021年2月 · 1年8个月',
      description: '参与美团商家端产品开发，负责前端界面设计和用户体验优化。独立完成移动端H5页面开发，用户访问量提升25%。'
    }
  ]
  userProfile.projects = [
    {
      id: 1,
      date: '2023年8月 - 2024年1月',
      title: '企业级CRM系统',
      description: '为中小企业定制的客户关系管理系统，支持销售流程管理、客户数据分析等功能。系统上线后帮助客户提升销售效率30%，获得用户高度认可。',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Redis', 'Docker']
    },
    {
      id: 2,
      date: '2024年2月 - 至今',
      title: '在线教育平台',
      description: '支持直播授课、作业提交、学习进度跟踪的综合教育平台。目前已完成核心功能开发，正在进行用户测试和功能优化。',
      technologies: ['Vue.js', 'Express', 'MongoDB', 'WebRTC', 'Socket.io']
    },
    {
      id: 3,
      date: '2024年6月 - 预计',
      title: '智能推荐系统',
      description: '基于机器学习的内容推荐引擎，支持用户行为分析和个性化内容推荐。目标推荐准确率达到85%，大幅提升用户活跃度和留存率。',
      technologies: ['Python', 'TensorFlow', 'Flask', 'Elasticsearch', 'Kafka']
    }
  ]
}

// 联系方式相关方法
const showContactPayment = () => {
  showContactModal.value = true
}

const handleContactPaymentSuccess = () => {
  hasViewedContact.value = true
  showContactModal.value = false
  
  // 发送消息通知被查看者
  notifyContactViewed()
}

const notifyContactViewed = async () => {
  try {
    await http.post('/user/contact-viewed', {
      viewedUserId: profileUserId.value,
      viewerUserId: 1 // 当前用户ID
    })
  } catch (error) {
    console.error('发送查看通知失败:', error)
  }
}

// 访客操作方法
const contactUser = () => {
  // 跳转到私信页面或打开联系弹窗
  alert('联系用户功能开发中...')
}

const inviteToProject = () => {
  // 跳转到项目邀请页面
  alert('项目邀请功能开发中...')
}

// 编辑函数
const editBasicInfo = () => {
  editMode.show = true
  editMode.type = 'basic'
  editMode.title = '编辑基本信息'
  
  // 复制当前数据到编辑数据
  editData.name = userProfile.name
  editData.title = userProfile.title
  editData.location = userProfile.location
  editData.workType = userProfile.workType
  editData.cooperationType = userProfile.cooperationType
  editData.workTime = userProfile.workTime
  editData.workLocation = userProfile.workLocation
}

const editAbout = () => {
  editMode.show = true
  editMode.type = 'about'
  editMode.title = '编辑个人介绍'
  editData.about = userProfile.about
}

const editSkills = () => {
  editMode.show = true
  editMode.type = 'skills'
  editMode.title = '编辑技能专长'
  editData.skills = [...userProfile.skills]
}

const editExperience = () => {
  alert('工作经验编辑功能正在开发中...')
}

const editProjects = () => {
  alert('项目经历编辑功能正在开发中...')
}

const closeEdit = () => {
  editMode.show = false
  editMode.type = ''
  editMode.title = ''
}

const saveEdit = () => {
  if (editMode.type === 'basic') {
    userProfile.name = editData.name
    userProfile.title = editData.title
    userProfile.location = editData.location
    userProfile.workType = editData.workType
    userProfile.cooperationType = editData.cooperationType
    userProfile.workTime = editData.workTime
    userProfile.workLocation = editData.workLocation
  } else if (editMode.type === 'about') {
    userProfile.about = editData.about
  } else if (editMode.type === 'skills') {
    userProfile.skills = [...editData.skills]
  }
  
  // 这里可以调用API保存数据
  console.log('保存用户资料:', userProfile)
  
  closeEdit()
}

const addSkill = () => {
  if (skillInput.value.trim() && !editData.skills.includes(skillInput.value.trim())) {
    editData.skills.push(skillInput.value.trim())
    skillInput.value = ''
  }
}

const removeSkill = (index: number) => {
  editData.skills.splice(index, 1)
}

onMounted(() => {
  loadUserProfile()
  document.title = '个人资料 - 小概率'
})
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.profile-page {
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  background: #fafbfc;
  color: #2d3748;
  line-height: 1.65;
  letter-spacing: 0.2px;
  min-height: 100vh;
  padding-top: 64px;
}

/* 导航栏样式已移到统一的Navbar组件中 */

/* 主要内容区域 */
.main-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
  display: grid;
  grid-template-columns: 310px 1fr;
  gap: 40px;
}

/* 左侧栏 */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 个人信息卡片 */
.profile-card {
  background: white;
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  text-align: center;
  border: 1px solid #e2e8f0;
  position: relative;
}

/* 认证标识 */
.certification-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #059669, #047857);
  color: white;
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
}

.certification-badge i {
  font-size: 0.7rem;
}

/* 联系方式区域 */
.contact-section {
  margin: 20px 0;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.contact-locked {
  text-align: center;
  padding: 20px 0;
}

.lock-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #fbbf24;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
  font-size: 16px;
}

.lock-text {
  color: #64748b;
  margin-bottom: 16px;
  font-size: 0.9rem;
}

.btn-contact {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn-contact:hover {
  background: linear-gradient(135deg, #d97706, #b45309);
  transform: translateY(-1px);
}

.contact-info {
  text-align: left;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 0;
  font-size: 0.9rem;
  color: #4a5568;
}

.contact-item i {
  width: 16px;
  color: #9ca3af;
}

/* 访客操作按钮 */
.visitor-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.visitor-actions .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.9rem;
}

/* 认证相关样式 */
.skill-tag.certified {
  background: #d1fae5;
  color: #059669;
  border: 1px solid #a7f3d0;
  position: relative;
}

.skill-verified {
  font-size: 0.7rem;
  margin-left: 4px;
  color: #059669;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #d1fae5;
  color: #059669;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 600;
}

.verified-badge i {
  font-size: 0.7rem;
}

.experience-item.certified,
.timeline-item.certified {
  border-left: 3px solid #059669;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  position: relative;
}

.experience-item.certified::after,
.timeline-item.certified::after {
  content: '';
  position: absolute;
  top: 12px;
  right: 12px;
  width: 20px;
  height: 20px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" fill="%23059669" viewBox="0 0 24 24"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>') no-repeat center;
  background-size: contain;
  opacity: 0.3;
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, #2b6cb0, #2c5aa0);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 36px;
  font-weight: 500;
  margin: 0 auto 24px;
  border: 3px solid #f7fafc;
}

.profile-name {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2d3748;
  letter-spacing: -0.3px;
}

.profile-title {
  font-size: 15px;
  color: #718096;
  margin-bottom: 20px;
  font-weight: 400;
}

.profile-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #64748b;
  font-size: 14px;
  margin-bottom: 24px;
}

/* 工作偏好信息 */
.work-preferences {
  margin-bottom: 20px;
}

.preference-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.preference-item {
  text-align: left;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.preference-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.preference-value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
}

.preference-value.tag {
  display: inline-block;
  background: #e0f2fe;
  color: #0066cc;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
}

.preference-value.paid {
  color: #059669;
}

.preference-value.remote {
  color: #7c3aed;
}

.preference-full {
  grid-column: 1 / -1;
}

.profile-stats {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #0066cc;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.profile-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: #0066cc;
  color: white;
}

.btn-primary:hover {
  background: #0052a3;
}

/* 技能卡片 */
.skills-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #1e293b;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: #f1f5f9;
  color: #0066cc;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

.skill-tag.editable {
  cursor: pointer;
  background: #fee2e2;
  color: #dc2626;
}

.skill-tag.editable:hover {
  background: #fecaca;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 关于我 */
.about-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.edit-btn {
  color: #0066cc;
  font-size: 14px;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 6px;
  transition: background 0.3s;
}

.edit-btn:hover {
  background: #f1f5f9;
}

.about-text {
  color: #475569;
  line-height: 1.7;
  white-space: pre-line;
}

/* 工作经验 */
.experience-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.experience-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.experience-item {
  padding-bottom: 24px;
  border-bottom: 1px solid #f1f5f9;
}

.experience-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.experience-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.experience-company {
  font-size: 14px;
  color: #0066cc;
  margin-bottom: 4px;
}

.experience-duration {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 12px;
}

.experience-desc {
  font-size: 14px;
  color: #475569;
  line-height: 1.6;
}

/* 项目时间线 */
.timeline-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.timeline {
  position: relative;
  padding-left: 30px;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  background: #f8fafc;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: -37px;
  top: 25px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #3b82f6;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #3b82f6;
}

.timeline-date {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.timeline-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8px;
}

.timeline-desc {
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 12px;
}

.timeline-metrics {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  font-size: 12px;
  flex-wrap: wrap;
}

.timeline-metric {
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.empty-state p {
  margin-bottom: 12px;
}

.btn-link {
  background: none;
  border: none;
  color: #0066cc;
  text-decoration: underline;
  cursor: pointer;
  font-size: 14px;
}

.btn-link:hover {
  color: #0052a3;
}

/* 编辑弹窗 */
.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.edit-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.edit-modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #64748b;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background: #f1f5f9;
}

.edit-modal-body {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.edit-modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px 16px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .edit-modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style> 