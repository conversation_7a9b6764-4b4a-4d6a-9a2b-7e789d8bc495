<template>
  <div class="project-publish-page">
    <Navbar />
    <div class="project-publish-container">
      <!-- 向导容器 -->
      <div class="wizard-container">
        <!-- 进度指示器 -->
        <div class="progress-header">
          <div class="progress-steps">
            <div
              v-for="(step, index) in steps"
              :key="index"
              :class="[
                'step',
                { active: currentStep === index + 1 },
                { completed: currentStep > index + 1 }
              ]"
            >
              <div class="step-circle">
                <span v-if="currentStep > index + 1">✓</span>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <div class="step-label">{{ step.label }}</div>
            </div>
          </div>
          <div class="progress-info">
            <h2 class="progress-title">{{ steps[currentStep - 1].title }}</h2>
            <p class="progress-subtitle">{{ steps[currentStep - 1].subtitle }}</p>
          </div>
        </div>

        <!-- 表单内容 -->
        <div class="form-container">
          <!-- 第一步：项目基本信息 -->
          <div
            v-show="currentStep === 1"
            class="step-content"
            :class="{ active: currentStep === 1 }"
          >
            <!-- <div class="step-header">
              <h3 class="step-title">项目基本信息</h3>
              <p class="step-description">完善项目的基本信息，让合作伙伴更好地了解你的项目</p>
            </div> -->

            <form @submit.prevent="nextStep">
              <div class="form-section">
                <h4 class="section-title">📋 基本信息</h4>
                
                <div class="form-group">
                  <label class="form-label">项目名称 <span class="required">*</span></label>
                  <input
                    v-model="formData.title"
                    type="text"
                    :class="['form-input', { error: errors.title }]"
                    placeholder="给你的项目起一个吸引人的名字"
                    required
                    maxlength="200"
                    @blur="validateField('title', formData.title)"
                  />
                  <div v-if="errors.title" class="error-message">{{ errors.title }}</div>
                  <div v-else class="form-hint">建议：简洁明了，突出项目特色（5-100字符）</div>
                </div>
                
                                  <div class="form-group">
                    <label class="form-label">一句话概括 <span class="required">*</span></label>
                    <input
                      v-model="formData.summary"
                      type="text"
                      :class="['form-input', { error: errors.summary }]"
                      placeholder="用一句话概括项目的核心价值"
                      required
                      maxlength="20"
                      @blur="validateField('summary', formData.summary)"
                    />
                    <div v-if="errors.summary" class="error-message">{{ errors.summary }}</div>
                    <div v-else class="form-hint">最多20个字符，让人一眼就能理解你的项目</div>
                  </div>
                
                <div class="form-grid">
                  <div class="form-group">
                    <label class="form-label">项目分类 <span class="required">*</span></label>
                    <select 
                      v-model="formData.category" 
                      :class="['form-select', { error: errors.category }]"
                      required
                      @blur="validateField('category', formData.category)"
                    >
                      <option value="">选择项目分类</option>
                      <option value="mobile">移动应用</option>
                      <option value="web">Web开发</option>
                      <option value="ai">AI/机器学习</option>
                      <option value="blockchain">区块链</option>
                      <option value="game">游戏开发</option>
                      <option value="social">社交网络</option>
                      <option value="ecommerce">电商平台</option>
                      <option value="fintech">金融科技</option>
                      <option value="education">教育培训</option>
                      <option value="health">健康生活</option>
                      <option value="enterprise">企业软件</option>
                      <option value="data">数据分析</option>
                    </select>
                    <div v-if="errors.category" class="error-message">{{ errors.category }}</div>
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">项目预算 <span class="required">*</span></label>
                    <select 
                      v-model="formData.budgetRange" 
                      :class="['form-select', { error: errors.budgetRange }]"
                      required
                      @blur="validateField('budgetRange', formData.budgetRange)"
                    >
                      <option value="">选择预算范围</option>
                      <option value="10000-50000">1万-5万</option>
                      <option value="50000-100000">5万-10万</option>
                      <option value="100000-300000">10万-30万</option>
                      <option value="300000-500000">30万-50万</option>
                      <option value="500000+">50万以上</option>
                    </select>
                    <div v-if="errors.budgetRange" class="error-message">{{ errors.budgetRange }}</div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="form-label">项目描述 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.description"
                    :class="['form-input', 'form-textarea', { error: errors.description }]"
                    placeholder="详细描述你的项目：功能特点、技术要求、目标用户、预期效果等..."
                    required
                    rows="5"
                    @blur="validateField('description', formData.description)"
                  ></textarea>
                  <div v-if="errors.description" class="error-message">{{ errors.description }}</div>
                  <div v-else class="form-hint">至少50字，详细描述有助于吸引合适的合作伙伴</div>
                </div>
              </div>

              <div class="form-section">
                <h4 class="section-title">🔗 项目链接</h4>
                
                <div class="form-group">
                  <label class="form-label">Demo链接</label>
                  <input
                    v-model="formData.demoUrl"
                    type="url"
                    class="form-input"
                    placeholder="https://demo.example.com"
                  />
                  <div class="form-hint">可选：如果有在线演示或原型，可以添加链接</div>
                </div>
              </div>

              <div class="form-section">
                <h4 class="section-title">📷 项目图片</h4>
                
                <div class="form-group">
                  <label class="form-label">项目展示图片</label>
                  <div class="image-upload-container">
                    <!-- 图片预览区 -->
                    <div class="image-preview-grid">
                      <div 
                        v-for="(image, index) in formData.images" 
                        :key="index" 
                        class="image-preview-item"
                      >
                        <img :src="image.url" :alt="image.name" class="preview-image" />
                        <div class="image-overlay">
                          <button 
                            type="button" 
                            class="remove-image-btn" 
                            @click="removeImage(index)"
                            title="删除图片"
                          >
                            ✕
                          </button>
                        </div>
                        <div class="image-info">
                          <span class="image-name">{{ image.name }}</span>
                        </div>
                      </div>
                      
                      <!-- 上传按钮 -->
                      <div 
                        v-if="formData.images.length < 5" 
                        class="image-upload-btn"
                        @click="triggerImageUpload"
                      >
                        <div class="upload-icon">📷</div>
                        <div class="upload-text">
                          <div>点击上传</div>
                          <div class="upload-hint">支持JPG, PNG格式</div>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 隐藏的文件输入 -->
                    <input
                      ref="imageInput"
                      type="file"
                      accept="image/jpeg,image/png,image/webp"
                      multiple
                      style="display: none"
                      @change="handleImageUpload"
                    />
                    
                    <div class="form-hint">
                      最多上传5张图片，每张不超过5MB，支持JPG、PNG、WebP格式
                    </div>
                    
                    <!-- 上传进度 -->
                    <div v-if="uploadProgress.show" class="upload-progress">
                      <div class="progress-bar">
                        <div 
                          class="progress-fill" 
                          :style="{ width: uploadProgress.percent + '%' }"
                        ></div>
                      </div>
                      <div class="progress-text">
                        正在上传... {{ uploadProgress.percent }}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>

          <!-- 第二步：团队和需求信息 -->
          <div
            v-show="currentStep === 2"
            class="step-content"
            :class="{ active: currentStep === 2 }"
          >
            <div class="step-header">
              <h3 class="step-title">团队和需求信息</h3>
              <p class="step-description">介绍你的团队现状和招募需求</p>
            </div>

            <form @submit.prevent="nextStep">
              <div class="form-section">
                <h4 class="section-title">👥 现有团队</h4>
                
                <div class="dynamic-list">
                  <div
                    v-for="(member, index) in formData.teamInfo"
                    :key="index"
                    class="dynamic-item"
                  >
                    <div class="item-header">
                      <span class="item-title">团队成员 #{{ index + 1 }}</span>
                      <button
                        v-if="formData.teamInfo.length > 1"
                        @click="removeTeamMember(index)"
                        type="button"
                        class="remove-btn"
                      >
                        删除
                      </button>
                    </div>
                    <div class="form-grid">
                      <div class="form-group">
                        <label class="form-label">姓名 <span class="required">*</span></label>
                        <input
                          v-model="member.name"
                          type="text"
                          class="form-input"
                          placeholder="成员姓名"
                          required
                        />
                      </div>
                      <div class="form-group">
                        <label class="form-label">角色 <span class="required">*</span></label>
                        <input
                          v-model="member.role"
                          type="text"
                          class="form-input"
                          placeholder="如：产品经理"
                          required
                        />
                      </div>
                    </div>
                    <!-- <div class="form-group">
                      <label class="form-label">背景介绍</label>
                      <textarea
                        v-model="member.background"
                        class="form-input"
                        placeholder="简要介绍成员的专业背景和经验"
                        rows="3"
                      ></textarea>
                    </div> -->
                    <div class="form-group">
                      <label class="form-label">个人介绍</label>
                      <textarea
                        v-model="member.introduction"
                        class="form-input"
                        placeholder="个人特点、兴趣爱好等"
                        rows="2"
                      ></textarea>
                    </div>
                  </div>
                </div>
                <button type="button" class="add-btn" @click="addTeamMember">
                  + 添加团队成员
                </button>
                
                <div class="form-group" style="margin-top: 20px;">
                  <label class="form-label">团队投入情况 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.teamInvestment"
                    class="form-input form-textarea"
                    placeholder="描述团队的投入程度、工作时间安排、已有进展等"
                    required
                    rows="4"
                  ></textarea>
                  <div class="form-hint">例如：全职投入、兼职开发、周末项目等</div>
                </div>
              </div>

              <div class="form-section">
                <h4 class="section-title">🔍 招募需求</h4>
                
                <div class="dynamic-list">
                  <div
                    v-for="(requirement, index) in formData.recruitmentInfo"
                    :key="index"
                    class="dynamic-item"
                  >
                    <div class="item-header">
                      <span class="item-title">招募岗位 #{{ index + 1 }}</span>
                      <button
                        v-if="formData.recruitmentInfo.length > 1"
                        @click="removeRequirement(index)"
                        type="button"
                        class="remove-btn"
                      >
                        删除
                      </button>
                    </div>
                    <div class="form-grid">
                      <div class="form-group">
                        <label class="form-label">岗位名称 <span class="required">*</span></label>
                        <input
                          v-model="requirement.position"
                          type="text"
                          class="form-input"
                          placeholder="如：前端开发工程师"
                          required
                        />
                      </div>
                      <div class="form-group">
                        <label class="form-label">合作方式 <span class="required">*</span></label>
                        <select v-model="requirement.cooperation" class="form-select" required>
                          <option value="">选择合作方式</option>
                          <option value="salary">薪资合作</option>
                          <option value="equity">股权合作</option>
                          <option value="mixed">薪资+股权</option>
                          <option value="profit">利润分成</option>
                        </select>
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="form-label">技能要求</label>
                      <input
                        v-model="requirement.skills"
                        type="text"
                        class="form-input"
                        placeholder="如：Vue.js, React, TypeScript（用逗号分隔）"
                      />
                    </div>
                    <div class="form-grid">
                      <div class="form-group">
                        <label class="form-label">薪资金额</label>
                        <input
                          v-model="requirement.salary"
                          type="text"
                          class="form-input"
                          placeholder="如：8000-12000"
                        />
                      </div>
                      <div class="form-group">
                        <label class="form-label">股权比例</label>
                        <input
                          v-model="requirement.equity"
                          type="text"
                          class="form-input"
                          placeholder="如：5%"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <button type="button" class="add-btn" @click="addRequirement">
                  + 添加招募岗位
                </button>
              </div>

              <div class="form-section">
                <h4 class="section-title">📍 工作方式</h4>
                
                <div class="form-grid">
                  <div class="form-group">
                    <label class="form-label">工作地点 <span class="required">*</span></label>
                    <select v-model="formData.workType" class="form-select" required>
                      <option value="">选择工作地点</option>
                      <option value="remote">远程工作</option>
                      <option value="onsite">现场工作</option>
                      <option value="hybrid">混合办公</option>
                    </select>
                  </div>
                  
                  <div class="form-group">
                    <label class="form-label">工作安排 <span class="required">*</span></label>
                    <select v-model="formData.workArrangement" class="form-select" required>
                      <option value="">选择工作安排</option>
                      <option value="fullTime">全职</option>
                      <option value="partTime">兼职</option>
                      <option value="contract">合同工</option>
                      <option value="intern">实习</option>
                    </select>
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="form-label">具体地址</label>
                  <input
                    v-model="formData.workLocation"
                    type="text"
                    class="form-input"
                    placeholder="如果不是完全远程，请填写具体工作地址"
                  />
                </div>
              </div>
            </form>
          </div>

          <!-- 第三步：项目进展 -->
          <div
            v-show="currentStep === 3"
            class="step-content"
            :class="{ active: currentStep === 3 }"
          >
            <div class="step-header">
              <h3 class="step-title">项目分析</h3>
              <p class="step-description">深入分析项目的商业价值和发展前景</p>
            </div>

            <form @submit.prevent="nextStep">
              <div class="form-section">
                <h4 class="section-title">📊 项目分析</h4>
                
                <div class="form-group">
                  <label class="form-label">最近进展 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.recentProgress"
                    class="form-input form-textarea"
                    placeholder="描述项目最近的重要进展，如产品开发、用户验证、市场反馈等"
                    required
                    rows="4"
                  ></textarea>
                </div>
                
                <div class="form-group">
                  <label class="form-label">用户分析 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.userAnalysis"
                    class="form-input form-textarea"
                    placeholder="详细描述目标用户群体、用户需求和市场痛点"
                    required
                    rows="4"
                  ></textarea>
                </div>

              </div>

              <div class="form-section">
                <h4 class="section-title">💰 商业模式</h4>
                
                <div class="form-group">
                  <label class="form-label">盈利模式 <span class="required">*</span></label>
                  <select v-model="formData.businessModel" class="form-select" required>
                    <option value="">选择主要盈利模式</option>
                    <option value="advertising">广告收入</option>
                    <option value="subscription">订阅付费</option>
                    <option value="one-time">一次性付费</option>
                    <option value="commission">交易佣金</option>
                    <option value="enterprise">企业服务</option>
                    <option value="ecommerce">电商销售</option>
                    <option value="data">数据服务</option>
                    <option value="platform">平台抽成</option>
                    <option value="freemium">免费增值</option>
                    <option value="undetermined">尚未确定</option>
                  </select>
                </div>
                
                <div class="form-group">
                  <label class="form-label">商业模式详述 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.businessDescription"
                    class="form-input form-textarea"
                    placeholder="详细描述商业模式、盈利方式、定价策略和市场预期"
                    required
                    rows="4"
                  ></textarea>
                </div>
              </div>

              <div class="form-section">
                <h4 class="section-title">📅 项目规划</h4>
                
                <div class="form-group">
                  <label class="form-label">项目周期 <span class="required">*</span></label>
                  <select v-model="formData.durationMonths" class="form-select" required>
                    <option value="">选择项目周期</option>
                    <option value="1">1个月</option>
                    <option value="2">2个月</option>
                    <option value="3">3个月</option>
                    <option value="6">6个月</option>
                    <option value="12">12个月</option>
                    <option value="24">24个月</option>
                  </select>
                </div>
              </div>
            </form>
          </div>

          <!-- 第四步：项目故事 -->
          <div
            v-show="currentStep === 4"
            class="step-content"
            :class="{ active: currentStep === 4 }"
          >
            <div class="step-header">
              <h3 class="step-title">项目故事</h3>
              <p class="step-description">分享项目的起源和发展愿景</p>
            </div>

            <form @submit.prevent="submitProject">
              <div class="form-section">
                <h4 class="section-title">🎯 项目故事</h4>
                
                <div class="form-group">
                  <label class="form-label">项目起源 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.projectOrigin"
                    class="form-input form-textarea"
                    placeholder="描述项目的初始灵感、创立背景和要解决的问题"
                    required
                    rows="4"
                  ></textarea>
                </div>
                
                <div class="form-group">
                  <label class="form-label">竞争优势 <span class="required">*</span></label>
                  <textarea
                    v-model="formData.competitiveAdvantage"
                    class="form-input form-textarea"
                    placeholder="相比竞争对手，你的项目有什么独特之处？技术、产品、团队或商业模式上的优势"
                    required
                    rows="4"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label class="form-label">发展愿景</label>
                  <textarea
                    v-model="formData.vision"
                    class="form-input form-textarea"
                    placeholder="描述项目的长远发展目标和愿景"
                    rows="3"
                  ></textarea>
                  <div class="form-hint">可选：分享你对项目未来的期望和规划</div>
                </div>

                <div class="form-group">
                  <label class="form-label">团队理念</label>
                  <textarea
                    v-model="formData.teamPhilosophy"
                    class="form-input form-textarea"
                    placeholder="描述团队的工作理念和价值观"
                    rows="3"
                  ></textarea>
                  <div class="form-hint">可选：让合作伙伴了解团队文化</div>
                </div>
              </div>
            </form>
          </div>

          <!-- 表单操作按钮 -->
          <div class="form-actions">
            <button
              v-if="currentStep > 1"
              type="button"
              class="btn btn-secondary"
              @click="prevStep"
            >
              ← 上一步
            </button>
            <div v-else></div>
            
            <div class="actions-right">
              <!-- 验证错误提示 -->
              <div v-if="!canProceed && Object.values(errors).some(error => error)" class="validation-hint">
                <span class="hint-icon">⚠️</span>
                <span>请完善表单信息后继续</span>
              </div>
              
              <button
                v-if="currentStep < 4"
                type="button"
                class="btn btn-primary"
                @click="nextStep"
                :disabled="!canProceed"
              >
                下一步 →
              </button>
              <button
                v-else
                type="button"
                class="btn btn-primary"
                @click="submitProject"
                :disabled="!canProceed || isSubmitting"
              >
                {{ isSubmitting ? '提交中...' : '🚀 提交审核' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import Navbar from '@/components/layout/Navbar.vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 步骤配置（完全匹配原型图的4步流程）
const steps = ref([
  { 
    label: '项目信息', 
    // title: '项目基本信息',
    // subtitle: '让我们从项目的基本信息开始'
  },
  { 
    label: '团队需求', 
    // title: '团队和需求信息',
    // subtitle: '介绍你的团队现状和招募需求'
  },
  { 
    label: '项目进展', 
    // title: '项目分析',
    // subtitle: '深入分析项目的商业价值和发展前景'
  },
  { 
    label: '项目故事', 
    // title: '项目故事',
    // subtitle: '分享项目的起源和发展愿景'
  }
])

// 当前步骤
const currentStep = ref(1)

// 提交状态
const isSubmitting = ref(false)

// 表单数据
const formData = reactive({
  // 基本信息
  title: '',
  summary: '',
  category: '',
  description: '',
  demoUrl: '',
  
  // 项目图片
  images: [] as Array<{
    id?: string;
    name: string;
    url: string;
    file?: File;
  }>,
  
  // 团队信息
  teamInfo: [
    {
      name: '',
      role: '',
      background: '',
      introduction: ''
    }
  ],
  teamInvestment: '',
  
  // 招募信息
  recruitmentInfo: [
    {
      position: '',
      cooperation: '',
      skills: '',
      salary: '',
      equity: ''
    }
  ],
  
  // 工作方式
  workType: '',
  workArrangement: '',
  workLocation: '',
  
  // 项目分析
  recentProgress: '',
  userAnalysis: '',
  businessModel: '',
  businessDescription: '',
  durationMonths: '',
  isCompanyRegistered: false,
  companyName: '',
  
  // 项目故事
  projectOrigin: '',
  competitiveAdvantage: '',
  vision: '',
  teamPhilosophy: ''
})

// 表单验证错误
const errors = reactive({
  title: '',
  summary: '',
  category: '',
  description: '',
  teamInfo: '',
  teamInvestment: '',
  recruitmentInfo: '',
  workType: '',
  workArrangement: '',
  recentProgress: '',
  userAnalysis: '',
  businessModel: '',
  businessDescription: '',
  durationMonths: '',
  projectOrigin: '',
  competitiveAdvantage: ''
})

// 图片上传相关状态
const imageInput = ref()
const uploadProgress = reactive({
  show: false,
  percent: 0
})

// 字段验证规则
const validateField = (field: string, value: any) => {
  let errorMessage = ''
  
  switch (field) {
    case 'title':
      if (!value) errorMessage = '项目名称不能为空'
      else if (value.length < 5) errorMessage = '项目名称至少5个字符'
      else if (value.length > 100) errorMessage = '项目名称不能超过100个字符'
      break
    case 'summary':
      if (!value) errorMessage = '一句话概括不能为空'
      else if (value.length > 20) errorMessage = '不能超过20个字符'
      break
    case 'category':
      if (!value) errorMessage = '请选择项目分类'
      break
    case 'description':
      if (!value) errorMessage = '项目描述不能为空'
      else if (value.length < 50) errorMessage = '项目描述至少50个字符'
      break
    case 'teamInvestment':
      if (!value) errorMessage = '请描述团队投入情况'
      break
    case 'workType':
      if (!value) errorMessage = '请选择工作地点'
      break
    case 'workArrangement':
      if (!value) errorMessage = '请选择工作安排'
      break
    case 'recentProgress':
      if (!value) errorMessage = '请描述最近进展'
      break
    case 'userAnalysis':
      if (!value) errorMessage = '请进行用户分析'
      break
    case 'businessModel':
      if (!value) errorMessage = '请选择盈利模式'
      break
    case 'businessDescription':
      if (!value) errorMessage = '请详述商业模式'
      break
    case 'durationMonths':
      if (!value) errorMessage = '请选择项目周期'
      break
    case 'projectOrigin':
      if (!value) errorMessage = '请描述项目起源'
      break
    case 'competitiveAdvantage':
      if (!value) errorMessage = '请描述竞争优势'
      break
  }
  
  // 更新错误状态
  if (field in errors) {
    errors[field as keyof typeof errors] = errorMessage
  }
  
  return errorMessage
}

// 检查当前步骤是否有效（不影响错误状态）
const checkCurrentStepValidity = () => {
  if (currentStep.value === 1) {
    // 验证第一步字段：只验证基本信息
    const fields = ['title', 'summary', 'category', 'description']
    return fields.every(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      return !error
    })
  }
  
  if (currentStep.value === 2) {
    // 验证第二步字段：团队和招募信息
    const fields = ['teamInvestment', 'workType', 'workArrangement']
    const fieldsValid = fields.every(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      return !error
    })
    
    // 验证团队成员 - 至少要有一个有效的团队成员
    const teamValid = formData.teamInfo.length > 0 && 
      !formData.teamInfo.every(member => !member.name && !member.role) &&
      !formData.teamInfo.some(member => (member.name && !member.role) || (!member.name && member.role))
    
    // 验证招募需求 - 如果填写了招募信息，则必须完整
    const filledRecruitments = formData.recruitmentInfo.filter(req => req.position || req.cooperation)
    const recruitmentValid = filledRecruitments.length === 0 || 
      !filledRecruitments.some(req => !req.position || !req.cooperation)
    
    return fieldsValid && teamValid && recruitmentValid
  }
  
  if (currentStep.value === 3) {
    // 验证第三步字段：项目分析
    const fields = ['recentProgress', 'userAnalysis', 'businessModel', 'businessDescription', 'durationMonths']
    return fields.every(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      return !error
    })
  }
  
  if (currentStep.value === 4) {
    // 验证第四步字段：项目故事
    const fields = ['projectOrigin', 'competitiveAdvantage']
    return fields.every(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      return !error
    })
  }
  
  return false
}

// 验证当前步骤并更新错误状态
const validateCurrentStep = () => {
  let hasErrors = false
  
  // 清空错误
  Object.keys(errors).forEach(key => {
    errors[key as keyof typeof errors] = ''
  })
  
  if (currentStep.value === 1) {
    // 验证第一步字段：只验证基本信息
    const fields = ['title', 'summary', 'category', 'description']
    fields.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      if (error) {
        errors[field as keyof typeof errors] = error
        hasErrors = true
      }
    })
    // 第一步不验证团队成员信息
  }
  
  if (currentStep.value === 2) {
    // 验证第二步字段：团队和招募信息
    const fields = ['teamInvestment', 'workType', 'workArrangement']
    fields.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      if (error) {
        errors[field as keyof typeof errors] = error
        hasErrors = true
      }
    })
    
    // 验证团队成员 - 至少要有一个有效的团队成员
    if (formData.teamInfo.length === 0 || formData.teamInfo.every(member => !member.name && !member.role)) {
      errors.teamInfo = '至少需要一个团队成员'
      hasErrors = true
    } else if (formData.teamInfo.some(member => (member.name && !member.role) || (!member.name && member.role))) {
      errors.teamInfo = '团队成员姓名和角色必须同时填写'
      hasErrors = true
    }
    
    // 验证招募需求 - 如果填写了招募信息，则必须完整
    const filledRecruitments = formData.recruitmentInfo.filter(req => req.position || req.cooperation)
    if (filledRecruitments.length > 0) {
      const hasIncompleteRecruitment = filledRecruitments.some(req => !req.position || !req.cooperation)
      if (hasIncompleteRecruitment) {
        errors.recruitmentInfo = '招募岗位名称和合作方式不能为空'
        hasErrors = true
      }
    }
  }
  
  if (currentStep.value === 3) {
    // 验证第三步字段：项目分析
    const fields = ['recentProgress', 'userAnalysis', 'businessModel', 'businessDescription', 'durationMonths']
    fields.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      if (error) {
        errors[field as keyof typeof errors] = error
        hasErrors = true
      }
    })
  }
  
  if (currentStep.value === 4) {
    // 验证第四步字段：项目故事
    const fields = ['projectOrigin', 'competitiveAdvantage']
    fields.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData])
      if (error) {
        errors[field as keyof typeof errors] = error
        hasErrors = true
      }
    })
  }
  
  return !hasErrors
}

// 验证当前步骤是否可以继续
const canProceed = computed(() => {
  return checkCurrentStepValidity()
})

// 下一步
const nextStep = () => {
  if (!validateCurrentStep()) {
    // 显示验证错误提示
    const firstErrorElement = document.querySelector('.form-input.error, .form-select.error')
    if (firstErrorElement) {
      firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
      ;(firstErrorElement as HTMLElement).focus()
    }
    return
  }
  
  if (currentStep.value < 4) {
    // 添加切换动画
    const currentContent = document.querySelector('.step-content.active')
    if (currentContent) {
      currentContent.style.transform = 'translateX(-20px)'
      currentContent.style.opacity = '0'
    }
    
    setTimeout(() => {
      currentStep.value++
      // 平滑滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 150)
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    // 添加切换动画
    const currentContent = document.querySelector('.step-content.active')
    if (currentContent) {
      currentContent.style.transform = 'translateX(20px)'
      currentContent.style.opacity = '0'
    }
    
    setTimeout(() => {
      currentStep.value--
      // 平滑滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }, 150)
  }
}

// 添加团队成员
const addTeamMember = () => {
  formData.teamInfo.push({
    name: '',
    role: '',
    background: '',
    introduction: ''
  })
}

// 移除团队成员
const removeTeamMember = (index: number) => {
  formData.teamInfo.splice(index, 1)
}

// 添加招募需求
const addRequirement = () => {
  formData.recruitmentInfo.push({
    position: '',
    cooperation: '',
    skills: '',
    salary: '',
    equity: ''
  })
}

// 移除招募需求
const removeRequirement = (index: number) => {
  formData.recruitmentInfo.splice(index, 1)
}

// 图片上传相关方法
const triggerImageUpload = () => {
  imageInput.value?.click()
}

const handleImageUpload = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  
  if (!files || files.length === 0) return
  
  // 检查图片数量限制
  if (formData.images.length + files.length > 5) {
    alert('最多只能上传5张图片')
    return
  }
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    
    // 检查文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      alert(`图片 "${file.name}" 超过5MB，请选择较小的文件`)
      continue
    }
    
    // 检查文件类型
    if (!file.type.match(/^image\/(jpeg|png|webp)$/)) {
      alert(`图片 "${file.name}" 格式不支持，请选择JPG、PNG或WebP格式`)
      continue
    }
    
    // 创建预览URL
    const previewUrl = URL.createObjectURL(file)
    
    // 添加到表单数据
    formData.images.push({
      name: file.name,
      url: previewUrl,
      file: file
    })
  }
  
  // 清空文件输入，允许重复选择同一文件
  target.value = ''
}

const removeImage = (index: number) => {
  const image = formData.images[index]
  
  // 释放预览URL内存
  if (image.url.startsWith('blob:')) {
    URL.revokeObjectURL(image.url)
  }
  
  // 从数组中移除
  formData.images.splice(index, 1)
}

const uploadImagesToServer = async () => {
  if (formData.images.length === 0) return []
  
  uploadProgress.show = true
  uploadProgress.percent = 0
  
  try {
    const uploadedImages = []
    
    for (let i = 0; i < formData.images.length; i++) {
      const image = formData.images[i]
      
      // 如果已经上传过（有id），跳过
      if (image.id) {
        uploadedImages.push(image)
        continue
      }
      
      // 如果没有file对象，跳过
      if (!image.file) {
        continue
      }
      
      const uploadFormData = new FormData()
      uploadFormData.append('image', image.file)
      
      try {
        const response = await fetch('/api/upload/image', {
          method: 'POST',
          body: uploadFormData,
          headers: {
            'Authorization': `Bearer ${userStore.token}`
          }
        })
        
        if (!response.ok) {
          throw new Error(`上传失败: ${response.statusText}`)
        }
        
        const result = await response.json()
        
        if (result.success) {
          uploadedImages.push({
            id: result.data.id,
            name: image.name,
            url: result.data.url
          })
        } else {
          throw new Error(result.message || '上传失败')
        }
      } catch (error) {
        console.error(`上传图片 "${image.name}" 失败:`, error)
        throw error
      }
      
      // 更新进度
      uploadProgress.percent = Math.round(((i + 1) / formData.images.length) * 100)
    }
    
    uploadProgress.show = false
    return uploadedImages
    
  } catch (error) {
    uploadProgress.show = false
    throw error
  }
}

// 提交项目
const submitProject = async () => {
  if (!canProceed.value) return

  isSubmitting.value = true
  
  try {
    // 验证所有必填字段
    const requiredFields = [
      { field: 'title', name: '项目标题' },
      { field: 'summary', name: '项目简介' },
      { field: 'category', name: '项目分类' },
      { field: 'description', name: '项目描述' },
      { field: 'teamInvestment', name: '团队投入情况' },
      { field: 'workType', name: '工作方式' },
      { field: 'workArrangement', name: '工作安排' },
      { field: 'recentProgress', name: '最近进展' },
      { field: 'userAnalysis', name: '用户分析' },
      { field: 'projectOrigin', name: '项目起源' },
      { field: 'competitiveAdvantage', name: '竞争优势' },
      { field: 'businessModel', name: '商业模式' },
      { field: 'businessDescription', name: '商业模式描述' },
      { field: 'durationMonths', name: '项目周期' }
    ]
    
    const missingFields = requiredFields.filter(({ field }) => {
      const value = formData[field as keyof typeof formData]
      return !value || (typeof value === 'string' && value.trim() === '')
    })
    
    if (missingFields.length > 0) {
      throw new Error(`请填写以下必填字段：${missingFields.map(f => f.name).join('、')}`)
    }
    
    // 先上传图片
    let uploadedImages = []
    if (formData.images.length > 0) {
      uploadedImages = await uploadImagesToServer()
    }
    
    // 构建提交数据，转换为后端期望的格式
    const projectData = {
      title: formData.title,
      summary: formData.summary, // 后端期望 summary
      description: formData.description,
      category: formData.category,
      durationMonths: parseInt(formData.durationMonths) || 12, // 后端期望数字
      demoUrl: formData.demoUrl || '',
      
      // 团队信息 - 后端期望JSON字符串
      teamInfo: JSON.stringify(formData.teamInfo.filter(member => member.name && member.role)),
      teamInvestment: formData.teamInvestment, // 后端期望 teamInvestment
      
      // 招募需求 - 后端期望JSON字符串
      recruitmentInfo: JSON.stringify(formData.recruitmentInfo
        .filter(req => req.position && req.cooperation)
        .map(req => ({
          role: req.position,
          skillName: req.skills ? req.skills.split(',').map(s => s.trim()) : [],
          cooperation: req.cooperation,
          salaryAmount: req.salary || '',
          equityAmount: req.equity ? parseFloat(req.equity) : null
        }))),
      
      // 工作安排
      workType: formData.workType, // 后端期望 workType
      workArrangement: formData.workArrangement,
      workLocation: formData.workLocation || '', // 后端期望 workLocation
      
      // 项目分析
      recentProgress: formData.recentProgress,
      userAnalysis: formData.userAnalysis,
      projectOrigin: formData.projectOrigin,
      competitiveAdvantage: formData.competitiveAdvantage, // 后端期望 competitiveAdvantage
      businessModel: formData.businessModel,
      businessDescription: formData.businessDescription // 后端期望 businessDescription
    }
    
    console.log('提交数据:', projectData)
    
    // 调用API提交项目
    const { createProject } = await import('@/api/project')
    const result = await createProject(projectData)
    
    console.log('提交结果:', result)
    
    alert('项目提交成功！我们会在2-3个工作日内审核，审核通过后将自动发布。')
    
    // 跳转到项目列表页
    router.push('/projects')
    
  } catch (error) {
    console.error('提交项目失败:', error)
    alert('提交失败，请重试：' + (error as any).message)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
/* 100%还原原型图样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.project-publish-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.project-publish-container {
  width: 90%;
  max-width: 900px;
  margin: 0 auto;
}

/* 向导容器 */
.wizard-container {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 进度指示器 */
.progress-header {
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  padding: 30px;
  border-bottom: 1px solid #e2e8f0;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 25px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e2e8f0;
  z-index: 1;
}

.step.active:not(:last-child)::after,
.step.completed:not(:last-child)::after {
  background: #667eea;
}

.step-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #e2e8f0;
  border: 3px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: #64748b;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step.active .step-circle {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.step.completed .step-circle {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.step-label {
  margin-top: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  text-align: center;
}

.step.active .step-label {
  color: #667eea;
  font-weight: 600;
}

.progress-info {
  text-align: center;
}

.progress-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.progress-subtitle {
  color: #64748b;
  font-size: 16px;
}

/* 表单容器 */
.form-container {
  padding: 40px;
  min-height: 500px;
}

.step-content {
  display: none;
}

.step-content.active {
  display: block;
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.step-header {
  text-align: center;
  margin-bottom: 30px;
}

.step-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.step-description {
  color: #64748b;
  font-size: 16px;
}

/* 表单样式 */
.form-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.required {
  color: #ef4444;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-hint {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

/* 动态表单项 */
.dynamic-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
}

.dynamic-item {
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.dynamic-item:last-child {
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.item-title {
  font-weight: 600;
  color: #374151;
}

.remove-btn {
  background: #fef2f2;
  color: #ef4444;
  border: 1px solid #fecaca;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #fecaca;
}

.add-btn {
  background: #f0f9ff;
  color: #0ea5e9;
  border: 2px dashed #0ea5e9;
  padding: 16px;
  border-radius: 8px;
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background: #e0f2fe;
}

/* 表单网格 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

/* 按钮样式 */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e5e7eb;
  gap: 16px;
}

.actions-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.validation-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.hint-icon {
  font-size: 16px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a67d8;
}

.btn-secondary {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #e2e8f0;
}

/* 错误提示样式 */
.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

.form-input.error {
  border-color: #ef4444;
}

.form-select.error {
  border-color: #ef4444;
}

/* 图片上传样式 */
.image-upload-container {
  border: 2px dashed #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  background: #f9fafb;
  transition: all 0.3s ease;
}

.image-upload-container:hover {
  border-color: #667eea;
  background: #f8fafc;
}

.image-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.image-preview-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.image-preview-item:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.preview-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview-item:hover .image-overlay {
  opacity: 1;
}

.remove-image-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 8px;
  padding-top: 16px;
}

.image-name {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.image-upload-btn {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #6b7280;
}

.image-upload-btn:hover {
  border-color: #667eea;
  background: #f0f9ff;
  color: #667eea;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-text {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.upload-hint {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 4px;
}

.upload-progress {
  margin-top: 16px;
  padding: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #374151;
  text-align: center;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-publish-container {
    width: 95%;
    padding: 20px 0;
  }
  
  .wizard-container {
    margin: 20px 0;
  }
  
  .progress-steps {
    flex-direction: column;
    gap: 20px;
  }
  
  .step::after {
    display: none;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .form-container {
    padding: 20px;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 16px;
  }
  
  .btn {
    width: 100%;
  }
  
  .image-preview-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }
  
  .preview-image {
    height: 80px;
  }
  
  .image-upload-btn {
    height: 80px;
  }
  
  .upload-icon {
    font-size: 18px;
    margin-bottom: 4px;
  }
  
  .upload-text {
    font-size: 12px;
  }
}
</style> 
