<template>
  <div class="not-found-page">
    <Navbar />
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="not-found-visual">
          <div class="error-code">404</div>
          <div class="error-illustration">
            <div class="floating-shapes">
              <div class="shape shape-1"></div>
              <div class="shape shape-2"></div>
              <div class="shape shape-3"></div>
            </div>
          </div>
        </div>
        <div class="not-found-text">
          <h1 class="error-title">页面不存在</h1>
          <p class="error-description">
            抱歉，您访问的页面可能已被删除、重命名或暂时不可用。
          </p>
          <div class="error-actions">
            <router-link to="/" class="btn btn-primary">
              返回首页
            </router-link>
            <router-link to="/projects" class="btn btn-secondary">
              浏览项目
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import Navbar from '@/components/layout/Navbar.vue'
</script>

<style scoped>
.not-found-page {
  @apply min-h-screen bg-gray-50;
}

.not-found-container {
  @apply pt-24 pb-16 px-6 flex items-center justify-center min-h-screen;
}

.not-found-content {
  @apply max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center;
}

.not-found-visual {
  @apply relative text-center;
}

.error-code {
  @apply text-8xl lg:text-9xl font-bold text-gray-200 leading-none;
}

.error-illustration {
  @apply relative mt-8;
}

.floating-shapes {
  @apply relative h-32;
}

.shape {
  @apply absolute w-6 h-6 rounded-full;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  @apply bg-primary top-0 left-1/4;
  animation-delay: 0s;
}

.shape-2 {
  @apply bg-secondary top-1/2 right-1/4;
  animation-delay: 2s;
}

.shape-3 {
  @apply bg-warning bottom-0 left-1/2;
  animation-delay: 4s;
}

.not-found-text {
  @apply text-center lg:text-left;
}

.error-title {
  @apply text-4xl font-bold text-gray-900 mb-4;
}

.error-description {
  @apply text-xl text-gray-600 mb-8 leading-relaxed;
}

.error-actions {
  @apply flex gap-4 justify-center lg:justify-start;
}

.btn {
  @apply px-8 py-4 rounded-xl font-medium transition-all duration-300 no-underline inline-flex items-center justify-center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  @apply text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}
</style> 