<template>
  <div class="help-center-page">
    <div class="help-layout">
      <!-- 左侧目录导航 -->
      <aside class="help-sidebar">
        <ul>
          <li><a href="#intro">新手入门</a></li>
          <li><a href="#account">账号与安全</a></li>
          <li><a href="#project">项目广场与发布</a></li>
          <li><a href="#team">团队与协作</a></li>
          <li><a href="#dao">DAO治理</a></li>
          <li><a href="#messages">消息中心</a></li>
          <li><a href="#certification">认证与支付</a></li>
          <li><a href="#faq">常见问题</a></li>
          <li><a href="#contact">联系我们</a></li>
        </ul>
      </aside>
      <!-- 右侧内容区 -->
      <main class="help-content">
        <section id="intro">
          <h2>新手入门</h2>
          <p>欢迎来到小概率！本平台为开发者和项目方提供从创意到成功的全流程服务。你可以浏览项目、发布想法、参与协作、学习技能、投资项目，还能参与社区治理。</p>
          <ul>
            <li>注册/登录，完善个人信息</li>
            <li>浏览项目广场，发现感兴趣的项目</li>
            <li>发布你的项目或想法，吸引团队成员</li>
            <li>参与DAO治理，推动平台发展</li>
          </ul>
        </section>
        <section id="account">
          <h2>账号与安全</h2>
          <ul>
            <li>支持邮箱注册、第三方登录</li>
            <li>忘记密码可通过邮箱找回</li>
            <li>实名认证提升账号安全与信任度</li>
            <li>如遇异常登录、账号被盗等问题，请及时联系客服</li>
          </ul>
        </section>
        <section id="project">
          <h2>项目广场与发布</h2>
          <ul>
            <li>浏览项目广场，支持多条件筛选和搜索</li>
            <li>项目详情页展示完整项目信息、团队、进展等</li>
            <li>发布项目采用四步引导流程，支持团队与需求、项目分析、故事等信息填写</li>
            <li>所有新发布项目需通过平台审核，审核标准详见FAQ</li>
            <li>项目审核进度和结果可在个人中心查看</li>
          </ul>
        </section>
        <section id="team">
          <h2>团队与协作</h2>
          <ul>
            <li>支持团队成员管理、角色分配</li>
            <li>可邀请成员加入项目，或申请加入他人项目</li>
            <li>支持多种协作方式（全职/兼职/远程/现场）</li>
            <li>团队协作工具持续完善中</li>
          </ul>
        </section>
        <section id="dao">
          <h2>DAO治理</h2>
          <ul>
            <li>DAO治理中心支持提案、投票、评论等功能</li>
            <li>成为DAO成员可参与平台重大决策</li>
            <li>提案分为功能改进、社区治理、商业模式、技术升级等类别</li>
            <li>提案流程：提交→审核→投票→执行/归档</li>
          </ul>
        </section>
        <section id="messages">
          <h2>消息中心</h2>
          <ul>
            <li>所有系统通知、项目动态、审核结果、DAO投票等消息统一在消息中心查看</li>
            <li>有新消息时首页导航栏会有红点提示</li>
            <li>支持消息已读/未读管理</li>
          </ul>
        </section>
        <section id="certification">
          <h2>认证与支付</h2>
          <ul>
            <li>支持个人与团队认证，提升平台信任度</li>
            <li>认证状态可在个人中心-认证中心查看</li>
            <li>平台支持多种支付方式，支付安全有保障</li>
            <li>如遇支付问题请及时联系客服</li>
          </ul>
        </section>
        <section id="faq">
          <h2>常见问题FAQ</h2>
          <details>
            <summary>如何发布项目？</summary>
            <div>点击首页“发布想法”或导航栏“发布项目”，按引导填写信息并提交，等待审核通过即可。</div>
          </details>
          <details>
            <summary>项目审核需要多久？</summary>
            <div>一般1-2个工作日内完成审核，特殊情况会有短信/邮件通知。</div>
          </details>
          <details>
            <summary>如何成为DAO成员？</summary>
            <div>在个人中心-认证中心申请DAO认证，审核通过后即可参与投票和提案。</div>
          </details>
          <details>
            <summary>如何联系客服？</summary>
            <div>可通过页面底部“联系我们”或右下角在线客服入口联系客服。</div>
          </details>
        </section>
        <section id="contact">
          <h2>联系我们</h2>
          <ul>
            <li>在线客服：页面右下角悬浮按钮</li>
            <li>邮箱：<EMAIL></li>
            <li>微信/社群：见首页底部二维码</li>
            <li>欢迎反馈建议，帮助我们做得更好！</li>
          </ul>
        </section>
      </main>
    </div>
    <!-- 悬浮客服/反馈按钮 -->
    <div class="help-float-btn">
      <button class="float-btn">在线客服</button>
      <button class="float-btn">反馈建议</button>
    </div>
  </div>
</template>

<script setup lang="ts">
// 目前无复杂逻辑，后续可扩展
</script>

<style scoped>
.help-center-page {
  min-height: 100vh;
  background: #f7f9fc;
  padding: 0;
}
.help-layout {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 0 80px 0;
  gap: 40px;
}
.help-sidebar {
  width: 220px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102,126,234,0.08);
  padding: 32px 0;
  position: sticky;
  top: 40px;
  align-self: flex-start;
  height: fit-content;
}
.help-sidebar ul {
  list-style: none;
  padding: 0 24px;
  margin: 0;
}
.help-sidebar li {
  margin-bottom: 18px;
}
.help-sidebar a {
  color: #667eea;
  font-weight: 600;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.2s;
}
.help-sidebar a:hover {
  color: #764ba2;
}
.help-content {
  flex: 1;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(102,126,234,0.08);
  padding: 48px 48px 32px 48px;
}
.help-content section {
  margin-bottom: 48px;
}
.help-content h2 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 18px;
}
.help-content ul {
  margin: 0 0 12px 18px;
  padding: 0;
  color: #374151;
  font-size: 15px;
  line-height: 1.8;
}
.help-content details {
  margin-bottom: 12px;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 15px;
}
.help-content summary {
  font-weight: 600;
  color: #764ba2;
  cursor: pointer;
  outline: none;
}
.help-float-btn {
  position: fixed;
  right: 32px;
  bottom: 32px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 100;
}
.float-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24px;
  padding: 12px 28px;
  font-size: 15px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102,126,234,0.15);
  cursor: pointer;
  transition: background 0.2s, transform 0.2s;
}
.float-btn:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-2px);
}
@media (max-width: 900px) {
  .help-layout {
    flex-direction: column;
    gap: 24px;
    padding: 24px 0 60px 0;
  }
  .help-sidebar {
    width: 100%;
    position: static;
    border-radius: 12px;
    margin-bottom: 0;
    padding: 20px 0;
  }
  .help-content {
    padding: 24px 16px 16px 16px;
  }
}
@media (max-width: 600px) {
  .help-center-page {
    padding: 0;
  }
  .help-layout {
    padding: 0 0 40px 0;
  }
  .help-sidebar {
    padding: 12px 0;
  }
  .help-content {
    padding: 12px 4px 8px 4px;
  }
  .help-float-btn {
    right: 12px;
    bottom: 12px;
  }
  .float-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}
</style> 