<template>
  <div class="investment-page">
    <Navbar />

    <!-- Hero区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <h1 class="hero-title">投资生态</h1>
        <p class="hero-subtitle">
          连接创新与资本，汇聚顶尖项目、专业投资人和战略资源，让每个投资都拥有突破小概率的机会
        </p>
        
        <!-- <div class="hero-stats">
          <div class="hero-stat">
            <span class="hero-stat-number">{{ stats.totalInvestment }}</span>
            <span class="hero-stat-label">累计投资金额</span>
          </div>
          <div class="hero-stat">
            <span class="hero-stat-number">{{ stats.projectCount }}</span>
            <span class="hero-stat-label">投资项目数</span>
          </div>
          <div class="hero-stat">
            <span class="hero-stat-number">{{ stats.investorCount }}</span>
            <span class="hero-stat-label">注册投资人</span>
          </div>
          <div class="hero-stat">
            <span class="hero-stat-number">{{ stats.exitCount }}</span>
            <span class="hero-stat-label">成功退出项目</span>
          </div>
        </div> -->
      </div>
    </section>

    <!-- 投资生态双列布局 -->
    <section class="dual-section">
      <div class="dual-container">
        <h2 class="section-title">投资生态双引擎</h2>
        <p class="section-subtitle">为投资人和创业者搭建最有价值的连接桥梁，实现互利共赢</p>
        
        <div class="dual-grid">
          <div class="section-card investor-card">
            <h3 class="card-title">💼 投资人生态</h3>
            <p class="card-subtitle">加入小概率联盟，获得独家项目池和优先投资权，共同打造下一个独角兽</p>
            <ul class="benefit-list">
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">独家项目池：优先接触平台精选的优质高成长项目</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">专业尽调支持：技术、商业、法务全方位深度尽调服务</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">投后赋能：专业团队协助价值提升和战略资源对接</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">多元退出：IPO、并购、股权转让等多元化退出机制</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">顶级人脉：与行业领袖、成功投资人深度交流合作</div>
              </li>
            </ul>
            <button class="card-cta" @click="applyInvestor">申请成为投资人</button>
          </div>
          
          <div class="section-card entrepreneur-card">
            <h3 class="card-title">🚀 创业者生态</h3>
            <p class="card-subtitle">从想法到成功，小概率为创业者提供全生命周期的成长加速</p>
            <ul class="benefit-list">
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">团队构建：智能匹配技术互补的核心创始团队</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">技术支持：资深专家提供架构设计和开发指导</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">资本加速：种子期直投+多轮融资对接+战略投资引入</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">商业赋能：市场推广、渠道合作、大客户对接</div>
              </li>
              <li class="benefit-item">
                <div class="benefit-icon">✓</div>
                <div class="benefit-text">成长加速：导师辅导、资源对接快速成长</div>
              </li>
            </ul>
            <button class="card-cta" @click="submitBusinessPlan">提交创业计划</button>
          </div>
        </div>
      </div>
    </section>

    <!-- 平台成长足迹 -->
    <section class="platform-timeline">
      <div class="timeline-container">
        <h2 class="timeline-title">平台成长足迹</h2>
        <p class="timeline-subtitle">见证我们如何一步步构建项目成功生态</p>

        <div class="timeline-list">
          <div class="timeline-item">
            <div class="timeline-icon">🚀</div>
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="timeline-project">平台正式上线</div>
                <div class="timeline-date">2023-10-01</div>
              </div>
              <div class="timeline-desc">小概率平台正式上线，致力于将项目成功从偶然变为必然</div>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-icon">👥</div>
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="timeline-project">首批项目入驻</div>
                <div class="timeline-date">2023-11-15</div>
              </div>
              <div class="timeline-desc">28个高潜力项目入驻平台，涵盖AI、绿色科技、教育创新等领域</div>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-icon">💡</div>
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="timeline-project">成长计划发布</div>
                <div class="timeline-date">2023-12-10</div>
              </div>
              <div class="timeline-desc">宣布平台利润的30%将用于支持项目成长，包括奖金池和资源注入</div>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-icon">🤝</div>
            <div class="timeline-content">
              <div class="timeline-header">
                <div class="timeline-project">支持者计划启动</div>
                <div class="timeline-date">2024-01-05</div>
              </div>
              <div class="timeline-desc">正式启动支持者计划，为项目提供法律框架内的多种支持方式</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 未来成长计划 -->
    <section class="growth-plan-section">
      <div class="growth-plan-container">
        <div class="growth-plan-header">
          <h2 class="growth-plan-title">未来成长计划</h2>
          <p class="growth-plan-subtitle">我们计划通过以下方式支持创新项目成长</p>
        </div>

        <div class="growth-plan-info">
          <div class="plan-highlight">
            <div class="plan-amount">30%</div>
            <p class="plan-desc">平台利润将用于支持项目成长</p>
          </div>

          <div class="growth-plan-grid">
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">🏆</div>
              <div class="plan-feature-title">成长奖金池</div>
              <div class="plan-feature-desc">设立季度奖金池，奖励取得重大进展的项目团队</div>
            </div>
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">💼</div>
              <div class="plan-feature-title">平台直投计划</div>
              <div class="plan-feature-desc">平台直接投资优质项目，提供启动资金和资源支持</div>
            </div>
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">🤝</div>
              <div class="plan-feature-title">支持者匹配</div>
              <div class="plan-feature-desc">在法律允许范围内，连接项目与个人支持者</div>
            </div>
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">🚀</div>
              <div class="plan-feature-title">孵化加速</div>
              <div class="plan-feature-desc">提供6-12个月的深度孵化支持，加速项目成长</div>
            </div>
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">🌐</div>
              <div class="plan-feature-title">资源网络</div>
              <div class="plan-feature-desc">对接行业资源、专家导师和大企业合作机会</div>
            </div>
            <div class="growth-plan-feature">
              <div class="plan-feature-icon">📈</div>
              <div class="plan-feature-title">价值提升</div>
              <div class="plan-feature-desc">专业团队协助项目提升技术价值和商业价值</div>
            </div>
          </div>
        </div>

        <div class="growth-plan-cta">
          <a href="#" class="plan-btn" @click="learnGrowthPlan">了解成长计划</a>
          <a href="#" class="plan-btn plan-btn-secondary" @click="viewProjectProgress">查看项目进展</a>
        </div>
      </div>
    </section>

    <!-- 法律合规说明 -->
    <section class="legal-section">
      <div class="legal-container">
        <h2 class="legal-title">法律合规说明</h2>
        <div class="legal-content">
          <p>小概率平台严格遵守国家相关法律法规，所有投资行为均在法律框架内进行。</p>
          <p>平台提供两种合规支持模式：</p>
        </div>

        <div class="legal-highlight">
          <p><strong>模式一：平台直接投资</strong> - 小概率平台使用自有资金直接投资优质项目，提供启动资金和资源支持。</p>
          <p><strong>模式二：支持者匹配</strong> - 在法律允许范围内，平台为合格支持者匹配项目机会，支持者以个人身份与项目方签订投资协议。</p>
        </div>

        <p class="legal-content">具体实施方案将根据项目情况和法律法规要求进行调整，请以平台最新公告为准。</p>
      </div>
    </section>

    <!-- 投资机会CTA -->
    <section class="opportunity-cta">
      <div class="opportunity-container">
        <h2 class="opportunity-title">抓住下一个投资机会</h2>
        <p class="opportunity-subtitle">优质项目稀缺，机会转瞬即逝。加入小概率投资生态，获得优先投资权</p>
        <div class="opportunity-buttons">
          <a href="#" class="opportunity-btn btn-purple" @click="applyInvestorCertification">申请投资人认证</a>
          <a href="#" class="opportunity-btn btn-outline" @click="learnInvestmentProcess">了解投资流程</a>
        </div>
      </div>
    </section>

    <!-- 申请成为投资人弹窗 -->
    <div v-if="showInvestorModal" class="modal-overlay" @click="closeInvestorModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>申请成为投资人</h3>
          <button class="modal-close" @click="closeInvestorModal">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitInvestorApplication">
            <div class="form-group">
              <label class="form-label">姓名 <span class="required">*</span></label>
              <input
                v-model="investorForm.name"
                type="text"
                class="form-input"
                placeholder="请输入您的真实姓名"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">联系方式 <span class="required">*</span></label>
              <input
                v-model="investorForm.contact"
                type="text"
                class="form-input"
                placeholder="请输入手机号或微信号"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">投资经验 <span class="required">*</span></label>
              <select v-model="investorForm.experience" class="form-select" required>
                <option value="">请选择投资经验</option>
                <option value="beginner">新手投资者（0-2年）</option>
                <option value="intermediate">有经验投资者（2-5年）</option>
                <option value="expert">资深投资者（5年以上）</option>
                <option value="professional">专业投资机构</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">投资预算范围</label>
              <select v-model="investorForm.budget" class="form-select">
                <option value="">请选择投资预算</option>
                <option value="10-50">10万-50万</option>
                <option value="50-100">50万-100万</option>
                <option value="100-500">100万-500万</option>
                <option value="500+">500万以上</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">感兴趣的行业</label>
              <div class="checkbox-group">
                <label class="checkbox-item" v-for="industry in industries" :key="industry.value">
                  <input
                    type="checkbox"
                    :value="industry.value"
                    v-model="investorForm.industries"
                  />
                  <span>{{ industry.label }}</span>
                </label>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">申请说明</label>
              <textarea
                v-model="investorForm.description"
                class="form-textarea"
                placeholder="请简单介绍您的投资背景和期望..."
                rows="4"
              ></textarea>
            </div>

            <div class="modal-actions">
              <button type="button" class="modal-btn modal-btn-secondary" @click="closeInvestorModal">取消</button>
              <button type="submit" class="modal-btn modal-btn-primary" :disabled="isSubmittingInvestor">
                {{ isSubmittingInvestor ? '提交中...' : '提交申请' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 提交创业计划弹窗 -->
    <div v-if="showBusinessModal" class="modal-overlay" @click="closeBusinessModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>提交创业计划</h3>
          <button class="modal-close" @click="closeBusinessModal">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitBusinessPlanForm">
            <div class="form-group">
              <label class="form-label">项目名称 <span class="required">*</span></label>
              <input
                v-model="businessForm.projectName"
                type="text"
                class="form-input"
                placeholder="请输入项目名称"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">项目简介 <span class="required">*</span></label>
              <textarea
                v-model="businessForm.summary"
                class="form-textarea"
                placeholder="请简要描述您的项目..."
                rows="3"
                required
              ></textarea>
            </div>

            <div class="form-group">
              <label class="form-label">所属行业 <span class="required">*</span></label>
              <select v-model="businessForm.industry" class="form-select" required>
                <option value="">请选择行业</option>
                <option value="mobile">移动应用</option>
                <option value="web">Web开发</option>
                <option value="ai">AI/机器学习</option>
                <option value="blockchain">区块链</option>
                <option value="fintech">金融科技</option>
                <option value="education">教育培训</option>
                <option value="health">健康生活</option>
                <option value="ecommerce">电商平台</option>
                <option value="other">其他</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">融资需求</label>
              <select v-model="businessForm.funding" class="form-select">
                <option value="">请选择融资需求</option>
                <option value="50-100">50万-100万</option>
                <option value="100-300">100万-300万</option>
                <option value="300-500">300万-500万</option>
                <option value="500-1000">500万-1000万</option>
                <option value="1000+">1000万以上</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">项目阶段 <span class="required">*</span></label>
              <select v-model="businessForm.stage" class="form-select" required>
                <option value="">请选择项目阶段</option>
                <option value="idea">创意阶段</option>
                <option value="prototype">原型开发</option>
                <option value="mvp">MVP阶段</option>
                <option value="growth">成长阶段</option>
                <option value="mature">成熟阶段</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">联系方式 <span class="required">*</span></label>
              <input
                v-model="businessForm.contact"
                type="text"
                class="form-input"
                placeholder="请输入手机号或微信号"
                required
              />
            </div>

            <div class="form-group">
              <label class="form-label">详细计划</label>
              <textarea
                v-model="businessForm.details"
                class="form-textarea"
                placeholder="请详细描述您的商业计划、市场分析、竞争优势等..."
                rows="6"
              ></textarea>
            </div>

            <div class="modal-actions">
              <button type="button" class="modal-btn modal-btn-secondary" @click="closeBusinessModal">取消</button>
              <button type="submit" class="modal-btn modal-btn-primary" :disabled="isSubmittingBusiness">
                {{ isSubmittingBusiness ? '提交中...' : '提交计划' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Navbar from '@/components/layout/Navbar.vue'

// 统计数据
const stats = ref({
  totalInvestment: '5.8亿',
  projectCount: '320+',
  investorCount: '1200+',
  exitCount: '45'
})

// 弹窗状态
const showInvestorModal = ref(false)
const showBusinessModal = ref(false)
const isSubmittingInvestor = ref(false)
const isSubmittingBusiness = ref(false)

// 行业选项
const industries = ref([
  { value: 'mobile', label: '移动应用' },
  { value: 'web', label: 'Web开发' },
  { value: 'ai', label: 'AI/机器学习' },
  { value: 'blockchain', label: '区块链' },
  { value: 'fintech', label: '金融科技' },
  { value: 'education', label: '教育培训' },
  { value: 'health', label: '健康生活' },
  { value: 'ecommerce', label: '电商平台' },
  { value: 'other', label: '其他' }
])

// 投资人申请表单
const investorForm = ref({
  name: '',
  contact: '',
  experience: '',
  budget: '',
  industries: [],
  description: ''
})

// 创业计划表单
const businessForm = ref({
  projectName: '',
  summary: '',
  industry: '',
  funding: '',
  stage: '',
  contact: '',
  details: ''
})


// 方法
const applyInvestor = () => {
  showInvestorModal.value = true
}

const submitBusinessPlan = () => {
  showBusinessModal.value = true
}

const closeInvestorModal = () => {
  showInvestorModal.value = false
  // 重置表单
  investorForm.value = {
    name: '',
    contact: '',
    experience: '',
    budget: '',
    industries: [],
    description: ''
  }
}

const closeBusinessModal = () => {
  showBusinessModal.value = false
  // 重置表单
  businessForm.value = {
    projectName: '',
    summary: '',
    industry: '',
    funding: '',
    stage: '',
    contact: '',
    details: ''
  }
}

const submitInvestorApplication = async () => {
  try {
    isSubmittingInvestor.value = true

    // TODO: 调用后端API提交投资人申请
    console.log('投资人申请数据:', investorForm.value)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    alert('投资人申请已提交！我们会在3个工作日内联系您。')
    closeInvestorModal()
  } catch (error) {
    console.error('提交投资人申请失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    isSubmittingInvestor.value = false
  }
}

const submitBusinessPlanForm = async () => {
  try {
    isSubmittingBusiness.value = true

    // TODO: 调用后端API提交创业计划
    console.log('创业计划数据:', businessForm.value)

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    alert('创业计划已提交！我们会在5个工作日内联系您。')
    closeBusinessModal()
  } catch (error) {
    console.error('提交创业计划失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    isSubmittingBusiness.value = false
  }
}

const learnGrowthPlan = () => {
  alert('成长计划详情页面开发中，请关注官方通知！')
}

const viewProjectProgress = () => {
  alert('项目进展展示功能开发中！')
}

const applyInvestorCertification = () => {
  alert('投资人认证申请功能开发中！')
}

const learnInvestmentProcess = () => {
  alert('投资流程详情页面开发中！')
}
</script>

<style scoped>
.investment-page {
  background: #fafbfc;
  color: #1e293b;
  line-height: 1.7;
}

/* Hero区域 */
.hero-section {
  padding: 120px 20px 60px;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 50%, #c084fc 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
}

.hero-badge {
  background: rgba(255,255,255,0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
  display: inline-block;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 40px;
  line-height: 1.6;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32px;
  margin-top: 60px;
}

.hero-stat {
  text-align: center;
}

.hero-stat-number {
  font-size: 32px;
  font-weight: 700;
  display: block;
  margin-bottom: 8px;
}

.hero-stat-label {
  font-size: 14px;
  opacity: 0.8;
}

/* 双列布局 */
.dual-section {
  padding: 80px 20px;
  background: white;
}

.dual-container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16px;
  color: #1e293b;
}

.section-subtitle {
  font-size: 18px;
  text-align: center;
  color: #6b7280;
  margin-bottom: 60px;
}

.dual-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.section-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 40px;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.section-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0,0,0,0.1);
}

.investor-card:hover {
  border-color: #8b5cf6;
  box-shadow: 0 12px 32px rgba(139, 92, 246, 0.15);
}

.entrepreneur-card:hover {
  border-color: #f59e0b;
  box-shadow: 0 12px 32px rgba(245, 158, 11, 0.15);
}

.card-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
}

.card-subtitle {
  color: #6b7280;
  margin-bottom: 32px;
  line-height: 1.6;
}

.benefit-list {
  list-style: none;
  padding: 0;
  margin-bottom: 40px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.benefit-icon {
  width: 20px;
  height: 20px;
  background: #10b981;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2px;
}

.benefit-text {
  color: #374151;
  line-height: 1.6;
}

.card-cta {
  width: 100%;
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.entrepreneur-card .card-cta {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.card-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);
}

.entrepreneur-card .card-cta:hover {
  box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

/* 平台成长足迹 */
.platform-timeline {
  padding: 100px 20px;
  background: #f9fafb;
}

.timeline-container {
  max-width: 1200px;
  margin: 0 auto;
}

.timeline-title {
  font-size: 42px;
  font-weight: 800;
  text-align: center;
  margin-bottom: 20px;
  color: #1a1a1a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.timeline-subtitle {
  font-size: 20px;
  text-align: center;
  color: #6b7280;
  margin-bottom: 60px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.timeline-list {
  max-width: 900px;
  margin: 0 auto;
}

.timeline-item {
  display: flex;
  gap: 24px;
  margin-bottom: 40px;
  position: relative;
  padding: 20px;
  border-radius: 16px;
  transition: all 0.3s;
}

.timeline-item:hover {
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
}

.timeline-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.timeline-content {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.timeline-project {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
}

.timeline-date {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.timeline-desc {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 16px;
  line-height: 1.6;
}

.timeline-amount {
  background: #e0e7ff;
  color: #4f46e5;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
}

/* 未来成长计划 */
.growth-plan-section {
  padding: 100px 20px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.growth-plan-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  text-align: center;
}

.growth-plan-header {
  margin-bottom: 70px;
}

.growth-plan-title {
  font-size: 46px;
  font-weight: 800;
  margin-bottom: 20px;
  line-height: 1.2;
}

.growth-plan-subtitle {
  font-size: 22px;
  opacity: 0.9;
  margin-bottom: 32px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.growth-plan-info {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 24px;
  padding: 50px 40px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 40px;
}

.plan-highlight {
  text-align: center;
  margin-bottom: 40px;
}

.plan-amount {
  font-size: 64px;
  font-weight: 800;
  margin-bottom: 12px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.plan-desc {
  font-size: 18px;
  opacity: 0.9;
  font-weight: 500;
}

.growth-plan-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.growth-plan-feature {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 30px 24px;
  text-align: center;
  transition: all 0.3s;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.growth-plan-feature:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-8px);
  border-color: rgba(255, 255, 255, 0.3);
}

.plan-feature-icon {
  font-size: 40px;
  margin-bottom: 20px;
}

.plan-feature-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 12px;
}

.plan-feature-desc {
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
}

.growth-plan-cta {
  text-align: center;
  margin-top: 30px;
}

.plan-btn {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #4f46e5;
  border: none;
  padding: 18px 36px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0 10px;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.plan-btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.plan-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(251, 191, 36, 0.4);
}

/* 法律合规说明 */
.legal-section {
  padding: 60px 20px;
  background: #f9fafb;
  text-align: center;
}

.legal-container {
  max-width: 900px;
  margin: 0 auto;
}

.legal-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.legal-content {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.8;
  margin-bottom: 30px;
}

.legal-highlight {
  background: #e0e7ff;
  padding: 20px;
  border-radius: 12px;
  margin-top: 30px;
  border-left: 4px solid #4f46e5;
}

/* 投资机会CTA */
.opportunity-cta {
  padding: 80px 20px;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  text-align: center;
}

.opportunity-container {
  max-width: 800px;
  margin: 0 auto;
}

.opportunity-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.opportunity-subtitle {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 40px;
}

.opportunity-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.opportunity-btn {
  padding: 14px 28px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.btn-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
}

.btn-outline {
  background: transparent;
  color: white;
  border: 2px solid rgba(255,255,255,0.3);
}

.opportunity-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 36px;
  }
  
  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .dual-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .growth-plan-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }
  
  .growth-plan-title {
    font-size: 36px;
  }
  
  .timeline-title {
    font-size: 36px;
  }
  
  .opportunity-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .plan-btn {
    margin: 8px 0;
  }
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.required {
  color: #ef4444;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
  resize: vertical;
  line-height: 1.5;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-top: 8px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.modal-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.modal-btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.modal-btn-secondary:hover {
  background: #e5e7eb;
}

.modal-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modal-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }

  .checkbox-group {
    grid-template-columns: 1fr;
  }

  .modal-actions {
    flex-direction: column;
  }

  .modal-btn {
    width: 100%;
  }
}
</style>