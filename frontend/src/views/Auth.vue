<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-shape"></div>
      <div class="bg-shape"></div>
      <div class="bg-shape"></div>
    </div>

    <!-- 主容器 -->
    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-overlay"></div>
        <div class="brand-content">
          <div class="brand-logo">小概率</div>
          <h2 class="brand-tagline">让成功成为必然</h2>

          <!-- 成功之路展示 -->
          <div class="journey-container">
            <div class="journey-line"></div>
            <div class="journey-progress" ref="journeyProgress"></div>
            <div class="journey-steps">
              <div class="journey-step" :class="{ active: currentStep >= 0 }">
                <i class="fas fa-lightbulb"></i>
                <div class="step-label">灵感</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 1 }">
                <i class="fas fa-users"></i>
                <div class="step-label">团队</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 2 }">
                <i class="fas fa-rocket"></i>
                <div class="step-label">成长</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 3 }">
                <i class="fas fa-trophy"></i>
                <div class="step-label">成功</div>
              </div>
            </div>
          </div>

          <p class="inspiration-quote">"在小概率，我们相信每个想法都有成为必然的机会。这里不仅是项目的起点，更是梦想实现的加速器。"</p>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-container">
          <div class="form-header">
            <h2 class="form-title">{{ isLogin ? '开启你的成功之旅' : '创建你的成功之路' }}</h2>
            <p class="form-subtitle">
              {{ isLogin ? '每一次登录都是新里程碑的起点' : '加入让成功成为必然的旅程' }}
            </p>
          </div>

          <!-- 成功消息 -->
          <div v-if="successMessage" class="success-message">
            {{ successMessage }}
          </div>

          <form @submit.prevent="handleSubmit" @keydown.enter="handleEnterKey" class="auth-form">
            <!-- 邮箱 -->
            <div class="form-group">
              <label class="form-label">邮箱地址</label>
              <input
                ref="emailInput"
                v-model="formData.email"
                type="email"
                class="form-input"
                :class="{ 'input-error': fieldErrors.email }"
                placeholder="请输入邮箱地址"
                :tabindex="isLogin ? 1 : 1"
                @blur="validateField('email')"
                required
              />
              <div v-if="fieldErrors.email" class="field-error">
                {{ fieldErrors.email }}
              </div>
            </div>

            <!-- 密码 -->
            <div class="form-group">
              <label class="form-label">密码</label>
              <div class="password-input-container">
                <input
                  ref="passwordInput"
                  v-model="formData.password"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'input-error': fieldErrors.password }"
                  :placeholder="isLogin ? '请输入密码' : '至少8位，包含大小写字母、数字和特殊字符'"
                  :tabindex="isLogin ? 2 : 2"
                  @input="onPasswordInput"
                  @blur="validateField('password')"
                  required
                />
                <button
                  type="button"
                  class="password-toggle"
                  @click="togglePasswordVisibility"
                  :tabindex="-1"
                >
                  <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              
              <!-- 密码强度指示器 -->
              <div v-if="!isLogin && formData.password && passwordStrength" class="password-strength">
                <div class="strength-bar">
                  <div 
                    class="strength-fill" 
                    :style="{ width: `${(passwordStrength.score + 1) * 20}%`, backgroundColor: passwordStrength.color }"
                  ></div>
                </div>
                <div class="strength-text" :style="{ color: passwordStrength.color }">
                  密码强度：{{ getPasswordStrengthText(passwordStrength.level) }}
                </div>
                <div v-if="passwordStrength.feedback.length > 0" class="strength-feedback">
                  <span class="feedback-text">
                    {{ passwordStrength.feedback.join('，') }}
                  </span>
                </div>
              </div>
              
              <!-- 常见密码警告 -->
              <div v-if="!isLogin && formData.password && isCommonPassword(formData.password)" class="common-password-warning">
                ⚠️ 这是一个常见密码，建议使用更复杂的密码
              </div>
              
              <div v-if="fieldErrors.password" class="field-error">
                {{ fieldErrors.password }}
              </div>
            </div>

            <!-- 注册时显示确认密码 -->
            <div v-if="!isLogin" class="form-group">
              <label class="form-label">确认密码</label>
              <div class="password-input-container">
                <input
                  ref="confirmPasswordInput"
                  v-model="formData.confirmPassword"
                  :type="showPassword ? 'text' : 'password'"
                  class="form-input"
                  :class="{ 'input-error': fieldErrors.confirmPassword }"
                  placeholder="请再次输入密码"
                  :tabindex="3"
                  @blur="validateField('confirmPassword')"
                  required
                />
                <button 
                  type="button" 
                  class="password-toggle"
                  @click="togglePasswordVisibility"
                  :aria-label="showPassword ? '隐藏密码' : '显示密码'"
                >
                  <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              <div v-if="fieldErrors.confirmPassword" class="field-error">
                {{ fieldErrors.confirmPassword }}
              </div>
            </div>

                        <!-- 注册时显示邮箱验证码 -->
            <div v-if="!isLogin" class="form-group">
              <label class="form-label">邮箱验证码</label>
              <div class="verification-group">
                <input
                  ref="verificationCodeInput"
                  v-model="formData.verificationCode"
                  type="text"
                  class="form-input verification-input"
                  :class="{ 'input-error': fieldErrors.verificationCode }"
                  placeholder="请输入6位验证码"
                  maxlength="6"
                  :tabindex="3"
                  @blur="validateField('verificationCode')"
                  required
                />
                <button
                  type="button"
                  class="verification-button"
                  :disabled="!canSendCode || countdown > 0"
                  @click="sendVerificationCode"
                  :tabindex="4"
                >
                  {{ countdown > 0 ? `重新发送(${countdown}s)` : '获取验证码' }}
                </button>
              </div>
              <div v-if="fieldErrors.verificationCode" class="field-error">
                {{ fieldErrors.verificationCode }}
              </div>
            </div>

            <!-- 登录时显示记住我 -->
            <div v-if="isLogin" class="form-options">
              <label class="checkbox-container">
                <input v-model="rememberMe" type="checkbox" :tabindex="3" />
                <span class="checkmark">记住我</span>
              </label>
              <router-link to="/forgot-password" class="forgot-link" :tabindex="4">
                忘记密码？
              </router-link>
            </div>

            <!-- 注册时显示协议 -->
            <div v-if="!isLogin" class="form-options">
              <label class="checkbox-container">
                <input v-model="agreeTerms" type="checkbox" :tabindex="5" required />
                <span class="checkmark">我同意 <a href="#" class="form-link">用户协议</a> 和 <a href="#" class="form-link">隐私政策</a></span>
              </label>
            </div>

            <!-- 提交按钮 -->
            <button 
              type="submit" 
              class="submit-btn" 
              :class="{ 'btn-loading': loading }"
              :disabled="loading"
              :tabindex="isLogin ? 5 : 6"
            >
              <span v-if="loading" class="loading-spinner"></span>
              {{ loading ? '处理中...' : (isLogin ? '登录账户' : '创建账户') }}
            </button>

            <!-- 错误信息 -->
            <div v-if="error" class="error-message">
              <div class="error-icon">⚠️</div>
              <div class="error-content">
                <div class="error-title">{{ isLogin ? '登录失败' : '注册失败' }}</div>
                <div class="error-details">{{ error }}</div>
              </div>
            </div>
          </form>

          <!-- 切换登录/注册 -->
          <div class="form-footer">
            <p class="switch-text">
              {{ isLogin ? '还没有账户？' : '已有账户？' }}
              <button @click="toggleMode" class="switch-btn" :tabindex="isLogin ? 6 : 7">
                {{ isLogin ? '创建新账户' : '登录账户' }}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import { checkPasswordStrength, isCommonPassword, getPasswordStrengthText } from '@/utils/passwordUtils'
import { validateRegistrationData, validateLoginData } from '@/utils/validators'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 模板引用
const emailInput = ref<HTMLInputElement>()
const passwordInput = ref<HTMLInputElement>()
const confirmPasswordInput = ref<HTMLInputElement>()
const verificationCodeInput = ref<HTMLInputElement>()
const journeyProgress = ref<HTMLElement>()

// 状态
const isLogin = ref(true)
const loading = ref(false)
const error = ref('')
const successMessage = ref('')
const rememberMe = ref(false)
const agreeTerms = ref(false)
const showPassword = ref(false)
const currentStep = ref(0)
const countdown = ref(0)
const canSendCode = ref(false)

// 表单数据
const formData = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

// 字段错误
const fieldErrors = reactive({
  email: '',
  password: '',
  confirmPassword: '',
  verificationCode: ''
})

// 计算属性
const passwordStrength = computed(() => {
  if (!formData.password) return null
  return checkPasswordStrength(formData.password)
})

// 生命周期
onMounted(() => {
  // 加载记住的用户名
  loadRememberedUsername()
  
  // 检查URL参数中的mode
  const urlParams = new URLSearchParams(window.location.search)
  const mode = urlParams.get('mode')
  
  if (mode === 'register') {
    isLogin.value = false
    console.log('🔄 检测到注册模式，切换到注册页面')
  } else if (mode === 'login') {
    isLogin.value = true
    console.log('🔄 检测到登录模式，切换到登录页面')
  }
  
  // 设置初始焦点
  nextTick(() => {
    emailInput.value?.focus()
  })
  
  // 启动journey动画
  startJourneyAnimation()
})

// Journey动画函数
const startJourneyAnimation = () => {
  const updateJourney = () => {
    currentStep.value = (currentStep.value + 1) % 4
    setTimeout(updateJourney, 2000)
  }
  setTimeout(updateJourney, 1000)
}

// 记住用户名功能
const loadRememberedUsername = () => {
  const remembered = localStorage.getItem('remembered_username')
  if (remembered) {
    formData.email = remembered
    rememberMe.value = true
  }
}

const saveRememberedUsername = () => {
  if (rememberMe.value) {
    localStorage.setItem('remembered_username', formData.email)
  } else {
    localStorage.removeItem('remembered_username')
  }
}

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// Enter键处理
const handleEnterKey = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSubmit()
  }
}

// 字段验证
const validateField = (fieldName: string) => {
  clearFieldError(fieldName)
  
  switch (fieldName) {
    case 'email':
      if (!formData.email) {
        fieldErrors.email = '邮箱不能为空'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        fieldErrors.email = '请输入有效的邮箱地址'
      }
      break
      
    case 'password':
      if (!formData.password) {
        fieldErrors.password = '密码不能为空'
      } else if (formData.password.length < 8) {
        fieldErrors.password = '密码至少需要8个字符'
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(formData.password)) {
        fieldErrors.password = '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊符号(@$!%*?&)'
      } else if (isCommonPassword(formData.password)) {
        fieldErrors.password = '请使用更复杂的密码'
      }
      break
      
    case 'confirmPassword':
      if (!formData.confirmPassword) {
        fieldErrors.confirmPassword = '请确认密码'
      } else if (formData.password !== formData.confirmPassword) {
        fieldErrors.confirmPassword = '两次输入的密码不一致'
      }
      break
      
    case 'verificationCode':
      if (!formData.verificationCode) {
        fieldErrors.verificationCode = '验证码不能为空'
      } else if (!/^\d{6}$/.test(formData.verificationCode)) {
        fieldErrors.verificationCode = '请输入6位数字验证码'
      }
      break
  }
}

const clearFieldError = (fieldName: string) => {
  fieldErrors[fieldName as keyof typeof fieldErrors] = ''
}

// 密码输入处理
const onPasswordInput = () => {
  if (fieldErrors.password) {
    clearFieldError('password')
  }
  if (formData.confirmPassword && fieldErrors.confirmPassword) {
    clearFieldError('confirmPassword')
  }
}

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  error.value = ''
  successMessage.value = ''
  
  // 更新URL参数
  const newMode = isLogin.value ? 'login' : 'register'
  router.replace({ query: { ...route.query, mode: newMode } })
  
  // 切换模式时保留邮箱，清空密码
  formData.password = ''
  formData.confirmPassword = ''
  formData.verificationCode = ''
  
  // 清空字段错误
  Object.keys(fieldErrors).forEach(key => {
    fieldErrors[key as keyof typeof fieldErrors] = ''
  })
  
  // 重置其他状态
  agreeTerms.value = false
  countdown.value = 0
  canSendCode.value = false
  
  // 设置焦点
  nextTick(() => {
    emailInput.value?.focus()
  })
}

// 监听路由变化以响应模式切换
watch(
  () => route.query.mode,
  (newMode) => {
    if (newMode === 'register' && isLogin.value) {
      isLogin.value = false
      console.log('🔄 URL模式变化：切换到注册')
    } else if (newMode === 'login' && !isLogin.value) {
      isLogin.value = true
      console.log('🔄 URL模式变化：切换到登录')
    }
  }
)

// 表单提交
const handleSubmit = async () => {
  console.log('🚀 开始表单提交，模式:', isLogin.value ? '登录' : '注册')
  
  // 验证所有字段
  const fieldsToValidate = isLogin.value 
    ? ['email', 'password']
    : ['email', 'verificationCode', 'password', 'confirmPassword']
  
  fieldsToValidate.forEach(field => validateField(field))
  
  // 检查是否有错误
  const hasErrors = Object.values(fieldErrors).some(error => error !== '')
  if (hasErrors) {
    console.log('❌ 表单验证失败:', fieldErrors)
    return
  }
  
  // 注册时检查协议同意
  if (!isLogin.value && !agreeTerms.value) {
    error.value = '请同意用户协议和隐私政策'
    return
  }
  
  error.value = ''
  successMessage.value = ''
  loading.value = true

  try {
    if (isLogin.value) {
      // 登录
      console.log('🔐 执行登录操作，邮箱:', formData.email)
      await userStore.login({
        email: formData.email,
        password: formData.password
      })
      
      // 保存记住的用户名
      saveRememberedUsername()
      
      successMessage.value = '登录成功！'
      console.log('✅ 登录成功，准备跳转')
      
      // 成功后跳转
      setTimeout(() => {
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/')
      }, 1000)
      
    } else {
      // 注册
      console.log('📝 执行注册操作，邮箱:', formData.email)
      await userStore.register({
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        verificationCode: formData.verificationCode
      })
      
      successMessage.value = '注册成功！正在为您登录...'
      console.log('✅ 注册成功，准备跳转')
      
      // 成功后跳转
      setTimeout(() => {
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/')
      }, 1000)
    }
    
  } catch (err: any) {
    console.error('认证失败:', err)
    
    // 提供更详细的错误信息
    let errorMessage = '操作失败，请稍后重试'
    
    if (err.response) {
      // 服务器返回了错误响应
      const status = err.response.status
      const data = err.response.data
      
      console.error('❌ HTTP状态码:', status)
      console.error('❌ 服务器响应:', data)
      
      if (status === 401) {
        errorMessage = '邮箱或密码错误'
      } else if (status === 400) {
        errorMessage = data?.message || '请求参数错误'
      } else if (status === 409) {
        errorMessage = data?.message || '邮箱已被注册'
      } else if (status === 422) {
        errorMessage = data?.message || '输入信息格式错误'
      } else if (status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试'
      } else {
        errorMessage = data?.message || `请求失败 (${status})`
      }
    } else if (err.request) {
      // 请求发送了但没有收到响应
      console.error('❌ 网络请求失败:', err.request)
      errorMessage = '网络连接失败，请检查网络连接'
    } else if (err.message) {
      // 其他错误
      console.error('❌ 请求配置错误:', err.message)
      errorMessage = err.message
    }
    
    error.value = errorMessage
    
    // 登录失败时，只清空密码，保留邮箱
    if (isLogin.value) {
      formData.password = ''
    } else {
      // 注册失败时，清空密码字段
      formData.password = ''
      formData.confirmPassword = ''
      formData.verificationCode = ''
    }
    
    // 设置焦点到密码输入框
    nextTick(() => {
      passwordInput.value?.focus()
    })
  } finally {
    loading.value = false
  }
}

// 监听邮箱变化，决定是否可以发送验证码
watch(
  () => formData.email,
  (newEmail) => {
    canSendCode.value = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newEmail)
  }
)

// 发送验证码
const sendVerificationCode = async () => {
  if (!canSendCode.value) {
    fieldErrors.email = '请输入有效的邮箱地址'
    return
  }
  
  try {
    loading.value = true
    
    // 调用后端API发送验证码
    const response = await authApi.sendVerificationCode({
      email: formData.email,
      type: 'registration'
    })
    
    // 开始倒计时
    startCountdown()
    
    successMessage.value = response.message || `验证码已发送至 ${formData.email}`
    
    // 3秒后清除成功消息
    setTimeout(() => {
      successMessage.value = ''
    }, 3000)
    
  } catch (err: any) {
    console.error('发送验证码失败:', err)
    error.value = err.message || '发送验证码失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 倒计时功能
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      countdown.value = 0
    }
  }, 1000)
}
</script>

<style scoped>
.auth-page {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e293b;
  padding: 20px;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10;
  overflow: hidden;
  pointer-events: none;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
  animation: float 40s infinite ease-in-out;
}

.bg-shape:nth-child(1) { 
  width: 400px; 
  height: 400px; 
  left: -150px; 
  top: 10%; 
  animation-delay: 0s; 
}

.bg-shape:nth-child(2) { 
  width: 300px; 
  height: 300px; 
  right: -100px; 
  top: 50%; 
  animation-delay: 15s; 
}

.bg-shape:nth-child(3) { 
  width: 200px; 
  height: 200px; 
  left: 50%; 
  top: 5%; 
  animation-delay: 30s; 
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-30px) rotate(90deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
  75% { transform: translateY(-25px) rotate(270deg); }
}

/* 主容器 */
.auth-container {
  display: flex;
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(102, 126, 234, 0.15);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 680px;
  height: auto;
  margin: 0 auto;
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.brand-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 40%);
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 10px 0;
}

.brand-logo {
  font-size: 48px;
  font-weight: 800;
  letter-spacing: -1px;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #ffffff, #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  font-weight: 500;
  color: #e0e7ff;
}

/* 成功之路展示 */
.journey-container {
  position: relative;
  height: 180px;
  margin: 0 auto 30px;
  width: 90%;
}

.journey-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%);
}

.journey-progress {
  position: absolute;
  top: 50%;
  left: 0;
  width: 0;
  height: 6px;
  background: white;
  transform: translateY(-50%);
  border-radius: 3px;
  animation: progress 3s infinite ease-in-out;
}

.journey-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.journey-step {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s;
  position: relative;
  border: 2px solid transparent;
}

.journey-step.active {
  background: white;
  color: #667eea;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.step-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: white;
  opacity: 0.8;
  white-space: nowrap;
}

@keyframes progress {
  0% {
    width: 0;
  }
  30% {
    width: 30%;
  }
  70% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* 激励语句 */
.inspiration-quote {
  margin-top: 30px;
  font-size: 16px;
  font-style: italic;
  color: #e0e7ff;
  position: relative;
  padding: 0 20px;
  line-height: 1.6;
}

.inspiration-quote::before,
.inspiration-quote::after {
  content: '"';
  position: absolute;
  font-size: 40px;
  color: rgba(255, 255, 255, 0.2);
  font-family: Georgia, serif;
}

.inspiration-quote::before {
  top: -20px;
  left: 0;
}

.inspiration-quote::after {
  bottom: -40px;
  right: 0;
}

/* 右侧表单区域 */
.form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
}

.form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #1e293b;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  color: #64748b;
  font-size: 16px;
}

/* 表单样式 */
.auth-form {
  display: block;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

/* 验证码输入组 */
.verification-group {
  display: flex;
  gap: 10px;
}

.verification-input {
  flex: 1;
}

.verification-button {
  width: 140px;
  padding: 14px 10px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
  white-space: nowrap;
}

.verification-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.verification-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  opacity: 0.7;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

.form-input.input-error {
  border-color: #ef4444;
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  font-size: 14px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-container input {
  width: 16px;
  height: 16px;
}

.checkmark {
  color: #6b7280;
  font-size: 14px;
}

.form-link {
  color: #8b5cf6;
  text-decoration: none;
  font-weight: 500;
}

.form-link:hover {
  text-decoration: underline;
}

.forgot-link {
  color: #8b5cf6;
  text-decoration: none;
  font-weight: 500;
}

.forgot-link:hover {
  text-decoration: underline;
}

.submit-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.submit-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.btn-loading {
  pointer-events: none;
}

/* 切换提示 */
.form-footer {
  text-align: center;
}

.switch-text {
  color: #64748b;
  font-size: 14px;
  margin-top: 10px;
}

.switch-btn {
  color: #8b5cf6;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  margin-left: 4px;
  transition: all 0.3s;
}

.switch-btn:hover {
  text-decoration: underline;
  color: #7c3aed;
}

/* 密码相关样式 */
.password-input-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  opacity: 0.7;
  padding: 4px;
}

.password-toggle:hover {
  opacity: 1;
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s;
  border-radius: 2px;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.strength-feedback {
  font-size: 11px;
  color: #6b7280;
}

.feedback-text {
  line-height: 1.4;
}

.common-password-warning {
  margin-top: 6px;
  font-size: 12px;
  color: #f59e0b;
  background: #fef3c7;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #fbbf24;
}

.field-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* 加载和错误消息 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid white;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #ef4444;
  font-size: 14px;
  padding: 16px;
  background: #fef2f2;
  border-radius: 8px;
  border: 1px solid #fecaca;
  margin-bottom: 16px;
  animation: shake 0.5s;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.error-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-content {
  flex: 1;
}

.error-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #dc2626;
}

.error-details {
  font-size: 13px;
  color: #b91c1c;
  line-height: 1.4;
}

.success-message {
  color: #059669;
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: #f0fdf4;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
  margin-bottom: 16px;
  animation: slideIn 0.5s;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
    height: auto;
    max-width: 500px;
    margin: 20px;
  }

  .brand-section {
    padding: 40px 30px;
  }

  .form-section {
    padding: 40px 30px;
  }
  
  .verification-group {
    flex-direction: column;
  }
  
  .verification-button {
    width: 100%;
    margin-top: 8px;
  }

  .journey-container {
    width: 100%;
    height: 140px;
  }

  .brand-logo {
    font-size: 36px;
  }

  .brand-tagline {
    font-size: 18px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-subtitle {
    font-size: 14px;
  }

  .journey-step {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .step-label {
    font-size: 10px;
  }

  .inspiration-quote {
    font-size: 14px;
    margin-top: 20px;
  }
}
</style> 