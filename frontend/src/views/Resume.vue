<template>
  <div class="resume-container">
    <!-- 头部标题和操作按钮 -->
    <div class="resume-header">
      <h1 class="resume-title"><i class="fas fa-file-alt"></i> 简历管理</h1>
      <div class="resume-actions">
        <button class="btn btn-outline">
          <i class="fas fa-eye"></i>
          预览简历
        </button>
        <button class="btn btn-primary">
          <极狐 class="fas fa-download"></i>
          导出PDF
        </button>
      </div>
    </div>
    
    <!-- 简历认证部分 -->
    <div class="certification-section">
      <div class="极狐certification-status">
        <div class="status-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <div>
          <div class="status-text">您的简历已通过平台认证</div>
          <div class="status-desc">认证简历在平台中排名更靠前，获得更多优质机会</div>
        </div>
      </div>
      <button class="btn btn-primary">
        <i class="fas fa-info-circle"></i>
        查看认证详情
      </button>
    </div>
    
    <!-- 工作偏好设置 -->
    <div class="preferences-section">
      <div class="section-header">
        <div class="section-icon">
          <i class="fas fa-sliders-h"></i>
        </div>
        <h2 class="section-title">工作偏好设置</h2>
      </div>
      
      <div class="work-preferences">
        <div class="preference-item">
          <label class="preference-label"><i class="fas fa-briefcase"></i> 期望职位</label>
          <input 
            type="text" 
            class="input-field" 
            placeholder="例如：高级全栈工程师"
            value="高级全栈工程师"
          >
        </div>
        
        <div class="preference-item">
          <label class="preference-label"><i class="fas fa-industry"></i> 期望行业</label>
          <select class="select-box">
            <option>互联网/IT</option>
            <option>金融</option>
            <option>教育</option>
            <option>医疗健康</option>
            <option selected>互联网/IT</option>
          </select>
        </div>
        
        <div class="preference-item">
          <label class="preference-label"><i class="fas fa-money-bill-wave"></i> 期望薪资</label>
          <select class="select-box">
            <option>面议</option>
            <option>15k-25k</option>
            <option selected>25k-40k</option>
            <option>40k以上</option>
          </select>
        </div>
        
        <div class="preference-item">
          <label class="preference-label"><i class="fas fa-city"></i> 工作城市</label>
          <select class="select-box">
            <option>北京</option>
            <option>上海</option>
            <option>深圳</option>
            <option>杭州</option>
            <option selected>北京</option>
          </select>
        </div>
      </div>
    </div>
    
    <!-- 工作经验部分 -->
    <div class="experience-section">
      <div class="section-header">
        <div class="section-icon">
          <i class="fas fa-briefcase"></i>
        </div>
        <h2 class="section-title">工作经验</h2>
      </div>
      
      <div class="experience-list">
        <div class="experience-item">
          <div class="experience-header">
            <h3 class="experience-title"><i class="fas fa-laptop-code"></i> 高级全栈工程师</h3>
            <div class="experience-actions">
              <button class="action-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="experience-details">
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-building"></i> 公司</span>
              <span class="detail-value">北京字节跳动科技有限公司</span>
            </div>
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-calendar-alt"></i> 时间</span>
              <span class="detail-value">2021年3月 - 至今</span>
            </div>
          </div>
          
          <p class="experience-desc">
            <i class="fas fa-tasks"></i> 负责公司内部管理系统的全栈开发，使用React + Node.js技术栈。主导了微服务架构改造，系统性能提升40%。带领3人团队完成多个核心业务模块开发。
          </p>
        </div>
        
        <div class="experience-item">
          <div class="experience-header">
            <h3 class="experience-title"><i class="fas fa-code"></i> 前端开发工程师</h3>
            <div class="experience-actions">
              <button class="action-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="experience-details">
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-building"></i> 公司</span>
              <span class="detail-value">美团点评</span>
            </div>
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-calendar-alt"></i> 时间</span>
              <span class="detail-value">2019年7月 - 2021年2月</span>
            </div>
          </div>
          
          <p class="experience-desc">
            <i class="fas fa-tasks"></i> 参与美团商家端产品开发，负责前端界面设计和用户体验优化。独立完成移动端H5页面开发，用户访问量提升25%。
          </p>
        </div>
        
        <div class="add-experience">
          <i class="fas fa-plus-circle"></i>
          <span>添加工作经验</span>
        </div>
      </div>
    </div>
    
    <!-- 项目经验部分 -->
    <div class="project-section">
      <div class="section-header">
        <div class="section-icon">
          <i class="fas fa-project-diagram"></i>
        </div>
        <h2 class="section-title">项目经验</h2>
      </div>
      
      <div class="project-list">
        <div class="project-item">
          <div class="project-header">
            <h3 class="project-title"><i class="fas fa-cogs"></i> 企业级CRM系统</h3>
            <div class="experience-actions">
              <button class="action-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="project-details">
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-calendar-alt"></i> 时间</span>
              <span class="detail-value">2023年8月 - 2024年1极狐月</span>
            </div>
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-user-tie"></i> 角色</span>
              <span class="detail-value">技术负责人</span>
            </div>
          </div>
          
          <p class="project-desc">
            <i class="fas fa-info-circle"></i> 为中小企业定制的客户关系管理系统，支持销售流程管理、客户数据分析等功能。系统上线后帮助客户提升销售效率30%，获得用户高度认可。
          </p>
          
          <div class="project-tech">
            <span class="tech-tag"><i class="fab fa-react"></i> React</span>
            <span class="tech-tag"><i class="fab fa-node-js"></i> Node.js</span>
            <span class="tech-tag"><i class="fas fa-database"></i> PostgreSQL</span>
            <span class="tech-tag"><i class="fas fa-server"></i> Redis</span>
            <span class="tech-tag"><i class="fab fa-docker"></i> Docker</span>
          </div>
        </div>
        
        <div class="project-item">
          <div class="project-header">
            <h3 class="project-title"><i class="fas fa-graduation-cap"></i> 在线教育平台</h3>
            <div class="experience-actions">
              <button class="action-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="project-details">
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-calendar-alt"></i> 时间</span>
              <span class="detail-value">2024年2月 - 至今</span>
            </div>
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-user-tie"></i> 角色</span>
              <span class="detail-value">全栈开发</span>
            </div>
          </div>
          
          <p class="project-desc">
            <i class="fas fa-info-circle"></i> 支持直播授课、作业提交、学习进度跟踪的综合教育平台。目前已完成核心功能开发，正在进行用户测试和功能优化。
          </p>
          
          <div class="project-tech">
            <span class="tech-tag"><i class="fab fa-vuejs"></i> Vue.js</span>
            <span class="tech-tag"><i class="fas fa-code"></i> Express</span>
            <span class="tech-tag"><i class="fas fa-database"></i> MongoDB</span>
            <span class="tech-tag"><i class="fas fa-video"></i> WebRTC</span>
            <span class="tech-tag"><i class="fas fa-network-wired"></i> Socket.io</span>
          </div>
        </div>
        
        <div class="add-project">
          <i class="fas fa-plus-circle"></i>
          <span>添加项目经验</span>
        </div>
      </div>
    </div>
    
    <!-- 教育经历部分 -->
    <div class="education-section">
      <div class="section-header">
        <div class="section-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <h2 class="section-title">教育经历</h2>
      </div>
      
      <div class="education-list">
        <div class="education-item">
          <div class="education-header">
            <h3 class="education-title"><i class="fas fa-user-graduate"></i> 计算机科学与技术</h3>
            <div class="experience-actions">
              <button class="action-btn">
                <i class="fas fa-edit"></i>
              </button>
              <button class="action-btn">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          
          <div class="education-details">
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-university"></i> 学校</span>
              <span class="detail-value">北京理工大学</span>
            </div>
            <div class="detail-item">
              <span class="detail-label"><i class="fas fa-calendar-alt"></i> 时间</span>
              <span class="detail-value">2016-2020</span>
            </div>
          </div>
          
          <p class="education-desc">
            <i class="fas fa-award"></i> 主修计算机科学基础课程，GPA 3.8/4.0，曾获得校级优秀学生奖学金。
          </p>
        </div>
        
        <div class="add-education">
          <i class="fas fa-plus-circle"></i>
          <span>添加教育经历</span>
        </div>
      </div>
    </div>
    
    <!-- 个人优势部分 -->
    <div class="strengths-section">
      <div class="section-header">
        <div class="section-icon">
          <i class="fas fa-star"></i>
        </div>
        <h2 class="section-title">个人优势</h2>
      </div>
      
      <div class="strengths-form">
        <textarea 
          class="strengths-textarea" 
          placeholder="请描述您的专业技能、工作经验优势、个人特长等..."
        >熟练掌握前后端开发技术栈，具备完整的项目开发经验。擅长团队协作，有较强的学习能力和问题解决能力。</textarea>
        <p class="strengths-hint">
          <i class="fas fa-lightbulb"></i> 建议从技术能力、项目经验、团队协作、学习能力等角度描述您的优势
        </p>
      </div>
    </div>
    
    <!-- 保存按钮 -->
    <div class="save-section">
      <button class="btn btn-save">
        <i class="fas fa-save"></i>
        保存简历信息
      </button>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue';

// 动态加载Font Awesome
const loadFontAwesome = () => {
  if (!document.querySelector('#font-awesome')) {
    const link = document.createElement('link');
    link.id = 'font-awesome';
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
    document.head.appendChild(link);
  }
};

onMounted(() => {
  loadFontAwesome();
});
</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --secondary: #8b5cf6;
  --dark: #1e293b;
  --light: #f8fafc;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-600: #4b5563;
  --gray-800: #1f2937;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 16px;
  --transition: all 0.3s ease;
}

body {
  background: #f8fafc;
  color: var(--dark);
  line-height: 1.6;
  font-size: 14px;
  padding: 0;
}

.resume-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.resume-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--gray-200);
}

.resume-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--dark);
  display: flex;
  align-items: center;
  gap: 10px;
}

.resume-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 10px 20px;
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: 14px;
  transition: var(--transition);
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-outline {
  background: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: white;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  border: none;
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-save {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 14px 32px;
  font-size: 16px;
  font-weight: 600;
}

.btn-save:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 简历认证部分 */
.certification-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 24px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.certification-status {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #22c55e;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.status-text {
  font-size: 18px;
  font-weight: 极狐600;
  color: #166534;
}

.status-desc {
  font-size: 14px;
  color: var(--gray-600);
  margin-top: 4px;
}

/* 工作偏好设置 */
.preferences-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 30px;
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.section-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--dark);
}

.work-preferences {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

@media (max-width: 600px) {
  .work-preferences {
    grid-template-columns: 1fr;
  }
}

.preference-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preference-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-box {
  padding: 12px 16px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  font-size: 15px;
  font-weight: 500;
  color: var(--dark);
  width: 100%;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/s极狐vg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.input-field {
  padding: 12px 16px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  font-size: 15px;
  font-weight: 500;
  color: var(--dark);
  width: 100%;
}

/* 工作经验部分 */
.experience-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 30px;
  margin-bottom: 30px;
}

.experience-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.experience-item {
  padding: 20px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.experience-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.experience-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  background: white;
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.action-btn:hover {
  background: var(--primary);
  color: white;
  border-color: transparent;
  transform: scale(1.1);
}

.experience-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: var(--gray-600);
  display: flex;
  align-items: center;
  gap: 4px;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--dark);
}

.experience-desc {
  font-size: 14px;
  color: var(--gray-600);
  line-height: 1.6;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.add-experience {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.add-experience:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

/* 项目经验部分 */
.project-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 30px;
  margin-bottom: 30px;
}

.project-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.project-item {
  padding: 20px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.project-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.project-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.project-desc {
  font-size: 14px;
  color: var(--gray-600);
  line-height: 1.6;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tag {
  background: white;
  color: var(--primary);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: 6px;
}

.add-project {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.add-project:hover {
  border-color: var(--primary);
  color: var(--primary);
  background: rgba(99, 102, 241, 0.05);
}

/* 教育经历部分 */
.education-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 30px;
  margin-bottom: 30px;
}

.education-list {
  display: flex;
  flex-direction: column;
  gap: