<template>
  <div class="projects-page">
    <!-- 浮动装饰背景 -->
    <div class="floating-shapes">
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
    </div>

    <!-- 导航栏 -->
    <Navbar />

    <!-- 精选项目Banner -->
    <div class="featured-banner">
      <div class="banner-header">
        <!-- 删除.banner-title和.banner-badge，仅保留内容区 -->
      </div>
      <div class="featured-projects">
        <div v-for="(item, idx) in featuredList" :key="item.title" class="featured-project-card" :class="{ active: idx === featuredIndex }" @click="goToFeaturedProject(item)">
          <div class="project-content">
            <div class="project-left">
              <h3 class="featured-project-title">{{ item.title }}</h3>
              <p class="featured-project-summary">{{ item.summary }}</p>
              <div class="featured-project-tags">
                <span v-for="tag in item.tags" :key="tag" class="featured-tag">{{ tag }}</span>
              </div>
            </div>
            <div class="project-right">
              <div class="project-right-item">
                <span class="project-right-label">工作性质</span>
                <span class="project-right-value">{{ item.workType }}</span>
              </div>
              <div class="project-right-item">
                <span class="project-right-label">工作地点</span>
                <span class="project-right-value">{{ item.location }}</span>
              </div>
              <div class="project-right-item">
                <span class="project-right-label">项目阶段</span>
                <span class="project-right-value">{{ item.stage }}</span>
              </div>
              <div class="project-status-indicator">
                <div class="status-dot-banner"></div>
                <span class="status-text-banner">{{ item.status }}</span>
              </div>
              <div class="featured-price">{{ item.price }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- banner切换按钮移到下方并居中 -->
      <div class="banner-nav banner-nav-bottom">
        <button 
          v-for="(item, index) in featuredList" 
          :key="index"
          class="banner-nav-dot" 
          :class="{ active: index === featuredIndex }"
          @click="featuredIndex = index"
        ></button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner">正在加载项目...</div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-message">{{ error }}</div>
      <button @click="loadProjects" class="retry-btn">重试</button>
    </div>

    <!-- 项目内容区 -->
    <div v-else class="projects-content">
      <!-- 区域切换标签 -->
      <div class="section-tabs">
        <div 
          class="section-tab free"
          :class="{ active: activeTab === 'free' }"
          @click="activeTab = 'free'"
        >
          盈利分红
        </div>
        <div 
          class="section-tab paid"
          :class="{ active: activeTab === 'paid' }"
          @click="activeTab = 'paid'"
        >
          薪资保障
        </div>
      </div>

      <!-- 项目网格 -->
      <div class="projects-grid">
        <div 
          v-for="project in filteredProjects" 
          :key="project.id"
          class="project-card"
          :class="project.type + '-zone'"
          @click="goToProjectDetail(project)"
        >
          <!-- 薪资类型徽章 -->
          <div class="salary-type-badge" :class="'salary-type-' + project.type">
            {{ project.type === 'free' ? '免费区' : '付费区' }}
          </div>
          
          <!-- 项目标题和摘要 -->
          <div class="project-header">
            <h3 class="project-title">{{ project.title }}</h3>
            <p class="project-summary">{{ project.summary }}</p>
          </div>
          
          <!-- 项目核心信息 -->
          <div class="project-core-info">
            <div class="info-item">
              <span class="info-label">工作性质</span>
              <span class="info-value">{{ project.workType }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工作时间</span>
              <span class="info-value">{{ project.workTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">工作地点</span>
              <span class="info-value">{{ project.location }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">项目阶段</span>
              <span class="info-value">{{ project.stage }}</span>
            </div>
          </div>
          
          <!-- 招募岗位 -->
          <div class="project-positions">
            <div class="positions-title">招募岗位</div>
            <div class="position-tags">
              <span 
                v-for="position in project.positions" 
                :key="position" 
                class="position-tag"
              >
                {{ position }}
              </span>
            </div>
          </div>
          
          <!-- 技能要求 -->
          <div class="project-skills">
            <div class="skills-title">技能要求</div>
            <div class="skill-tags">
              <span 
                v-for="skill in project.skills.slice(0, 4)" 
                :key="skill" 
                class="skill-tag"
              >
                {{ skill }}
              </span>
            </div>
          </div>
          
          <!-- 时间线指示器 -->
          <div class="timeline-indicator">
            <div class="timeline-progress">
              <div class="timeline-dot" :class="{ completed: project.progress >= 1 }"></div>
              <div class="timeline-connector" :class="{ active: project.progress > 1 }"></div>
              <div class="timeline-dot" :class="{ completed: project.progress >= 2, active: project.progress >= 2 && project.progress < 3 }"></div>
              <div class="timeline-connector" :class="{ active: project.progress > 2 }"></div>
              <div class="timeline-dot" :class="{ completed: project.progress >= 3 }"></div>
            </div>
            <div>
              <span class="timeline-text">{{ project.currentStage }}</span>
              <div class="timeline-phase">{{ project.timeline }}</div>
            </div>
          </div>
          
          <!-- 项目底部 -->
          <div class="project-footer">
            <div class="project-meta">
              <span class="meta-item">
                <span class="meta-icon">📅</span>
                <span class="meta-text">{{ project.publishDate }}</span>
              </span>
              <span class="meta-item">
                <span class="meta-icon">👥</span>
                <span class="meta-text">{{ project.applicants }}人申请</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div v-if="filteredProjects.length > 0" class="load-more">
        <button @click="loadMore" :disabled="loadingMore" class="load-more-btn">
          {{ loadingMore ? '加载中...' : '加载更多项目' }}
        </button>
        <div v-if="loadingMore" class="loading-spinner">正在加载更多项目...</div>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <div class="empty-icon">📋</div>
        <div class="empty-title">暂无{{ activeTab === 'free' ? '免费' : '付费' }}项目</div>
        <div class="empty-subtitle">请切换到{{ activeTab === 'free' ? '付费' : '免费' }}区查看更多项目</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../stores/user'
// @ts-ignore
import Navbar from '../components/layout/Navbar.vue'
import { getProjects, getFeaturedProjects } from '../api/project'

const router = useRouter()
const userStore = useUserStore()

// 精选项目类型定义
interface FeaturedProject {
  id?: string | number
  title: string
  summary: string
  tags: string[]
  workType: string
  location: string
  stage: string
  status: string
  price: string
}

// UI项目类型定义
interface UIProject {
  id: number
  title: string
  summary: string
  type: 'free' | 'paid'
  isAiRecommended: boolean
  workType: string
  workTime: string
  location: string
  stage: string
  positions: string[]
  skills: string[]
  progress: number
  currentStage: string
  timeline: string
  publishDate: string
  applicants: number | any[]
  featured: boolean // 添加featured字段
  originalData: any
}

// 响应式状态
const activeTab = ref('free')
const loadingMore = ref(false)
const loading = ref(true)
const error = ref('')

// 项目数据
const projects = ref<UIProject[]>([])

// 计算属性
const filteredProjects = computed(() => {
  return projects.value.filter(project => project.type === activeTab.value)
})

const isAuthenticated = computed(() => userStore.isAuthenticated)
const currentUser = computed(() => userStore.user)

// 精选项目数据
const featuredList = ref<FeaturedProject[]>([])
const featuredIndex = ref(0)
let timer: any = null

// 加载精选项目数据
const loadFeaturedProjects = async () => {
  try {
    const response = await getFeaturedProjects()
    console.log('🌟 精选项目API响应:', response)
    
    const featuredArray = Array.isArray(response.data?.projects)
      ? response.data.projects
      : Array.isArray(response.projects)
        ? response.projects
        : []
    
    console.log('🌟 精选项目数组:', featuredArray)
    
    if (featuredArray.length > 0) {
      featuredList.value = featuredArray.slice(0, 3).map((project: any) => ({
        id: project.id, // 保存项目ID
        title: project.title,
        summary: project.summary || project.description?.substring(0, 100) + '...' || '优质项目，期待您的加入',
        tags: ['精选', '热门'],
        workType: project.workType || '远程',
        location: project.location || '全球',
        stage: project.stage || '招募中',
        status: '热招中',
        price: project.budget || '面议'
      }))
    } else {
      // 如果没有精选项目，设置默认数据
      featuredList.value = [{
        title: '暂无精选项目',
        summary: '管理员还未设置精选项目，请稍后查看',
        tags: ['待更新'],
        workType: '远程',
        location: '全球',
        stage: '待发布',
        status: '准备中',
        price: '面议'
      }]
    }
    
    console.log('🌟 转换后的精选项目:', featuredList.value)
  } catch (error) {
    console.error('❌ 加载精选项目失败:', error)
    featuredList.value = [{
      title: '加载失败',
      summary: '精选项目加载失败，请刷新页面重试',
      tags: ['错误'],
      workType: '远程',
      location: '全球',
      stage: '错误',
      status: '错误',
      price: '面议'
    }]
  }
}

function switchFeatured(dir: number) {
  if (featuredList.value.length === 0) return
  featuredIndex.value = (featuredIndex.value + dir + featuredList.value.length) % featuredList.value.length
}

function startFeaturedAuto() {
  timer && clearInterval(timer)
  timer = setInterval(() => {
    if (featuredList.value.length > 1) {
      switchFeatured(1)
    }
  }, 5000)
}

onMounted(async () => { 
  loadFeaturedProjects().then(() => {
    startFeaturedAuto()
  })
  loadProjects() 
})

// API调用函数
const getProjectsData = async () => {
  try {
    console.log('🌐 发起API请求: /api/projects')
    
    // 构建请求头，如果有token则加入
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }
    
    if (userStore.token) {
      headers['Authorization'] = `Bearer ${userStore.token}`
      console.log('🔐 已添加认证令牌到请求头')
    }
    
    const response = await fetch('/api/projects', { headers })
    console.log('📡 响应状态:', response.status, response.statusText)
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    console.log('📦 原始API响应:', data)
    
    // 根据实际响应结构处理数据
    if (data.success && data.data && data.data.projects) {
      return data.data
    } else if (data.projects) {
      return data
    } else if (Array.isArray(data)) {
      return { projects: data }
    } else {
      console.warn('⚠️ API响应格式异常:', data)
      return { projects: [] }
    }
  } catch (err) {
    console.error('❌ API调用失败:', err)
    throw err
  }
}

// 转换项目数据格式
const transformProjectData = (apiProjects: any[]): UIProject[] => {
  console.log('🔄 开始转换项目数据:', apiProjects)
  
  return apiProjects.map((project: any) => {
    // 提取技能标签
    const skills = project.requirements?.flatMap((req: any) => {
      if (Array.isArray(req.skillName)) {
        return req.skillName
      } else if (typeof req.skillName === 'string') {
        return req.skillName.split(',').map((s: string) => s.trim())
      }
      return []
    }).filter(Boolean) || []
    
    // 提取职位
    const positions = project.requirements?.map((req: any) => req.role).filter(Boolean) || []
    
    // 判断是否为付费项目
    const isPaid = project.requirements?.some((req: any) => 
      req.cooperation === 'salary_equity' || req.cooperation === 'salary'
    ) || false
    
    // 计算进度阶段
    const getProgressStage = (milestone: string) => {
      const stages = {
        'team-building': { progress: 1, stage: '组建团队' },
        'requirement-design': { progress: 1.5, stage: '需求设计' },
        'development': { progress: 2, stage: '开发阶段' },
        'testing-launch': { progress: 2.5, stage: '测试阶段' },
        'completed': { progress: 3, stage: '已完成' }
      }
      return stages[milestone as keyof typeof stages] || { progress: 1, stage: '设计阶段' }
    }
    
    const progressInfo = getProgressStage(project.currentMilestone)
    
    // 格式化日期
    const formatDate = (dateString: string) => {
      try {
        return new Date(dateString).toLocaleDateString('zh-CN', { 
          month: 'numeric', 
          day: 'numeric' 
        })
      } catch {
        return '最近'
      }
    }
    
    const transformedProject = {
      id: project.id,
      title: project.title,
      summary: project.tagline || (project.description?.substring(0, 50) + '...'),
      type: (isPaid ? 'paid' : 'free') as 'free' | 'paid',
      isAiRecommended: Boolean(project.featured), // 使用featured字段标记为AI推荐
      workType: project.workArrangement === 'fullTime' ? '全职' : '兼职',
      workTime: project.workArrangement === 'fullTime' ? '标准工时' : '灵活安排',
      location: project.location || '远程协作',
      stage: progressInfo.stage,
      positions: positions,
      skills: skills,
      progress: progressInfo.progress,
      currentStage: progressInfo.stage,
      timeline: project.timeframe || '预计3个月完成',
      publishDate: formatDate(project.createdAt),
      applicants: Array.isArray(project.applicants) ? project.applicants.length : (project.applicants || 0),
      featured: Boolean(project.featured), // 添加featured字段
      originalData: project
    }
    
    console.log('✅ 转换项目:', transformedProject)
    return transformedProject
  })
}

// 加载项目数据
const loadProjects = async () => {
  try {
    loading.value = true
    error.value = ''
    console.log('🔄 开始加载项目数据...')
    
    const response = await getProjects()
    // 兼容后端返回结构
    const projectsArray = Array.isArray(response.data?.projects)
      ? response.data.projects
      : Array.isArray(response.projects)
        ? response.projects
        : []
    console.log('📋 原始项目数组:', projectsArray.length, projectsArray)
    
    // 只显示已审核通过的项目 
    const approvedProjects = projectsArray.filter((p: any) => {
      return p.reviewStatus === 'approved' && p.status === 'recruiting'
    })
    
    if (approvedProjects.length === 0) {
      projects.value = transformProjectData(projectsArray)
    } else {
      projects.value = transformProjectData(approvedProjects)
    }
  } catch (err) {
    error.value = `加载项目失败: ${err instanceof Error ? err.message : '未知错误'}`
  } finally {
    loading.value = false
  }
}

// 加载更多项目
const loadMore = async () => {
  loadingMore.value = true
  // 模拟加载延迟
  setTimeout(() => {
    loadingMore.value = false
  }, 1000)
}

// 跳转到精选项目详情
const goToFeaturedProject = (featuredProject: FeaturedProject) => {
  if (featuredProject.id) {
    console.log('🔗 跳转到精选项目详情:', featuredProject.id)
    router.push(`/project/${featuredProject.id}`)
  } else {
    console.log('⚠️ 精选项目没有ID，无法跳转')
  }
}

// 跳转到项目详情
const goToProjectDetail = (project: UIProject) => {
  console.log('🔗 跳转到项目详情:', project.id)
  router.push(`/project/${project.id}`)
}

// 申请项目
const applyForProject = async (project: UIProject, event: Event) => {
  event.stopPropagation()
  
  // 检查登录状态
  if (!isAuthenticated.value) {
    console.log('🔐 用户未登录，跳转到登录页面')
    router.push('/auth')
    return
  }
  
  console.log('📝 申请项目:', project.title, '用户:', currentUser.value?.username)
  
  try {
    // 构建请求头
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    }
    
    if (userStore.token) {
      headers['Authorization'] = `Bearer ${userStore.token}`
    }
    
    const response = await fetch(`/api/projects/${project.id}/apply`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        message: '我对这个项目很感兴趣，希望能够加入团队！',
      })
    })
    
    if (response.ok) {
      // 更新申请人数显示
      if (Array.isArray(project.applicants)) {
        project.applicants.push({ id: Date.now(), status: 'pending' })
      } else {
        project.applicants = Number(project.applicants) + 1
      }
      alert('申请提交成功！')
    } else {
      const errorData = await response.json()
      throw new Error(errorData.message || '申请失败')
    }
    
  } catch (err) {
    console.error('申请失败:', err)
    alert(err instanceof Error ? err.message : '申请失败，请稍后重试')
  }
}

// 初始化
// onMounted(async () => {
//   console.log('🚀 Projects页面开始初始化')
  
//   // 首先初始化用户状态
//   if (userStore.token && !userStore.user) {
//     console.log('🔄 检测到token但无用户信息，开始初始化用户')
//     try {
//       await userStore.initUser()
//       console.log('✅ 用户信息初始化完成:', userStore.user?.username)
//     } catch (error) {
//       console.error('❌ 用户信息初始化失败:', error)
//     }
//   }
  
//   // 然后加载项目数据
//   await loadProjects()
  
//   console.log('✅ Projects页面初始化完成')
//   console.log('👤 当前用户状态:', {
//     isAuthenticated: isAuthenticated.value,
//     user: currentUser.value?.username,
//     token: userStore.token ? '已设置' : '未设置'
//   })
// })
</script>

<style scoped>
/* 页面基础样式 */
.projects-page {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
}

.floating-shapes {
  display: none;
}

/* 内容区域 */
.projects-content {
  max-width: 1400px;
  margin: 72px auto 0;
  padding: 32px 20px;
  position: relative;
  z-index: 2;
}

/* 区域切换标签 */
.section-tabs {
  display: flex;
  margin-bottom: 32px;
  margin-top: 16px;
  background: white;
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}
.section-tab {
  flex: 1;
  padding: 12px 24px;
  text-align: center;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  font-size: 1rem;
}
.section-tab.free {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: white;
}
.section-tab.paid {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
}
.section-tab:not(.active) {
  background: #f8fafc;
  color: #6b7280;
}
.section-tab.active {
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.08);
  z-index: 1;
}

/* 项目网格 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

/* 项目卡片 */
.project-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  min-height: 450px;
  display: flex;
  flex-direction: column;
}
.project-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}
.project-card.free-zone {
  background: linear-gradient(135deg, #f0fdf4 0%, #f7fee7 100%);
  border-color: #bbf7d0;
}
.project-card.free-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #22c55e, #16a34a);
}
.project-card.paid-zone {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
  border-color: #d8b4fe;
}
.project-card.paid-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #9333ea, #7c3aed);
}

/* 薪资类型徽章 */
.salary-type-badge {
  position: absolute;
  top: 0;
  right: 16px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.salary-type-free {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: white;
}
.salary-type-paid {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 项目标题区 */
.project-header {
  margin-bottom: 16px;
  padding-right: 100px;
  margin-top: 0;
}
.project-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
  line-height: 1.3;
}
.project-summary {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

/* 核心信息区域 */
.project-core-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}
.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}
.info-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 600;
}

/* 招募岗位 */
.project-positions {
  margin-bottom: 16px;
}
.positions-title {
  font-size: 13px;
  color: #374151;
  font-weight: 600;
  margin-bottom: 8px;
}
.position-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
.position-tag {
  padding: 4px 8px;
  background: #ddd6fe;
  color: #5b21b6;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 技能标签 */
.project-skills {
  margin-bottom: 20px;
}
.skills-title {
  font-size: 13px;
  color: #374151;
  font-weight: 600;
  margin-bottom: 8px;
}
.skill-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
.skill-tag {
  padding: 4px 8px;
  background: #e0f2fe;
  color: #0369a1;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
}

/* 时间线指示器 */
.timeline-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}
.timeline-indicator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
}
.timeline-progress {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}
.timeline-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #cbd5e1;
  transition: all 0.3s ease;
  position: relative;
}
.timeline-dot.completed {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}
.timeline-dot.active {
  background: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  animation: pulse-timeline 2s infinite;
}
@keyframes pulse-timeline {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}
.timeline-connector {
  flex: 1;
  height: 2px;
  background: #e2e8f0;
  border-radius: 1px;
  position: relative;
}
.timeline-connector.active {
  background: linear-gradient(90deg, #10b981, #3b82f6);
}
.timeline-text {
  font-size: 12px;
  color: #1e293b;
  font-weight: 600;
  margin-left: 8px;
}
.timeline-phase {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 项目底部信息 */
.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
  margin-top: auto;
}
.project-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}
.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #f8fafc;
  border-radius: 6px;
  transition: all 0.2s;
}
.meta-item:hover {
  background: #f1f5f9;
}
.meta-icon {
  font-size: 12px;
  opacity: 0.8;
}
.meta-text {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}
.apply-btn {
  padding: 8px 16px;
  background: #1a1a1a;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}
.apply-btn:hover {
  background: #374151;
}

/* 加载更多 */
.load-more {
  text-align: center;
  margin-top: 40px;
}
.load-more-btn {
  padding: 14px 32px;
  background: white;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}
.load-more-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}
.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 加载状态和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 16px;
  position: relative;
  z-index: 2;
}
.loading-spinner {
  font-size: 18px;
  color: #6366f1;
  text-align: center;
  padding: 16px;
  margin: 16px 0;
}
.error-message {
  color: #ef4444;
  font-size: 18px;
  text-align: center;
}
.retry-btn {
  padding: 12px 24px;
  background: white;
  color: #6366f1;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}
.retry-btn:hover {
  background: #f3f4f6;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}
.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}
.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}
.empty-subtitle {
  font-size: 1rem;
  opacity: 0.8;
}

/* 精选项目Banner样式（再次微调） */
.featured-banner {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  border-radius: 16px;
  padding: 16px 20px;
  margin: 64px 0 16px 0; /* 顶部留出导航栏高度，底部缩小间距 */
  color: white;
  min-height: 80px;
  position: relative;
  box-shadow: 0 2px 12px rgba(16,185,129,0.08);
}
.banner-header {
  display: none;
}
.banner-nav {
  display: flex;
  gap: 6px;
}
.banner-nav-bottom {
  justify-content: center;
  margin-top: 8px;
}
.banner-nav-dot {
  width: 12px;
  height: 12px;
  background: rgba(255,255,255,0.3);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  opacity: 0.6;
}
.banner-nav-dot:hover {
  background: rgba(255,255,255,0.8);
  opacity: 1;
  transform: scale(1.2);
}
.banner-nav-dot.active {
  background: rgba(255,255,255,0.9);
  opacity: 1;
  transform: scale(1.3);
  box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}
.featured-projects {
  position: relative;
  min-height: 60px;
}
.featured-project-card {
  display: none;
  position: relative;
}
.featured-project-card.active {
  display: block;
}
.project-content {
  display: grid;
  grid-template-columns: 1fr 220px;
  gap: 24px;
  height: 100%;
  align-items: center;
}
.project-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 6px;
}
.featured-project-title {
  font-size: 28px;
  font-weight: 800;
  margin-bottom: 8px;
  color: white;
}
.featured-project-summary {
  font-size: 18px;
  color: rgba(255,255,255,0.95);
  margin-bottom: 12px;
  line-height: 1.5;
}
.featured-project-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}
.featured-tag {
  background: rgba(255,255,255,0.16);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: white;
}
.project-right {
  background: rgba(255,255,255,0.10);
  backdrop-filter: blur(6px);
  border-radius: 14px;
  padding: 16px 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  border: 1px solid rgba(255,255,255,0.13);
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.project-right-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.project-right-label {
  font-size: 10px;
  color: rgba(255,255,255,0.7);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}
.project-right-value {
  font-size: 13px;
  color: rgba(255,255,255,0.95);
  font-weight: 600;
}
.featured-price {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 18px;
  font-weight: 800;
  margin-top: 2px;
  text-shadow: 0 1px 2px rgba(251, 191, 36, 0.13);
}
.project-status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(16, 185, 129, 0.13);
  border-radius: 8px;
  border: 1px solid rgba(16, 185, 129, 0.18);
}
.status-dot-banner {
  width: 5px;
  height: 5px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}
.status-text-banner {
  font-size: 10px;
  color: rgba(255,255,255,0.9);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}
@media (max-width: 768px) {
  .featured-banner {
    padding: 12px 6px;
  }
  .project-content {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  .featured-project-title {
    font-size: 15px;
  }
  .banner-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  .project-right {
    padding: 10px 4px;
  }
}
</style> 