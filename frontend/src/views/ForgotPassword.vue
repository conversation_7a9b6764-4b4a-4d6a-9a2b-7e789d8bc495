<template>
  <div class="auth-page">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-shape"></div>
      <div class="bg-shape"></div>
      <div class="bg-shape"></div>
    </div>

    <!-- 主容器 -->
    <div class="auth-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-overlay"></div>
        <div class="brand-content">
          <div class="brand-logo">小概率</div>
          <h2 class="brand-tagline">安全重置您的密码</h2>

          <div class="security-icon">
            <i class="fas fa-lock"></i>
          </div>

          <div class="journey-container">
            <div class="journey-line"></div>
            <div class="journey-progress"></div>
            <div class="journey-steps">
              <div class="journey-step" :class="{ active: currentStep >= 1 }">
                <i class="fas fa-key"></i>
                <div class="step-label">验证身份</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 2 }">
                <i class="fas fa-envelope"></i>
                <div class="step-label">邮箱验证</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 3 }">
                <i class="fas fa-lock-open"></i>
                <div class="step-label">重置密码</div>
              </div>
              <div class="journey-step" :class="{ active: currentStep >= 4 }">
                <i class="fas fa-check-circle"></i>
                <div class="step-label">完成</div>
              </div>
            </div>
          </div>

          <p class="inspiration-quote">"我们采用行业标准的安全协议，确保您的账户信息在重置过程中得到充分保护"</p>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="form-section">
        <div class="form-header">
          <h1 class="form-title">重置您的密码</h1>
          <p class="form-subtitle">请按照以下步骤重置您的账户密码</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
          <div class="step" :class="{ active: currentStep === 1 }">
            1
            <div class="step-label">验证身份</div>
          </div>
          <div class="step" :class="{ active: currentStep === 2 }">
            2
            <div class="step-label">输入验证码</div>
          </div>
          <div class="step" :class="{ active: currentStep === 3 }">
            3
            <div class="step-label">设置新密码</div>
          </div>
          <div class="step-line"></div>
        </div>

        <!-- 步骤1：验证身份 -->
        <div v-if="currentStep === 1" class="auth-form">
          <div class="form-group">
            <label class="form-label">注册邮箱地址</label>
            <input
              v-model="formData.email"
              type="email"
              class="form-input"
              :class="{ error: fieldErrors.email }"
              placeholder="请输入您的注册邮箱"
              @blur="validateField('email')"
              required
            />
            <div v-if="fieldErrors.email" class="form-error">{{ fieldErrors.email }}</div>
          </div>

          <button
            class="form-button"
            :disabled="loading || !canSendEmail"
            @click="sendVerificationCode"
          >
            <span v-if="loading">
              <i class="fas fa-spinner fa-spin"></i> 发送中...
            </span>
            <span v-else>发送验证邮件</span>
          </button>

          <div class="back-link" @click="goToLogin">
            <i class="fas fa-arrow-left"></i> 返回登录
          </div>
        </div>

        <!-- 步骤2：输入验证码 -->
        <div v-if="currentStep === 2" class="auth-form">
          <div class="form-group">
            <label class="form-label">验证码</label>
            <div class="verification-group">
              <input
                v-model="formData.verificationCode"
                type="text"
                class="form-input verification-input"
                :class="{ error: fieldErrors.verificationCode }"
                placeholder="请输入6位验证码"
                maxlength="6"
                @blur="validateField('verificationCode')"
                required
              />
              <button
                type="button"
                class="verification-button"
                :disabled="countdown > 0 || loading"
                @click="resendVerificationCode"
              >
                {{ countdown > 0 ? `重新发送(${countdown}s)` : '重新发送' }}
              </button>
            </div>
            <div v-if="fieldErrors.verificationCode" class="form-error">{{ fieldErrors.verificationCode }}</div>
          </div>

          <button
            class="form-button"
            :disabled="loading"
            @click="verifyCode"
          >
            <span v-if="loading">
              <i class="fas fa-spinner fa-spin"></i> 验证中...
            </span>
            <span v-else>验证并继续</span>
          </button>

          <div class="back-link" @click="goToStep1">
            <i class="fas fa-arrow-left"></i> 返回上一步
          </div>
        </div>

        <!-- 步骤3：设置新密码 -->
        <div v-if="currentStep === 3" class="auth-form">
          <div class="form-group">
            <label class="form-label">新密码</label>
            <div class="input-with-icon">
              <input
                v-model="formData.newPassword"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                :class="{ error: fieldErrors.newPassword }"
                placeholder="至少8位，包含大写字母、小写字母、数字和特殊符号"
                @blur="validateField('newPassword')"
                required
              />
              <button 
                type="button" 
                class="password-toggle"
                @click="togglePasswordVisibility"
                :aria-label="showPassword ? '隐藏密码' : '显示密码'"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div v-if="fieldErrors.newPassword" class="form-error">{{ fieldErrors.newPassword }}</div>
          </div>

          <div class="form-group">
            <label class="form-label">确认新密码</label>
            <div class="input-with-icon">
              <input
                v-model="formData.confirmPassword"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                :class="{ error: fieldErrors.confirmPassword }"
                placeholder="请再次输入密码"
                @blur="validateField('confirmPassword')"
                required
              />
              <button 
                type="button" 
                class="password-toggle"
                @click="togglePasswordVisibility"
                :aria-label="showPassword ? '隐藏密码' : '显示密码'"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div v-if="fieldErrors.confirmPassword" class="form-error">{{ fieldErrors.confirmPassword }}</div>
          </div>

          <button
            class="form-button"
            :disabled="loading"
            @click="resetPassword"
          >
            <span v-if="loading">
              <i class="fas fa-spinner fa-spin"></i> 重置中...
            </span>
            <span v-else>重置密码</span>
          </button>

          <div class="back-link" @click="goToStep2">
            <i class="fas fa-arrow-left"></i> 返回上一步
          </div>
        </div>

        <!-- 成功提示 -->
        <div v-if="currentStep === 4" class="auth-form">
          <div class="success-message">
            <div class="success-icon">
              <i class="fas fa-check-circle"></i>
            </div>
            <h2>密码已成功重置！</h2>
            <p>您的账户密码已更新，请使用新密码登录</p>
          </div>

          <button class="form-button" @click="goToLogin">
            立即登录
          </button>
        </div>

        <!-- 错误消息 -->
        <div v-if="error" class="error-message">
          <div class="error-icon">⚠️</div>
          <div class="error-content">
            <div class="error-title">操作失败</div>
            <div class="error-details">{{ error }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { authApi } from '@/api/auth'

const router = useRouter()

// 状态管理
const currentStep = ref(1)
const loading = ref(false)
const error = ref('')
const countdown = ref(0)
const showPassword = ref(false)

// 表单数据
const formData = reactive({
  email: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 字段错误
const fieldErrors = reactive({
  email: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const canSendEmail = computed(() => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)
})

// 字段验证
const validateField = (fieldName: string) => {
  clearFieldError(fieldName)
  
  switch (fieldName) {
    case 'email':
      if (!formData.email) {
        fieldErrors.email = '邮箱不能为空'
      } else if (!canSendEmail.value) {
        fieldErrors.email = '请输入有效的邮箱地址'
      }
      break
      
    case 'verificationCode':
      if (!formData.verificationCode) {
        fieldErrors.verificationCode = '验证码不能为空'
      } else if (!/^\d{6}$/.test(formData.verificationCode)) {
        fieldErrors.verificationCode = '请输入6位数字验证码'
      }
      break
      
    case 'newPassword':
      if (!formData.newPassword) {
        fieldErrors.newPassword = '新密码不能为空'
      } else if (formData.newPassword.length < 8) {
        fieldErrors.newPassword = '密码至少需要8个字符'
      } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(formData.newPassword)) {
        fieldErrors.newPassword = '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊符号'
      }
      break
      
    case 'confirmPassword':
      if (!formData.confirmPassword) {
        fieldErrors.confirmPassword = '请确认密码'
      } else if (formData.newPassword !== formData.confirmPassword) {
        fieldErrors.confirmPassword = '两次输入的密码不一致'
      }
      break
  }
}

const clearFieldError = (fieldName: string) => {
  fieldErrors[fieldName as keyof typeof fieldErrors] = ''
}

// 发送验证码
const sendVerificationCode = async () => {
  validateField('email')
  if (fieldErrors.email) return
  
  try {
    loading.value = true
    error.value = ''
    
    await authApi.sendVerificationCode({
      email: formData.email,
      type: 'password_reset'
    })
    
    currentStep.value = 2
    startCountdown()
    
  } catch (err: any) {
    console.error('发送验证码失败:', err)
    
    if (err.response?.status === 404) {
      fieldErrors.email = '该邮箱未注册'
    } else if (err.response?.status === 429) {
      fieldErrors.email = '发送过于频繁，请稍后再试'
    } else {
      error.value = err.message || '发送验证码失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}

// 重新发送验证码
const resendVerificationCode = async () => {
  try {
    loading.value = true
    error.value = ''
    
    await authApi.sendVerificationCode({
      email: formData.email,
      type: 'password_reset'
    })
    
    startCountdown()
    
  } catch (err: any) {
    console.error('重新发送验证码失败:', err)
    error.value = err.message || '重新发送验证码失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 验证验证码
const verifyCode = async () => {
  validateField('verificationCode')
  if (fieldErrors.verificationCode) return
  
  try {
    loading.value = true
    error.value = ''
    
    await authApi.verifyCode({
      email: formData.email,
      code: formData.verificationCode,
      type: 'password_reset'
    })
    
    currentStep.value = 3
    
  } catch (err: any) {
    console.error('验证码验证失败:', err)
    
    if (err.response?.status === 400) {
      fieldErrors.verificationCode = '验证码无效或已过期'
    } else {
      error.value = err.message || '验证码验证失败，请稍后重试'
    }
  } finally {
    loading.value = false
  }
}

// 重置密码
const resetPassword = async () => {
  validateField('newPassword')
  validateField('confirmPassword')
  
  if (fieldErrors.newPassword || fieldErrors.confirmPassword) return
  
  try {
    loading.value = true
    error.value = ''
    
    // 使用正确的API路径 - 只传递邮箱、验证码和新密码
    const response = await authApi.resetPasswordWithCode({
      email: formData.email,
      code: formData.verificationCode,
      password: formData.newPassword
    })
    
    if (response.success) {
      currentStep.value = 4
    } else {
      throw new Error(response.message || '重置密码失败')
    }
    
  } catch (err: any) {
    console.error('重置密码失败:', err)
    error.value = err.message || '重置密码失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 切换密码显示/隐藏
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 倒计时功能
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      countdown.value = 0
    }
  }, 1000)
}

// 导航函数
const goToLogin = () => {
  router.push('/auth')
}

const goToStep1 = () => {
  currentStep.value = 1
  clearAllErrors()
}

const goToStep2 = () => {
  currentStep.value = 2
  clearAllErrors()
}

const clearAllErrors = () => {
  Object.keys(fieldErrors).forEach(key => {
    fieldErrors[key as keyof typeof fieldErrors] = ''
  })
  error.value = ''
}
</script>

<style scoped>
.auth-page {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e293b;
  padding: 20px;
}

/* 背景装饰 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10;
  overflow: hidden;
  pointer-events: none;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05));
  animation: float 40s infinite ease-in-out;
}

.bg-shape:nth-child(1) { 
  width: 400px; 
  height: 400px; 
  left: -150px; 
  top: 10%; 
  animation-delay: 0s; 
}

.bg-shape:nth-child(2) { 
  width: 300px; 
  height: 300px; 
  right: -100px; 
  top: 50%; 
  animation-delay: 15s; 
}

.bg-shape:nth-child(3) { 
  width: 200px; 
  height: 200px; 
  left: 50%; 
  top: 5%; 
  animation-delay: 30s; 
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-30px) rotate(90deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
  75% { transform: translateY(-25px) rotate(270deg); }
}

/* 主容器 */
.auth-container {
  display: flex;
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(102, 126, 234, 0.15);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 680px;
  height: auto;
  margin: 0 auto;
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
  overflow: hidden;
}

.brand-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 40%);
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 10px 0;
}

.brand-logo {
  font-size: 48px;
  font-weight: 800;
  letter-spacing: -1px;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #ffffff, #fbbf24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-tagline {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  font-weight: 500;
  color: #e0e7ff;
}

/* 安全图标 */
.security-icon {
  font-size: 80px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 30px;
}

/* 成功之路展示 */
.journey-container {
  position: relative;
  height: 180px;
  margin: 0 auto 30px;
  width: 90%;
}

.journey-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%);
}

.journey-progress {
  position: absolute;
  top: 50%;
  left: 0;
  width: 0;
  height: 6px;
  background: white;
  transform: translateY(-50%);
  border-radius: 3px;
  animation: progress 3s infinite ease-in-out;
}

.journey-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.journey-step {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.3s;
  position: relative;
  border: 2px solid transparent;
}

.journey-step.active {
  background: white;
  color: #667eea;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.step-label {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: white;
  opacity: 0.8;
  white-space: nowrap;
}

@keyframes progress {
  0% {
    width: 0;
  }
  30% {
    width: 30%;
  }
  70% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* 激励语句 */
.inspiration-quote {
  margin-top: 30px;
  font-size: 16px;
  font-style: italic;
  color: #e0e7ff;
  position: relative;
  padding: 0 20px;
  line-height: 1.6;
}

.inspiration-quote::before,
.inspiration-quote::after {
  content: '"';
  position: absolute;
  font-size: 40px;
  color: rgba(255, 255, 255, 0.2);
  font-family: Georgia, serif;
}

.inspiration-quote::before {
  top: -20px;
  left: 0;
}

.inspiration-quote::after {
  bottom: -40px;
  right: 0;
}

/* 右侧表单区域 */
.form-section {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow-y: auto;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 12px;
  color: #1e293b;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  color: #64748b;
  font-size: 16px;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
}

.step-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #e5e7eb;
  transform: translateY(-50%);
  z-index: 1;
}

.step {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
  color: #94a3b8;
  transition: all 0.3s;
}

.step.active {
  background: #8b5cf6;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 5px 15px rgba(139, 92, 246, 0.3);
}

.step-label {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
  font-weight: 500;
  width: max-content;
}

.step.active .step-label {
  color: #8b5cf6;
  font-weight: 600;
}

/* 表单样式 */
.auth-form {
  display: block;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
}

/* 验证码输入组 */
.verification-group {
  display: flex;
  gap: 10px;
}

.verification-input {
  flex: 1;
}

.verification-button {
  width: 140px;
  padding: 14px 10px;
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.verification-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
}

.verification-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  opacity: 0.7;
}

.form-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 6px;
  display: block;
}

.form-button {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 15px;
  position: relative;
  overflow: hidden;
}

.form-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.form-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.form-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 返回链接 */
.back-link {
  display: flex;
  align-items: center;
  color: #8b5cf6;
  font-weight: 500;
  margin-top: 15px;
  cursor: pointer;
  font-size: 15px;
  justify-content: center;
}

.back-link i {
  margin-right: 8px;
  transition: transform 0.2s;
}

.back-link:hover i {
  transform: translateX(-4px);
}

/* 成功消息 */
.success-message {
  text-align: center;
  padding: 30px 0;
}

.success-icon {
  font-size: 60px;
  color: #10b981;
  margin-bottom: 20px;
}

/* 错误消息 */
.error-message {
  color: #ef4444;
  font-size: 14px;
  padding: 16px;
  background: #fef2f2;
  border-radius: 8px;
  border: 1px solid #fecaca;
  margin-bottom: 16px;
  animation: shake 0.5s;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.error-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-content {
  flex: 1;
}

.error-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #dc2626;
}

.error-details {
  font-size: 13px;
  color: #b91c1c;
  line-height: 1.4;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 响应式设计 */
@media (max-width: 900px) {
  .auth-container {
    flex-direction: column;
    min-height: auto;
    max-width: 600px;
  }

  .brand-section {
    padding: 30px;
  }

  .journey-container {
    height: 150px;
    margin-bottom: 20px;
  }

  .journey-step {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .step-label {
    font-size: 10px;
    bottom: -25px;
  }

  .form-section {
    padding: 30px;
  }
  
  .verification-group {
    flex-direction: column;
  }
  
  .verification-button {
    width: 100%;
    margin-top: 8px;
  }
}

@media (max-width: 500px) {
  .auth-container {
    border-radius: 16px;
  }

  .brand-section {
    padding: 20px;
  }

  .brand-logo {
    font-size: 36px;
  }

  .brand-tagline {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .journey-container {
    height: 120px;
  }

  .journey-step {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .step-label {
    font-size: 9px;
    bottom: -20px;
  }

  .inspiration-quote {
    font-size: 14px;
    margin-top: 15px;
  }

  .form-section {
    padding: 20px;
  }

  .form-title {
    font-size: 24px;
  }

  .form-subtitle {
    font-size: 14px;
  }

  .verification-group {
    flex-direction: column;
  }

  .verification-button {
    width: 100%;
    margin-top: 8px;
  }

  .step {
    width: 30px;
    height: 30px;
    font-size: 14px;
  }

  .step-label {
    font-size: 10px;
    top: 40px;
  }
}

/* 密码显示/隐藏功能 */
.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon .form-input {
  padding-right: 50px;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.password-toggle:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

.password-toggle i {
  font-size: 16px;
}
</style>