<template>
  <div class="advice-page">
    <Navbar />

    <!-- DAO治理中心主体 -->
    <main class="dao-section">
      <div class="container">
        <!-- 页面头部 -->
        <div class="section-header">
          <h1 class="section-title">您的决策，塑造平台未来</h1>
          <p class="section-subtitle">
            作为小概率成员，您不仅使用平台，更是平台的共同所有者。只要成为小概率会员，都有权决定平台的商业模式、定价策略、功能开发方向，真正实现"我们的平台，我们做主"
          </p>
          <!-- <div class="stats-row">
            <div class="stat-item">
              <span class="stat-number">{{ stats.totalProposals }}</span>
              <span class="stat-label">总提案数</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ stats.activeProposals }}</span>
              <span class="stat-label">活跃提案</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ stats.totalVotes }}</span>
              <span class="stat-label">总投票数</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">{{ stats.participantCount }}</span>
              <span class="stat-label">参与人数</span>
            </div>
          </div> -->
        </div>

        <!-- 过滤和排序 -->
        <div class="filter-section">
          <div class="filter-options">
            <div 
              v-for="filter in filterOptions" 
              :key="filter.id"
              class="filter-option"
              :class="{ active: activeFilter === filter.id }"
              @click="setActiveFilter(filter.id)"
            >
              {{ filter.name }}
            </div>
          </div>
          <select v-model="sortBy" class="sort-select">
            <option value="latest">最新提案</option>
            <option value="popular">最受欢迎</option>
            <option value="urgent">最紧急</option>
          </select>
        </div>

        <!-- 治理提案列表 -->
        <div class="proposals-grid">
          <div 
            v-for="proposal in filteredProposals" 
            :key="proposal.id"
            class="proposal-card"
          >
            <div class="proposal-header">
              <span class="proposal-id">{{ proposal.id }}</span>
              <span 
                class="proposal-status"
                :class="`status-${proposal.status}`"
              >
                {{ proposal.statusText }}
              </span>
            </div>
            <div class="proposal-body">
              <h3 class="proposal-title">{{ proposal.title }}</h3>
              <div class="proposal-meta">
                <span>分类: {{ proposal.categoryText }}</span>
                <span v-if="proposal.deadline !== '无限期'">剩余: {{ proposal.remainingDays }}天</span>
                <span v-else>无限期</span>
              </div>
              <p class="proposal-summary">{{ proposal.summary }}</p>
              
              <div class="voting-stats">
                <div class="vote-count">
                  <span class="support-count">
                    <i class="fas fa-thumbs-up"></i>
                    支持 {{ proposal.vote_stats.support }}
                  </span>
                  <span class="oppose-count">
                    <i class="fas fa-thumbs-down"></i>
                    反对 {{ proposal.vote_stats.oppose }}
                  </span>
            
                </div>
              </div>
            </div>
            <div class="proposal-footer">
              <div class="voting-actions" v-if="proposal.status === '投票中'">
                <button 
                  class="vote-btn support-btn"
                  :class="{ active: proposal.userVote === 'support' }"
                  @click="vote(proposal.id, 'support')"
                  :disabled="proposal.userVote"
                >
                  <i class="fas fa-thumbs-up"></i>
                  支持
                </button>
                <button 
                  class="vote-btn oppose-btn"
                  :class="{ active: proposal.userVote === 'oppose' }"
                  @click="vote(proposal.id, 'oppose')"
                  :disabled="proposal.userVote"
                >
                  <i class="fas fa-thumbs-down"></i>
                  反对
                </button>
              </div>
              <div class="vote-status" v-else-if="proposal.userVote">
                <span class="user-vote-indicator" :class="proposal.userVote">
                  <i :class="proposal.userVote === 'support' ? 'fas fa-thumbs-up' : 'fas fa-thumbs-down'"></i>
                  您已投{{ proposal.userVote === 'support' ? '支持' : '反对' }}票
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 建议提交面板 -->
        <div class="suggestion-panel">
          <div class="panel-header">
            <div>
              <h2 class="panel-title">发起治理提案</h2>
              <p class="panel-subtitle">
                作为DevMatch DAO成员，您有权发起治理提案，推动平台发展。
                社区将对提案进行公开讨论和投票决策
              </p>
            </div>
            <!-- <div>
              <span class="stats">
                <i class="fas fa-lightbulb"></i> 
                本周已收集 <strong>{{ weeklyStats.suggestions }}</strong> 条建议
              </span>
            </div> -->
          </div>

          <form @submit.prevent="submitProposal">
            <div class="form-group">
              <label class="form-label">提案标题 *</label>
              <input 
                type="text" 
                v-model="form.title"
                class="form-control" 
                placeholder="简洁明确地描述您的提案内容"
                maxlength="60"
              >
              <div class="character-count">{{ form.title.length }}/60</div>
            </div>

            <div class="form-group">
              <label class="form-label">提案分类 *</label>
              <select v-model="form.category" class="form-control">
                <option value="">选择分类</option>
                <option value="feature">平台功能改进</option>
                <option value="community">社区治理</option>
                <option value="business">商业模式</option>
                <option value="technology">技术升级</option>
                <option value="resource">资源分配</option>
                <option value="other">其他提案</option>
              </select>
            </div>

            <div class="form-group">
              <label class="form-label">提案详情 *</label>
              <textarea 
                v-model="form.description"
                class="form-control" 
                placeholder="详细描述提案内容：背景分析、具体方案、实施计划、预期效果等"
                maxlength="1500"
              ></textarea>
              <div class="character-count">{{ form.description.length }}/1500</div>
            </div>

            <div class="form-group">
              <label class="form-label">支持论据</label>
              <textarea 
                v-model="form.evidence"
                class="form-control" 
                placeholder="提供支持提案的数据、案例或理论依据，提高提案通过率"
                maxlength="1000"
              ></textarea>
              <div class="character-count">{{ form.evidence.length }}/1000</div>
            </div>

            <div class="form-group">
              <label class="form-label">影响评估</label>
              <div class="filter-options">
                <div 
                  v-for="impact in impactOptions" 
                  :key="impact.id"
                  class="filter-option"
                  :class="{ active: form.impact === impact.id }"
                  @click="form.impact = impact.id"
                >
                  {{ impact.name }}
                </div>
              </div>
            </div>

            <div class="btn-group">
              <button type="button" class="btn btn-outline" @click="resetForm">重置</button>
              <button type="submit" class="btn btn-primary">发起提案</button>
            </div>
          </form>
        </div>

        <!-- 分类指南 -->
        <div class="categories-guide">
          <h3 class="categories-title">DAO治理分类指南</h3>
          <p class="panel-subtitle">
            了解不同类型的治理提案，选择合适的分类以确保提案得到准确的评估和讨论
          </p>

          <div class="category-list">
            <div 
              v-for="category in categoryGuides" 
              :key="category.id"
              class="category-card"
            >
              <div class="category-icon">
                <i :class="category.icon"></i>
              </div>
              <h4 class="category-name">{{ category.name }}</h4>
              <p class="category-desc">{{ category.description }}</p>
              <p class="category-examples">例如：{{ category.examples }}</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
// @ts-ignore
import Navbar from '../components/layout/Navbar.vue'
import { useUserStore } from '../stores/user'
import { http } from '../utils/request'

// 类型定义
interface Proposal {
  id: string
  title: string
  category: string
  content: string
  evidence?: string
  impact?: string
  status: string
  creator_id: number
  created_at: string
  updated_at: string
  deadline?: string
  vote_stats: {
    support: number
    oppose: number
    abstain: number
    total: number
  }
  comments_count: number
  views_count: number
}

interface Stats {
  total_proposals: number
  active_proposals: number
  approved_proposals: number
  total_dao_members: number
  total_votes: number
  this_week_proposals: number
}

// 响应式数据
const activeFilter = ref('all')
const sortBy = ref('latest')
const loading = ref(false)
const userStore = useUserStore()

// 用户投票状态
const userVotes = ref<Record<string, 'support' | 'oppose'>>({})

// 提案数据
const proposals = ref<Proposal[]>([])

// 表单数据
const form = ref({
  title: '',
  category: '',
  description: '',
  evidence: '',
  impact: ''
})

// 统计数据
const stats = ref({
  totalProposals: 0,
  activeProposals: 0,
  totalVotes: 0,
  participantCount: 0
})

const weeklyStats = ref({
  suggestions: 0
})

// 过滤选项 - 只显示前端可见的3个状态
const filterOptions = ref([
  { id: 'all', name: '全部提案' },
  { id: '投票中', name: '投票中' },
  { id: '已通过', name: '已通过' },
  { id: '未通过', name: '未通过' }
])

// 影响评估选项
const impactOptions = ref([
  { id: 'high_value_low_difficulty', name: '高价值低实施难度' },
  { id: 'high_value_high_difficulty', name: '高价值高实施难度' },
  { id: 'medium_value_low_difficulty', name: '中价值低实施难度' },
  { id: 'other', name: '其他影响' }
])


// 分类指南
const categoryGuides = ref([
  {
    id: 'feature',
    name: '平台功能改进',
    description: '对现有功能的改进建议、新功能提议、界面优化等',
    examples: '添加AI文档生成工具、重构项目管理面板',
    icon: 'fas fa-wrench'
  },
  {
    id: 'community',
    name: '社区治理',
    description: 'DAO治理规则、投票机制、权限管理、激励体系、社区活动等',
    examples: '投票权重调整、治理流程优化、社区奖励机制',
    icon: 'fas fa-users'
  },
  {
    id: 'business',
    name: '商业模式',
    description: '收入模式、定价策略、合作伙伴、平台商业化路径等',
    examples: '免费增值模式优化、API商业化策略',
    icon: 'fas fa-chart-line'
  },
  {
    id: 'technology',
    name: '技术升级',
    description: '技术架构升级、安全增强、性能优化、算法改进等',
    examples: '引入新型匹配算法、实施零信任安全框架',
    icon: 'fas fa-microchip'
  }
])

// 分类映射
const categoryMap: Record<string, string> = {
  'feature': '平台功能改进',
  'community': '社区治理',
  'business': '商业模式',
  'technology': '技术升级',
  'resource': '资源分配',
  'other': '其他提案'
}

// 状态映射 - 只显示前端可见的3个状态
const statusMap: Record<string, { text: string; class: string }> = {
  '投票中': { text: '投票中', class: 'voting' },
  '已通过': { text: '已通过', class: 'approved' },
  '未通过': { text: '未通过', class: 'rejected' }
}

// 计算属性  
const filteredProposals = computed(() => {
  let filtered = proposals.value
  
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(p => p.status === activeFilter.value)
  }
  
  // 排序
  if (sortBy.value === 'popular') {
    filtered = [...filtered].sort((a, b) => b.vote_stats.total - a.vote_stats.total)
  } else if (sortBy.value === 'urgent') {
    filtered = [...filtered].sort((a, b) => {
      const aDeadline = new Date(a.deadline || '9999-12-31').getTime()
      const bDeadline = new Date(b.deadline || '9999-12-31').getTime()
      return aDeadline - bDeadline
    })
  } else {
    filtered = [...filtered].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  }
  
  return filtered.map(proposal => {
    // 计算剩余天数
    const now = new Date()
    const deadline = proposal.deadline ? new Date(proposal.deadline) : null
    const remainingDays = deadline ? Math.max(0, Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))) : 0
    
    return {
      ...proposal,
      categoryText: categoryMap[proposal.category] || proposal.category,
      statusText: statusMap[proposal.status]?.text || proposal.status,
      statusClass: statusMap[proposal.status]?.class || proposal.status,
      summary: proposal.content.substring(0, 150) + '...',
      remainingDays: remainingDays,
      deadline: proposal.deadline ? (remainingDays > 0 ? `${remainingDays}天后截止` : '已截止') : '无限期',
      userVote: userVotes.value[proposal.id] || null // 用户的投票状态
    }
  })
})

// API调用方法
const loadStats = async () => {
  try {
    const data = await http.get('/dao/stats')
    stats.value = {
      totalProposals: data.total_proposals || 0,
      activeProposals: data.active_proposals || 0,
      totalVotes: data.total_votes || 0,
      participantCount: data.total_dao_members || 0
    }
    weeklyStats.value.suggestions = data.this_week_proposals || 0
  } catch (error) {
    console.error('加载统计数据失败:', error)
    // 使用默认值
    stats.value = {
      totalProposals: 0,
      activeProposals: 0,
      totalVotes: 0,
      participantCount: 0
    }
  }
}

const loadProposals = async () => {
  try {
    loading.value = true
    const params: any = {}
    if (activeFilter.value !== 'all') {
      params.status = activeFilter.value
    }
    
    const data = await http.get('/dao/proposals', { params })
    proposals.value = data.data || data || []
  } catch (error) {
    console.error('加载提案失败:', error)
    proposals.value = []
  } finally {
    loading.value = false
  }
}

// 方法
const setActiveFilter = async (filterId: string) => {
  activeFilter.value = filterId
  await loadProposals()
}

// 投票方法
const vote = async (proposalId: string, voteType: 'support' | 'oppose') => {
  try {
    // 检查登录状态
    if (!userStore.isAuthenticated) {
      if (confirm('您需要登录才能投票。是否立即登录？')) {
        window.location.href = '/auth?mode=login&redirect=' + encodeURIComponent(window.location.pathname)
      }
      return
    }

    // 检查DAO成员身份
    const daoStatus = await checkDaoMembership()
    if (!daoStatus.isDaoMember) {
      if (confirm('只有DAO成员才能投票。是否立即成为DAO成员？')) {
        // 跳转到DAO成员页面
        window.location.href = '/dao'
      }
      return
    }

    // 检查是否已投票
    if (userVotes.value[proposalId]) {
      alert('您已经投过票了，无法修改')
      return
    }

    // 二次确认
    const voteText = voteType === 'support' ? '支持' : '反对'
    if (!confirm(`确认投${voteText}票吗？一经投票无法修改。`)) {
      return
    }

    // 发送投票请求
    loading.value = true
    const result = await http.post(`/dao/proposals/${proposalId}/vote`, {
      voteType: voteType
    })

    if (result.success) {
      // 更新本地投票状态
      userVotes.value[proposalId] = voteType
      
      // 重新加载提案列表
      await loadProposals()
      
      alert(`投票成功！您已投${voteText}票`)
    } else {
      alert(result.message || '投票失败')
    }
  } catch (error: any) {
    console.error('投票失败:', error)
    alert(error.response?.data?.message || '投票失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 检查DAO成员身份
const checkDaoMembership = async () => {
  try {
    const result = await http.get('/dao/membership-status')
    return result
  } catch (error) {
    console.error('检查DAO成员身份失败:', error)
    return { isDaoMember: false }
  }
}

// 加载用户投票状态
const loadUserVotes = async () => {
  try {
    if (!userStore.isAuthenticated) return
    
    const result = await http.get('/dao/user-votes')
    if (result.success) {
      userVotes.value = result.data || {}
    }
  } catch (error) {
    console.error('加载用户投票状态失败:', error)
  }
}

const submitProposal = async () => {
  // 验证必填字段
  if (!form.value.title || !form.value.category || !form.value.description) {
    alert('请填写必填字段！')
    return
  }

  // 检查登录状态
  if (!userStore.isAuthenticated) {
    if (confirm('您需要登录才能提交提案。是否立即登录？')) {
      // 保存表单数据到localStorage
      localStorage.setItem('advice-form-data', JSON.stringify(form.value))
      // 跳转到登录页面，登录后会自动返回当前页面
      window.location.href = '/auth?mode=login&redirect=' + encodeURIComponent(window.location.pathname)
    }
    return
  }
  
  try {
    loading.value = true
    await http.post('/dao/proposals', {
      title: form.value.title,
      category: form.value.category,
      content: form.value.description,
      evidence: form.value.evidence,
      impact: form.value.impact
    })
    
    alert('提案提交成功！等待管理员审核。')
    resetForm()
    // 清除保存的表单数据
    localStorage.removeItem('advice-form-data')
    await loadProposals()
    await loadStats()
  } catch (error: any) {
    alert('提交失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  form.value = {
    title: '',
    category: '',
    description: '',
    evidence: '',
    impact: ''
  }
}

// 初始化数据
onMounted(async () => {
  // 恢复保存的表单数据
  const savedFormData = localStorage.getItem('advice-form-data')
  if (savedFormData) {
    try {
      const parsedData = JSON.parse(savedFormData)
      form.value = { ...form.value, ...parsedData }
      console.log('恢复表单数据成功')
    } catch (error) {
      console.error('恢复表单数据失败:', error)
    }
  }
  
  await Promise.all([
    loadStats(),
    loadProposals(),
    loadUserVotes()
  ])
})
</script>

<style scoped>
/* CSS变量 */
.advice-page {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --surface-bg: #f7f9fc;
  --card-bg: #ffffff;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --text-muted: #a0aec0;
  --border-color: #e2e8f0;
  --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-neumorphic: 8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff;
  --border-radius: 20px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  --primary: #667eea;
  --secondary: #764ba2;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --dark: #2d3748;
  --light: #f8fafc;
  --gray: #94a3b8;
  --border: #e2e8f0;
}

.advice-page {
  background: var(--surface-bg);
  color: var(--text-primary);
  line-height: 1.6;
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 主体内容 */
.dao-section {
  padding: 120px 0 40px;
}

.section-header {
  margin-bottom: 50px;
  text-align: center;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--text-primary);
  font-weight: 800;
  letter-spacing: -1px;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.1rem;
  color: var(--text-secondary);
  max-width: 700px;
  margin: 0 auto 40px;
  line-height: 1.7;
}

.stats-row {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.2rem;
  font-weight: 800;
  color: var(--primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 过滤区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  background: var(--card-bg);
  padding: 20px 30px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-options {
  display: flex;
  gap: 10px;
}

.filter-option {
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: var(--transition);
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.7);
}

.filter-option:hover,
.filter-option.active {
  background: var(--primary-gradient);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.sort-select {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  cursor: pointer;
}

/* 提案网格 */
.proposals-grid {
  display: grid;
  gap: 30px;
  margin-bottom: 60px;
}

.proposal-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 30px;
  transition: var(--transition);
}

.proposal-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

.proposal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.proposal-id {
  font-family: 'Courier New', monospace;
  background: rgba(102, 126, 234, 0.1);
  color: var(--primary);
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.85rem;
}

.proposal-status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.status-active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
}

.status-voting {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning);
}

.status-draft {
  background: rgba(148, 163, 184, 0.1);
  color: var(--gray);
}

.status-archived {
  background: rgba(148, 163, 184, 0.1);
  color: var(--gray);
}

.status-executing {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
}

.proposal-body {
  margin-bottom: 20px;
}

.proposal-title {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-primary);
  line-height: 1.4;
}

.proposal-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.proposal-summary {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 20px;
}

.progress-container {
  margin-top: 20px;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.progress-bar {
  height: 8px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-gradient);
  transition: var(--transition);
}

.proposal-footer {
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--text-secondary);
}

.stat i {
  color: var(--gray);
}

/* 建议提交面板 */
.suggestion-panel {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-neumorphic);
  padding: 40px;
  margin-top: 50px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.panel-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-primary);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel-subtitle {
  color: var(--text-secondary);
  max-width: 600px;
  margin-top: 12px;
  line-height: 1.6;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark);
}

.form-control {
  width: 100%;
  padding: 14px 18px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 1rem;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.7);
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2), 4px 4px 8px #d1d9e6, -4px -4px 8px #ffffff;
  background: rgba(255, 255, 255, 0.9);
}

textarea.form-control {
  min-height: 150px;
  resize: vertical;
}

.character-count {
  font-size: 0.85rem;
  color: var(--gray);
  text-align: right;
  margin-top: 5px;
}

.btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  border: none;
  transition: var(--transition);
  font-size: 0.9rem;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
}

/* 分类指南 */
.categories-guide {
  margin-top: 40px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 35px;
  box-shadow: var(--shadow-neumorphic);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.categories-title {
  font-size: 1.4rem;
  margin-bottom: 20px;
  color: var(--text-primary);
  font-weight: 700;
}

.category-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.category-card {
  border: 1px solid var(--border-color);
  border-radius: 15px;
  padding: 24px;
  transition: var(--transition);
  background: rgba(255, 255, 255, 0.5);
}

.category-card:hover {
  border-color: var(--primary);
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
}

.category-icon {
  font-size: 1.8rem;
  color: var(--primary);
  margin-bottom: 15px;
}

.category-name {
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.category-desc {
  font-size: 0.9rem;
  color: var(--gray);
  line-height: 1.5;
}

.category-examples {
  margin-top: 10px;
  font-size: 0.85rem;
  color: var(--gray);
  font-style: italic;
}

/* 投票统计样式 */
.voting-stats {
  margin: 20px 0;
}

.vote-count {
  display: flex;
  gap: 20px;
  align-items: center;
  font-size: 0.9rem;
}

.support-count {
  color: var(--success);
  font-weight: 600;
}

.oppose-count {
  color: var(--danger);
  font-weight: 600;
}

.total-count {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 投票按钮样式 */
.voting-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.vote-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: 2px solid;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: var(--transition);
  background: transparent;
  min-width: 100px;
  justify-content: center;
}

.vote-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.support-btn {
  border-color: var(--success);
  color: var(--success);
}

.support-btn:hover:not(:disabled) {
  background: var(--success);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.support-btn.active {
  background: var(--success);
  color: white;
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.oppose-btn {
  border-color: var(--danger);
  color: var(--danger);
}

.oppose-btn:hover:not(:disabled) {
  background: var(--danger);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.oppose-btn.active {
  background: var(--danger);
  color: white;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

/* 投票状态显示 */
.vote-status {
  text-align: center;
  padding: 12px;
}

.user-vote-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.85rem;
}

.user-vote-indicator.support {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.user-vote-indicator.oppose {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* 提案状态样式优化 */
.proposal-status.voting {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
  animation: pulse-voting 2s infinite;
  padding: 8px 16px;
  font-size: 0.8rem;
}

.proposal-status.approved {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
  font-size: 0.9rem;
  padding: 10px 20px;
  font-weight: 700;
}

.proposal-status.rejected {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
  font-size: 0.9rem;
  padding: 10px 20px;
  font-weight: 700;
}

@keyframes pulse-voting {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(251, 191, 36, 0.6);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .filter-section {
    flex-direction: column;
    gap: 20px;
  }
  
  .filter-options {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .panel-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .category-list {
    grid-template-columns: 1fr;
  }
  
  .voting-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .vote-btn {
    width: 100%;
  }
}
</style> 