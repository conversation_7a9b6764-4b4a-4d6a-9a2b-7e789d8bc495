<template>
  <div class="admin-review-page">
    <Navbar />
    <div class="admin-review-container">
      <div class="page-header">
        <h1 class="page-title">项目审核管理</h1>
        <div class="stats-bar">
          <div class="stat-item">
            <span class="stat-label">待审核</span>
            <span class="stat-value">{{ pendingCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">今日审核</span>
            <span class="stat-value">{{ todayReviewCount }}</span>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <div class="filter-group">
          <select v-model="filterStatus" class="filter-select">
            <option value="">全部状态</option>
            <option value="pending">待审核</option>
            <option value="approved">已通过</option>
            <option value="rejected">已拒绝</option>
          </select>
          <select v-model="filterCategory" class="filter-select">
            <option value="">全部分类</option>
            <option value="mobile">移动应用</option>
            <option value="web">Web开发</option>
            <option value="ai">AI/机器学习</option>
            <option value="blockchain">区块链</option>
            <option value="game">游戏开发</option>
            <option value="social">社交网络</option>
            <option value="ecommerce">电商平台</option>
            <option value="fintech">金融科技</option>
            <option value="education">教育培训</option>
            <option value="health">健康生活</option>
            <option value="enterprise">企业软件</option>
            <option value="data">数据分析</option>
          </select>
        </div>
        <div class="search-group">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索项目名称或创建者..."
            class="search-input"
          />
          <button @click="loadProjects" class="search-btn">
            🔍 搜索
          </button>
        </div>
      </div>

      <!-- 项目列表 -->
      <div class="projects-section">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>

        <div v-else-if="projects.length === 0" class="empty-state">
          <div class="empty-icon">📋</div>
          <h3>暂无项目</h3>
          <p>当前筛选条件下没有找到项目</p>
        </div>

        <div v-else class="projects-grid">
          <div
            v-for="project in projects"
            :key="project.id"
            class="project-card"
            :class="{ 'pending': project.reviewStatus === 'pending' }"
          >
            <div class="project-header">
              <div class="project-info">
                <h3 class="project-title">{{ project.title }}</h3>
                <p class="project-summary">{{ project.summary }}</p>
                <div class="project-meta">
                  <span class="meta-tag">{{ getCategoryLabel(project.category) }}</span>
                  <span class="meta-tag">团队{{ getTeamSize(project.teamInfo) }}人</span>
                  <span class="meta-tag">{{ project.durationMonths }}个月</span>
                  <span class="meta-tag">{{ getWorkArrangementLabel(project.workArrangement) }}</span>
                </div>
              </div>
              <div class="project-status">
                <span 
                  class="status-badge" 
                  :class="getStatusClass(project.reviewStatus)"
                >
                  {{ getStatusLabel(project.reviewStatus) }}
                </span>
              </div>
            </div>

            <div class="project-content">
              <div class="content-section">
                <h4>项目描述</h4>
                <p class="description">{{ project.description }}</p>
              </div>

              <div class="content-section">
                <h4>发起人信息</h4>
                <div class="creator-info">
                  <img
                    :src="project.creator?.avatarUrl || '/default-avatar.png'"
                    :alt="project.creator?.username || '未知用户'"
                    class="creator-avatar"
                  />
                  <div class="creator-details">
                    <p class="creator-name">{{ project.creator?.username || '未知用户' }}</p>
                    <p class="creator-email">{{ project.creator?.email || '邮箱未提供' }}</p>
                  </div>
                </div>
              </div>

              <div class="content-section">
                <h4>提交时间</h4>
                <p>{{ formatDate(project.submittedAt) }}</p>
              </div>

              <div v-if="project.reviewMessage" class="content-section">
                <h4>审核意见</h4>
                <p class="review-message">{{ project.reviewMessage }}</p>
              </div>
            </div>

            <div class="project-actions">
              <button
                @click="viewProject(project)"
                class="btn btn-outline"
              >
                查看详情
              </button>
              <button
                v-if="project.reviewStatus === 'pending'"
                @click="approveProject(project)"
                class="btn btn-success"
                :disabled="reviewing"
              >
                {{ reviewing ? '处理中...' : '通过' }}
              </button>
              <button
                v-if="project.reviewStatus === 'pending'"
                @click="rejectProject(project)"
                class="btn btn-danger"
                :disabled="reviewing"
              >
                {{ reviewing ? '处理中...' : '拒绝' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <div v-if="showReviewModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ reviewAction === 'approve' ? '审核通过' : '审核拒绝' }}</h3>
          <button @click="closeModal" class="close-btn">×</button>
        </div>
        <div class="modal-body">
          <div class="project-info">
            <h4>{{ currentProject?.title }}</h4>
            <p>{{ currentProject?.summary }}</p>
          </div>
          <div class="form-group">
            <label class="form-label">
              审核意见 {{ reviewAction === 'reject' ? '(必填)' : '(可选)' }}
            </label>
            <textarea
              v-model="reviewMessage"
              class="form-textarea"
              placeholder="请输入审核意见..."
              rows="4"
              :required="reviewAction === 'reject'"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeModal" class="btn btn-secondary">
            取消
          </button>
          <button
            @click="confirmReview"
            class="btn"
            :class="reviewAction === 'approve' ? 'btn-success' : 'btn-danger'"
            :disabled="reviewing || (reviewAction === 'reject' && !reviewMessage.trim())"
          >
            {{ reviewing ? '处理中...' : '确认' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Navbar from '@/components/layout/Navbar.vue'
import { useUserStore } from '@/stores/user'
import type { ProjectDetails } from '@/api/project'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const projects = ref<ProjectDetails[]>([])
const loading = ref(false)
const reviewing = ref(false)
const searchQuery = ref('')
const filterStatus = ref('')
const filterCategory = ref('')

// 审核弹窗
const showReviewModal = ref(false)
const reviewAction = ref<'approve' | 'reject'>('approve')
const reviewMessage = ref('')
const currentProject = ref<ProjectDetails | null>(null)

// 统计数据
const pendingCount = computed(() => 
  projects.value.filter(p => p.reviewStatus === 'pending').length
)

const todayReviewCount = computed(() => {
  const today = new Date().toDateString()
  return projects.value.filter(p => 
    p.reviewedAt && new Date(p.reviewedAt).toDateString() === today
  ).length
})

// 权限检查
onMounted(async () => {
  if (!userStore.isAuthenticated || userStore.user?.role !== 'admin') {
    alert('权限不足，请联系管理员')
    router.push('/login')
    return
  }
  
  await loadProjects()
})

// 加载项目列表
const loadProjects = async () => {
  loading.value = true
  try {
    const { getPendingProjects } = await import('@/api/project')
    const response = await getPendingProjects()
    projects.value = response.data.projects
  } catch (error) {
    console.error('加载项目失败:', error)
    alert('加载项目失败，请刷新重试')
  } finally {
    loading.value = false
  }
}

// 查看项目详情
const viewProject = (project: ProjectDetails) => {
  router.push(`/projects/${project.id}`)
}

// 审核通过
const approveProject = (project: ProjectDetails) => {
  currentProject.value = project
  reviewAction.value = 'approve'
  reviewMessage.value = ''
  showReviewModal.value = true
}

// 审核拒绝
const rejectProject = (project: ProjectDetails) => {
  currentProject.value = project
  reviewAction.value = 'reject'
  reviewMessage.value = ''
  showReviewModal.value = true
}

// 确认审核
const confirmReview = async () => {
  if (!currentProject.value) return
  
  if (reviewAction.value === 'reject' && !reviewMessage.value.trim()) {
    alert('请输入拒绝理由')
    return
  }
  
  reviewing.value = true
  try {
    const { reviewProject } = await import('@/api/project')
    await reviewProject(currentProject.value.id, {
      reviewStatus: reviewAction.value === 'approve' ? 'approved' : 'rejected',
      reviewMessage: reviewMessage.value.trim() || undefined
    })
    
    alert(`项目${reviewAction.value === 'approve' ? '审核通过' : '审核拒绝'}成功`)
    closeModal()
    await loadProjects()
  } catch (error) {
    console.error('审核失败:', error)
    alert('审核失败，请重试')
  } finally {
    reviewing.value = false
  }
}

// 关闭弹窗
const closeModal = () => {
  showReviewModal.value = false
  currentProject.value = null
  reviewMessage.value = ''
}

// 工具函数
const getCategoryLabel = (category: string) => {
  const labels: Record<string, string> = {
    mobile: '移动应用',
    web: 'Web开发',
    ai: 'AI/机器学习',
    blockchain: '区块链',
    game: '游戏开发',
    social: '社交网络',
    ecommerce: '电商平台',
    fintech: '金融科技',
    education: '教育培训',
    health: '健康生活',
    enterprise: '企业软件',
    data: '数据分析'
  }
  return labels[category] || category
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return labels[status] || status
}

const getStatusClass = (status: string) => {
  const classes: Record<string, string> = {
    pending: 'status-pending',
    approved: 'status-approved',
    rejected: 'status-rejected'
  }
  return classes[status] || ''
}

// 计算团队规模
const getTeamSize = (teamInfo: any) => {
  if (!teamInfo) return 0
  if (typeof teamInfo === 'string') {
    try {
      const parsed = JSON.parse(teamInfo)
      return Array.isArray(parsed) ? parsed.length : 0
    } catch {
      return 0
    }
  }
  return Array.isArray(teamInfo) ? teamInfo.length : 0
}

// 工作安排标签映射
const getWorkArrangementLabel = (arrangement: string) => {
  const labels: Record<string, string> = {
    'everyday': '每一天',
    'weekends': '仅周末',
    'evenings_weekends': '晚上下班后+周末',
    'fullTime': '全职',
    'partTime': '兼职',
    'contract': '合同工',
    'intern': '实习'
  }
  return labels[arrangement] || arrangement || '未设置'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}
</script>

<style scoped>
.admin-review-page {
  @apply min-h-screen bg-gray-50;
}

.admin-review-container {
  @apply pt-24 px-4 pb-8 max-w-7xl mx-auto;
}

/* 页面头部 */
.page-header {
  @apply flex justify-between items-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-gray-900;
}

.stats-bar {
  @apply flex gap-6;
}

.stat-item {
  @apply flex flex-col items-center p-4 bg-white rounded-lg shadow-sm;
}

.stat-label {
  @apply text-sm text-gray-600;
}

.stat-value {
  @apply text-2xl font-bold text-indigo-600;
}

/* 筛选区域 */
.filter-section {
  @apply flex justify-between items-center mb-6 p-4 bg-white rounded-lg shadow-sm;
}

.filter-group {
  @apply flex gap-4;
}

.filter-select {
  @apply px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500;
}

.search-group {
  @apply flex gap-2;
}

.search-input {
  @apply px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 w-64;
}

.search-btn {
  @apply px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm hover:bg-indigo-700 transition-colors;
}

/* 项目列表 */
.projects-section {
  @apply min-h-96;
}

.loading-state {
  @apply flex flex-col items-center justify-center py-12 text-gray-500;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-indigo-200 border-t-indigo-600 rounded-full animate-spin mb-4;
}

.empty-state {
  @apply text-center py-12 text-gray-500;
}

.empty-icon {
  @apply text-4xl mb-4;
}

.projects-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.project-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow;
}

.project-card.pending {
  @apply border-l-4 border-l-orange-500;
}

.project-header {
  @apply flex justify-between items-start mb-4;
}

.project-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.project-summary {
  @apply text-gray-600 mb-3;
}

.project-meta {
  @apply flex flex-wrap gap-2;
}

.meta-tag {
  @apply px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full;
}

.project-status {
  @apply flex-shrink-0;
}

.status-badge {
  @apply px-3 py-1 rounded-full text-xs font-medium;
}

.status-pending {
  @apply bg-orange-100 text-orange-800;
}

.status-approved {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.project-content {
  @apply space-y-4 mb-4;
}

.content-section h4 {
  @apply text-sm font-semibold text-gray-900 mb-1;
}

.description {
  @apply text-gray-600 text-sm line-clamp-3;
}

.creator-info {
  @apply flex items-center gap-3;
}

.creator-avatar {
  @apply w-8 h-8 rounded-full;
}

.creator-name {
  @apply font-medium text-gray-900;
}

.creator-email {
  @apply text-gray-600 text-sm;
}

.review-message {
  @apply text-gray-600 text-sm italic;
}

.project-actions {
  @apply flex justify-end gap-3 pt-4 border-t border-gray-200;
}

/* 按钮样式 */
.btn {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-outline {
  @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}

/* 弹窗样式 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-lg max-w-md w-full mx-4 max-h-96 overflow-y-auto;
}

.modal-header {
  @apply flex justify-between items-center p-4 border-b border-gray-200;
}

.modal-header h3 {
  @apply text-lg font-semibold;
}

.close-btn {
  @apply text-gray-400 hover:text-gray-600 text-xl;
}

.modal-body {
  @apply p-4 space-y-4;
}

.modal-footer {
  @apply flex justify-end gap-3 p-4 border-t border-gray-200;
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-y;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    @apply flex-col gap-4;
  }
  
  .filter-section {
    @apply flex-col gap-4;
  }
  
  .projects-grid {
    @apply grid-cols-1;
  }
  
  .project-actions {
    @apply flex-col gap-2;
  }
}
</style> 