<template>
  <div class="project-detail-page">
    <!-- 浮动装饰背景 -->
    <div class="floating-shapes">
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
      <div class="shape"></div>
    </div>

    <!-- 使用真实的导航栏组件 -->
    <Navbar />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载项目详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <h2>加载失败</h2>
      <p>{{ error }}</p>
      <button @click="loadProject" class="retry-btn">重试</button>
    </div>

    <!-- 项目详情内容 -->
    <div v-else-if="project">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="hero-container">
          <div class="hero-content">
            <div class="hero-badge">{{ getCategoryText(project.category) }}</div>
            <h1 class="hero-title">{{ project.title }}</h1>
            <p class="hero-subtitle">{{ project.tagline || project.summary || '创新项目，期待您的加入' }}</p>
            <p class="hero-description">
              {{ project.description }}
            </p>
          <div class="hero-buttons">
              <button @click="scrollToJoinSection" class="hero-btn hero-btn-primary">申请加入</button>
          </div>
              </div>
          <div class="project-preview">
            <div class="preview-gallery">
              <div class="main-preview">
                <!-- 如果有上传的图片，显示第一张作为主图 -->
                <div v-if="hasProjectImages" class="preview-slide active" :class="{ active: currentSlide === 0 }">
                  <img :src="getImageUrl(project.images[0].url)" :alt="project.images[0].name" class="slide-image" />
                  <div class="slide-overlay">
                    <div class="slide-title">{{ project.images[0].name || '项目主界面' }}</div>
                    <div class="slide-desc">{{ project.title }}的核心功能展示</div>
                  </div>
                </div>
                <!-- 默认图标展示（当没有上传图片时） -->
                <div v-else class="preview-slide active" :class="{ active: currentSlide === 0 }">
                  <div class="slide-content">
                    <div class="slide-icon">{{ getCategoryIcon(project.category) }}</div>
                    <div class="slide-title">项目主界面</div>
                    <div class="slide-desc">{{ project.title }}的核心功能展示</div>
                  </div>
                </div>
                
                <!-- 其他上传的图片 -->
                <div v-for="(image, index) in additionalImages" :key="index + 1" 
                     class="preview-slide" :class="{ active: currentSlide === index + 1 }">
                  <img :src="getImageUrl(image.url)" :alt="image.name" class="slide-image" />
                  <div class="slide-overlay">
                    <div class="slide-title">{{ image.name || `界面${index + 2}` }}</div>
                    <div class="slide-desc">{{ image.description || '功能界面展示' }}</div>
                  </div>
                </div>
              </div>
              <button v-if="totalSlides > 1" class="preview-nav prev-btn" @click="changeSlide(-1)">‹</button>
              <button v-if="totalSlides > 1" class="preview-nav next-btn" @click="changeSlide(1)">›</button>
            </div>
            
            <!-- 缩略图条 -->
            <div class="thumbnail-strip" v-if="totalSlides > 1">
              <!-- 主图缩略图 -->
              <div class="thumbnail" :class="{ active: currentSlide === 0 }" @click="goToSlide(0)">
                <img v-if="hasProjectImages" :src="getImageUrl(project.images[0].url)" :alt="project.images[0].name" class="thumbnail-image" />
                <span v-else class="thumbnail-icon">{{ getCategoryIcon(project.category) }}</span>
              </div>
              <!-- 其他图片缩略图 -->
              <div v-for="(image, index) in additionalImages" :key="index + 1" 
                   class="thumbnail" :class="{ active: currentSlide === index + 1 }" @click="goToSlide(index + 1)">
                <img :src="getImageUrl(image.url)" :alt="image.name" class="thumbnail-image" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 故事章节 -->
      <section class="story-section">
        <div class="story-container">
          <div class="chapter">
            <div class="chapter-number">01</div>
            <h2 class="chapter-title">项目起源</h2>
            <div class="chapter-content">
              <div class="chapter-quote">
                {{ project.projectOrigin || '这个项目源于团队对创新的热情和对市场需求的深度洞察。我们希望通过技术的力量，为用户创造真正有价值的产品。' }}
              </div>
              </div>
            </div>
        
          <div class="chapter">
            <div class="chapter-number">02</div>
            <h2 class="chapter-title">竞争优势</h2>
            <div class="chapter-content">
              <div class="content-box">
                {{ project.competitiveAdvantage || '我们采用最新的技术栈和开发理念，确保产品的技术先进性和用户体验。团队具备丰富的行业经验，能够应对各种技术挑战。' }}
              </div>
            </div>
          </div>
        
          <div class="chapter">
            <div class="chapter-number">03</div>
            <h2 class="chapter-title">用户价值</h2>
            <div class="chapter-content">
              <div class="content-box">
                {{ project.userAnalysis || '我们深入分析了目标用户群体的需求和痛点，设计了针对性的解决方案。产品将为用户带来显著的价值提升和体验改善。' }}
              </div>
            </div>
          </div>

          <div class="chapter">
            <div class="chapter-number">04</div>
            <h2 class="chapter-title">团队愿景</h2>
            <div class="chapter-content">
              <div class="content-box">
                {{ project.vision || project.teamPhilosophy || '我们致力于打造一个充满创新精神的团队，通过技术的力量为用户创造价值，推动行业的发展和进步。' }}
              </div>
            </div>
          </div>

    <!-- 项目进度 -->
          <div class="project-progress">
            <h3 class="progress-title">🚀 项目开发进度</h3>
        <div class="progress-timeline">
              <div class="progress-line">
                <div class="progress-fill" :style="{ width: `${project.progress || 25}%` }"></div>
            </div>
              <div class="progress-step" v-for="(milestone, index) in project.milestones || defaultMilestones" :key="index">
                <div class="step-circle" :class="{ 
                  completed: milestone.status === 'completed', 
                  active: milestone.status === 'current' 
                }">{{ index + 1 }}</div>
                <div class="step-label" :class="{ 
                  completed: milestone.status === 'completed', 
                  active: milestone.status === 'current' 
                }">{{ milestone.name }}</div>
              </div>
          </div>
        </div>
      </div>
    </section>

      <!-- 团队故事 -->
      <section class="team-story">
      <div class="team-container">
          <h2 class="section-title">我们的团队</h2>
          <p class="section-subtitle">每个成员都有自己独特的故事和技能</p>
          
        <div class="team-grid">
            <!-- 只展示项目发布时填写的团队成员 -->
            <div class="team-member" v-for="member in project.teamInfo" :key="member.name" v-if="project.teamInfo && project.teamInfo.length > 0">
              <div class="member-avatar">{{ member.name?.charAt(0) || '成' }}</div>
              <div class="member-name">{{ member.name }}</div>
              <div class="member-role">{{ member.role }}</div>
              <div class="member-story">
                {{ member.background || member.introduction || '团队核心成员，具备丰富的专业经验和项目开发能力。' }}
              </div>
            </div>

            <!-- 如果没有团队成员信息，显示提示 -->
            <div v-if="!project.teamInfo || project.teamInfo.length === 0" class="no-team-info">
              <p>团队信息正在完善中...</p>
            </div>
        </div>
      </div>
    </section>

    <!-- 加入我们 -->
      <section class="join-section">
      <div class="join-container">
          <h2 class="join-title">加入我们的团队</h2>
        <p class="join-subtitle">
          我们正在寻找志同道合的伙伴，一起完成这个充满创意和挑战的项目。
            不论你是程序员、设计师，还是产品经理，只要你对项目有热情，我们都欢迎你的加入。
        </p>
        
        <div class="wanted-roles">
            <div class="wanted-role" v-for="requirement in project.requirements?.slice(0, 4)" :key="requirement.role" @click="openRoleModal(requirement)">
              <div class="role-icon">{{ getRoleIcon(requirement.role) }}</div>
              <div class="role-title">{{ requirement.roleName || requirement.role }}</div>
              <div class="role-desc">
                <div class="role-skills">
                  <span class="skill-label">技能要求:</span>
                  <span class="skill-value">{{ requirement.skillName || '相关专业技能' }}</span>
                </div>
                <div class="role-salary">
                  <span class="salary-label">薪资待遇:</span>
                  <span class="salary-value">{{ getSalaryText(requirement.cooperation) }}</span>
                </div>
              </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 角色详情弹窗 -->
      <div class="role-modal" :class="{ active: showRoleModal }" @click="closeRoleModal">
      <div class="role-modal-content" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">
              <span>{{ getRoleIcon(selectedRequirement?.role) }}</span>
              <span>{{ selectedRequirement?.roleName || selectedRequirement?.role }}</span>
          </h3>
          <button class="close-modal" @click="closeRoleModal">&times;</button>
        </div>
        
        <div class="job-info-grid">
          <div class="info-card">
              <div class="info-label">合作方式</div>
              <div class="info-value">{{ getSalaryText(selectedRequirement?.cooperation) }}</div>
          </div>
          <div class="info-card">
              <div class="info-label">工作安排</div>
              <div class="info-value">{{ getWorkArrangementText(project.workArrangement) }}</div>
          </div>
          <div class="info-card">
            <div class="info-label">工作地点</div>
              <div class="info-value">{{ getWorkLocationText(project.workLocation) }}</div>
          </div>
          <div class="info-card">
            <div class="info-label">薪资类型</div>
            <div class="info-value">
                <span class="salary-badge">
                  {{ getSalaryText(selectedRequirement?.cooperation) }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="role-description">
          <h4>职位描述</h4>
            <p>{{ getRoleDescription(selectedRequirement?.role) }}</p>
          
          <h4>技能要求</h4>
          <ul class="requirements-list">
              <li>{{ selectedRequirement?.skillName || '相关专业技能' }}</li>
              <li>{{ selectedRequirement?.level || '熟练' }}掌握相关技术栈</li>
              <li>良好的团队协作能力</li>
              <li>对项目有热情和责任心</li>
              <li>能够在规定时间内完成任务</li>
          </ul>
        </div>
        
        <div class="modal-actions">
          <button class="modal-btn modal-btn-secondary" @click="closeRoleModal">稍后考虑</button>
          <button class="modal-btn" @click="applyForRole">立即申请</button>
        </div>
      </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
// @ts-ignore
import Navbar from '@/components/layout/Navbar.vue'
// @ts-ignore
import { getProjectDetail } from '@/api/project'

const route = useRoute()
const router = useRouter()

// 响应式状态
const loading = ref(true)
const error = ref('')
const project = ref<any>(null)
const currentSlide = ref(0)
const showRoleModal = ref(false)
const selectedRequirement = ref<any>(null)

// 图片相关计算属性
const hasProjectImages = computed(() => {
  return project.value?.images && project.value.images.length > 0
})

const additionalImages = computed(() => {
  if (!hasProjectImages.value) return []
  return project.value.images.slice(1) // 除了第一张图片之外的所有图片
})

const totalSlides = computed(() => {
  if (!hasProjectImages.value) return 1
  return project.value.images.length
})

// 图片URL处理方法
const getImageUrl = (imageUrl: string) => {
  // 如果是相对路径，添加后端服务器前缀
  if (imageUrl.startsWith('/uploads/')) {
    return `http://localhost:8000${imageUrl}`
  }
  // 如果是完整URL，直接返回
  return imageUrl
}

// 默认里程碑
const defaultMilestones = [
  { name: '团队组建', status: 'completed' },
  { name: '需求设计', status: 'current' },
  { name: '开发实现', status: 'pending' },
  { name: '测试上线', status: 'pending' }
]

// 获取项目ID
const projectId = computed(() => route.params.id as string)

// 加载项目数据
const loadProject = async () => {
  if (!projectId.value) {
    error.value = '无效的项目ID'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    console.log('🔍 开始获取项目详情, 项目ID:', projectId.value)
    const response = await getProjectDetail(parseInt(projectId.value))
    console.log('📡 项目详情API响应:', response)
    
    if (response.success) {
      project.value = response.data.project
      console.log('✅ 项目数据加载成功:', project.value)
      
      // 确保必要的数据结构存在
      if (!project.value.requirements) {
        project.value.requirements = []
      }
      if (!project.value.teamMembers) {
        project.value.teamMembers = []
      }
      if (!project.value.images) {
        project.value.images = []
      }
      if (!project.value.milestones) {
        project.value.milestones = defaultMilestones
      }
      
      console.log('📊 项目数据结构检查完成')
    } else {
      error.value = response.message || '获取项目详情失败'
      console.error('❌ API返回错误:', response)
    }
  } catch (err: any) {
    console.error('💥 获取项目详情失败:', err)
    error.value = err.message || '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 工具函数
const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    'web': '🌐 Web应用',
    'mobile': '📱 移动应用',
    'desktop': '💻 桌面应用',
    'game': '🎮 游戏开发',
    'ai': '🤖 AI智能',
    'blockchain': '⛓️ 区块链',
    'tool': '🔧 开发工具',
    'ecommerce': '🛒 电商平台',
    'social': '👥 社交应用',
    'education': '📚 教育科技'
  }
  return categoryMap[category] || '💡 创新项目'
}

const getCategoryIcon = (category: string) => {
  const iconMap: Record<string, string> = {
    'web': '🌐',
    'mobile': '📱',
    'desktop': '💻',
    'game': '🎮',
    'ai': '🤖',
    'blockchain': '⛓️',
    'tool': '🔧',
    'ecommerce': '🛒',
    'social': '👥',
    'education': '📚'
  }
  return iconMap[category] || '💡'
}

const getRoleIcon = (role: string) => {
  const iconMap: Record<string, string> = {
    'frontend': '💻',
    'backend': '⚙️',
    'designer': '🎨',
    'pm': '📊',
    'qa': '🔍',
    'devops': '🚀',
    'mobile': '📱',
    'fullstack': '🌟'
  }
  return iconMap[role] || '👨‍💼'
}

const getRoleDescription = (role: string) => {
  const descMap: Record<string, string> = {
    'frontend': '负责用户界面开发，需要熟练掌握前端技术栈',
    'backend': '负责服务端开发和数据库设计，确保系统稳定性',
    'designer': '负责产品UI/UX设计，创造优秀的用户体验',
    'pm': '负责产品规划和项目管理，协调团队高效协作',
    'qa': '负责软件测试和质量保证，确保产品质量',
    'devops': '负责部署运维和持续集成，保障系统稳定运行',
    'mobile': '负责移动端应用开发，适配各种移动设备',
    'fullstack': '全栈开发工程师，能够胜任前后端开发工作'
  }
  return descMap[role] || '参与项目开发，发挥专业技能，与团队协作完成项目目标'
}

const getSalaryText = (cooperation: string) => {
  const salaryMap: Record<string, string> = {
    'salary': '固定薪资',
    'equity': '股权分成',
    'salary_equity': '薪资+股权',
    'profit_sharing': '利润分红',
    'volunteer': '志愿参与',
    'internship': '实习机会'
  }
  return salaryMap[cooperation] || '面议'
}

const getWorkArrangementText = (arrangement: string) => {
  const map: Record<string, string> = {
    'fullTime': '全职',
    'partTime': '兼职',
    'contract': '合同工',
    'intern': '实习',
    'flexible': '灵活安排'
  }
  return map[arrangement] || '灵活安排'
}

const getWorkLocationText = (location: string) => {
  const map: Record<string, string> = {
    'remote': '远程协作',
    'onsite': '现场办公',
    'hybrid': '混合办公'
  }
  return map[location] || '灵活安排'
}

// 轮播图相关方法
const changeSlide = (direction: number) => {
  if (!totalSlides.value || totalSlides.value <= 1) return
  
  const newIndex = (currentSlide.value + direction + totalSlides.value) % totalSlides.value
  currentSlide.value = newIndex
}

const goToSlide = (index: number) => {
  if (index >= 0 && index < totalSlides.value) {
    currentSlide.value = index
  }
}

// 角色弹窗相关方法
const openRoleModal = (requirement: any) => {
  selectedRequirement.value = requirement
  showRoleModal.value = true
}

const closeRoleModal = () => {
  showRoleModal.value = false
  selectedRequirement.value = null
}

const applyForRole = () => {
  // TODO: 实现申请逻辑
  alert('申请功能开发中...')
  closeRoleModal()
}

const handleApplyProject = () => {
  // TODO: 实现项目申请逻辑
  alert('项目申请功能开发中...')
}

const scrollToJoinSection = () => {
  const joinSection = document.querySelector('.join-section')
  if (joinSection) {
    joinSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// 生命周期
onMounted(() => {
  loadProject()
})
</script>

<style scoped>
/* 全局样式 */
.project-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  padding-top: 72px; /* 为固定导航栏留出空间 */
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: white;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: white;
  text-align: center;
  padding: 20px;
}

.error-container h2 {
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.error-container p {
  margin-bottom: 20px;
  opacity: 0.8;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 浮动装饰背景 */
.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes .shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.floating-shapes .shape:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-shapes .shape:nth-child(2) {
  width: 120px;
  height: 120px;
  top: 60%;
  left: 80%;
  animation-delay: 1s;
}

.floating-shapes .shape:nth-child(3) {
  width: 60px;
  height: 60px;
  top: 40%;
  left: 70%;
  animation-delay: 2s;
}

.floating-shapes .shape:nth-child(4) {
  width: 100px;
  height: 100px;
  top: 80%;
  left: 20%;
  animation-delay: 3s;
}

.floating-shapes .shape:nth-child(5) {
  width: 40px;
  height: 40px;
  top: 10%;
  left: 60%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 导航栏 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 0 20px;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 50%, #ec4899 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 32px;
}

.nav-menu a {
  color: #64748b;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-menu a.active,
.nav-menu a:hover {
  color: #6366f1;
}

.nav-buttons {
  display: flex;
  gap: 12px;
}

.nav-btn {
  padding: 8px 16px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-ghost {
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  color: white;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  z-index: 2;
  padding: 80px 0;
  color: white;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-content {
  animation: slideInLeft 1s ease-out;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 16px;
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 24px;
  line-height: 1.6;
}

.hero-description {
  font-size: 1rem;
  opacity: 0.8;
  line-height: 1.7;
  margin-bottom: 40px;
}

.hero-buttons {
  display: flex;
  gap: 16px;
}

.hero-btn {
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.hero-btn-primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.hero-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
}

/* 项目预览区域 */
.project-preview {
  animation: slideInRight 1s ease-out;
}

.preview-gallery {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.main-preview {
  position: relative;
  height: 300px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 20px;
}

.preview-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-slide.active {
  opacity: 1;
  transform: translateX(0);
}

.slide-content {
  text-align: center;
  color: white;
}

.slide-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.slide-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.slide-desc {
  font-size: 1rem;
  opacity: 0.8;
}

.preview-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.preview-nav:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.thumbnail-strip {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.thumbnail {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.thumbnail.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.thumbnail:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 图片展示样式 */
.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.slide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  border-radius: 0 0 8px 8px;
}

.slide-overlay .slide-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.slide-overlay .slide-desc {
  font-size: 0.9rem;
  opacity: 0.9;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.thumbnail-icon {
  font-size: 1.2rem;
  color: white;
}

/* 故事章节 */
.story-section {
  background: white;
  position: relative;
  z-index: 2;
}

.story-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 100px 24px;
}

.chapter {
  margin-bottom: 80px;
  position: relative;
}

.chapter-number {
  position: absolute;
  left: -80px;
  top: 0;
  font-size: 6rem;
  font-weight: 900;
  color: #f0f0f0;
  line-height: 1;
}

.chapter-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 32px;
  color: #2d3748;
  position: relative;
}

.chapter-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a5568;
  position: relative;
}

.content-box {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 24px;
  margin: 16px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.content-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.chapter-quote {
  margin: 40px 0;
  padding: 30px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  font-style: italic;
  font-size: 1.2rem;
  position: relative;
}

.chapter-quote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  opacity: 0.3;
}

/* 项目进度 */
.project-progress {
  margin-top: 80px;
  padding: 40px;
  background: #f8fafc;
  border-radius: 16px;
}

.progress-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 40px;
  color: #2d3748;
  text-align: center;
}

.progress-timeline {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.progress-line {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 1s ease;
}

.progress-step {
  position: relative;
  text-align: center;
  z-index: 1;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #a0aec0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin: 0 auto 12px;
  transition: all 0.3s ease;
}

.step-circle.completed {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.step-circle.active {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  animation: pulse 2s infinite;
}

.step-label {
  font-size: 0.9rem;
  color: #a0aec0;
  font-weight: 500;
}

.step-label.completed,
.step-label.active {
  color: #4a5568;
  font-weight: 600;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 分析部分样式 */
.analysis-section {
  margin-top: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.analysis-section h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  position: relative;
}

.analysis-section h4::before {
  content: '📊';
  margin-right: 8px;
}

.analysis-section p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #4a5568;
  margin: 0;
}

/* 团队故事 */
.team-story {
  background: #f8fafc;
  position: relative;
  z-index: 2;
}

.team-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 24px;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 16px;
  color: #2d3748;
}

.section-subtitle {
  font-size: 1.2rem;
  text-align: center;
  color: #718096;
  margin-bottom: 80px;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.team-member {
  background: white;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.team-member:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.member-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 auto 20px;
}

.member-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2d3748;
}

.member-role {
  font-size: 1rem;
  color: #667eea;
  margin-bottom: 20px;
  font-weight: 500;
}

.member-story {
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
}

.no-team-info {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  font-size: 1.1rem;
  grid-column: 1 / -1;
}

/* 加入我们 */
.join-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  z-index: 2;
}

.join-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 100px 24px;
  text-align: center;
}

.join-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 24px;
}

.join-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 80px;
  line-height: 1.6;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.wanted-roles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.wanted-role {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px 30px;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
}

.wanted-role:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.role-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.role-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.role-desc {
  font-size: 0.95rem;
  opacity: 0.8;
  line-height: 1.5;
}

.role-skills,
.role-salary {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.skill-label,
.salary-label {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.skill-value,
.salary-value {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}


/* 角色详情弹窗 */
.role-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.role-modal.active {
  opacity: 1;
  visibility: visible;
}

.role-modal-content {
  background: white;
  border-radius: 20px;
  padding: 50px;
  max-width: 800px;
  width: 95%;
  max-height: 85vh;
  overflow-y: auto;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.role-modal.active .role-modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 12px;
}

.close-modal {
  background: none;
  border: none;
  font-size: 2rem;
  color: #a0aec0;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: #4a5568;
}

.job-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.info-card {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
}

.info-label {
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.salary-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.role-description h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  margin-top: 30px;
}

.role-description h4:first-child {
  margin-top: 0;
}

.role-description p {
  font-size: 1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 20px;
}

.requirements-list {
  list-style: none;
  padding: 0;
}

.requirements-list li {
  font-size: 1rem;
  color: #4a5568;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.requirements-list li::before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.modal-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.modal-btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.modal-btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-btn-secondary:hover {
  background: #cbd5e0;
}

.modal-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 动画 */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
  
  .chapter-number {
    position: static;
    font-size: 4rem;
    text-align: center;
    margin-bottom: 20px;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .hero-container {
    padding: 0 16px;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .story-container,
  .team-container,
  .join-container {
    padding: 60px 16px;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .join-title {
    font-size: 2rem;
  }
  
  .team-grid {
    grid-template-columns: 1fr;
  }
  
  .wanted-roles {
    grid-template-columns: 1fr;
  }
  
  .job-info-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .role-modal-content {
    padding: 30px 20px;
    margin: 20px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }
  
  .chapter-title {
    font-size: 1.8rem;
  }
  
  .section-title,
  .join-title {
    font-size: 1.8rem;
  }
}
</style> 