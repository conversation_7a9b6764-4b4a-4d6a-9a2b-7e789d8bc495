<template>
  <div class="admin-user-page">
    <Navbar />
    <div class="admin-user-container">
      <div class="page-header">
        <h1 class="page-title">用户管理</h1>
        <div class="stats-bar">
          <div class="stat-item">
            <span class="stat-label">总用户数</span>
            <span class="stat-value">{{ totalUsers }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">DAO成员</span>
            <span class="stat-value">{{ daoMemberCount }}</span>
          </div>
        </div>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <div class="filter-group">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索用户名或邮箱..."
            class="search-input"
          />
          <select v-model="filterRole" class="filter-select">
            <option value="">全部角色</option>
            <option value="user">普通用户</option>
            <option value="admin">管理员</option>
          </select>
          <select v-model="filterDaoStatus" class="filter-select">
            <option value="">全部状态</option>
            <option value="dao">DAO成员</option>
            <option value="non-dao">非DAO成员</option>
          </select>
        </div>
        <div class="action-group">
          <button @click="loadUsers" class="btn btn-primary">
            🔍 搜索
          </button>
        </div>
      </div>

      <!-- 用户列表 -->
      <div class="users-section">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>加载中...</p>
        </div>

        <div v-else-if="users.length === 0" class="empty-state">
          <div class="empty-icon">👥</div>
          <h3>暂无用户</h3>
          <p>当前筛选条件下没有找到用户</p>
        </div>

        <div v-else class="users-table">
          <table>
            <thead>
              <tr>
                <th>用户信息</th>
                <th>角色</th>
                <th>DAO状态</th>
                <th>项目数</th>
                <th>注册时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in users" :key="user.id" class="user-row">
                <td class="user-info">
                  <div class="user-avatar">
                    <img v-if="user.avatar" :src="user.avatar" :alt="user.username" />
                    <span v-else>{{ getUserInitial(user) }}</span>
                  </div>
                  <div class="user-details">
                    <div class="user-name">{{ user.username || '未设置' }}</div>
                    <div class="user-email">{{ user.email }}</div>
                    <div v-if="user.location" class="user-location">📍 {{ user.location }}</div>
                  </div>
                </td>
                <td>
                  <span class="role-badge" :class="user.role">
                    {{ user.role === 'admin' ? '管理员' : '普通用户' }}
                  </span>
                </td>
                <td>
                  <span class="dao-status" :class="{ active: user.isDaoMember }">
                    {{ user.isDaoMember ? 'DAO成员' : '非成员' }}
                  </span>
                </td>
                <td>{{ user.projectCount }}</td>
                <td>{{ formatDate(user.createdAt) }}</td>
                <td class="actions">
                  <button
                    v-if="!user.isDaoMember"
                    @click="addToDaoMember(user)"
                    class="btn btn-sm btn-success"
                    :disabled="updating"
                  >
                    加入DAO
                  </button>
                  <button
                    v-else
                    @click="removeFromDaoMember(user)"
                    class="btn btn-sm btn-danger"
                    :disabled="updating"
                  >
                    移除DAO
                  </button>
                  <button
                    @click="editUser(user)"
                    class="btn btn-sm btn-outline"
                  >
                    编辑
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div v-if="pagination.pages > 1" class="pagination">
          <button
            @click="changePage(pagination.page - 1)"
            :disabled="pagination.page <= 1"
            class="pagination-btn"
          >
            上一页
          </button>
          <span class="pagination-info">
            第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页
          </span>
          <button
            @click="changePage(pagination.page + 1)"
            :disabled="pagination.page >= pagination.pages"
            class="pagination-btn"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑用户弹窗 -->
    <div v-if="showEditModal" class="modal-overlay" @click="closeEditModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>编辑用户</h3>
          <button class="modal-close" @click="closeEditModal">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveUser">
            <div class="form-group">
              <label class="form-label">用户名</label>
              <input
                v-model="editForm.username"
                type="text"
                class="form-input"
                placeholder="请输入用户名"
              />
            </div>
            <div class="form-group">
              <label class="form-label">邮箱</label>
              <input
                v-model="editForm.email"
                type="email"
                class="form-input"
                placeholder="请输入邮箱"
                required
              />
            </div>
            <div class="form-group">
              <label class="form-label">角色</label>
              <select v-model="editForm.role" class="form-select">
                <option value="user">普通用户</option>
                <option value="admin">管理员</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">位置</label>
              <input
                v-model="editForm.location"
                type="text"
                class="form-input"
                placeholder="请输入位置"
              />
            </div>
            <div class="form-group">
              <label class="form-label">个人简介</label>
              <textarea
                v-model="editForm.bio"
                class="form-textarea"
                placeholder="请输入个人简介"
                rows="3"
              ></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="btn btn-secondary" @click="closeEditModal">取消</button>
              <button type="submit" class="btn btn-primary" :disabled="saving">
                {{ saving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Navbar from '@/components/layout/Navbar.vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const users = ref<any[]>([])
const loading = ref(false)
const updating = ref(false)
const saving = ref(false)
const searchQuery = ref('')
const filterRole = ref('')
const filterDaoStatus = ref('')

// 分页数据
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0,
  pages: 0
})

// 编辑弹窗
const showEditModal = ref(false)
const currentUser = ref<any>(null)
const editForm = reactive({
  username: '',
  email: '',
  role: 'user',
  location: '',
  bio: ''
})

// 计算属性
const totalUsers = computed(() => pagination.total)
const daoMemberCount = computed(() => 
  users.value.filter(user => user.isDaoMember).length
)

// 权限检查
onMounted(async () => {
  if (!userStore.isAuthenticated || userStore.user?.role !== 'admin') {
    alert('权限不足，请联系管理员')
    router.push('/login')
    return
  }
  
  await loadUsers()
})

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: pagination.page.toString(),
      limit: pagination.limit.toString()
    })
    
    if (searchQuery.value) {
      params.append('search', searchQuery.value)
    }
    if (filterRole.value) {
      params.append('role', filterRole.value)
    }
    
    const response = await fetch(`/api/admin/users?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const result = await response.json()
    
    if (result.success) {
      users.value = result.data.users.filter((user: any) => {
        if (filterDaoStatus.value === 'dao') return user.isDaoMember
        if (filterDaoStatus.value === 'non-dao') return !user.isDaoMember
        return true
      })
      
      Object.assign(pagination, result.data.pagination)
    } else {
      alert(result.message || '加载用户列表失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    alert('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 分页
const changePage = (page: number) => {
  pagination.page = page
  loadUsers()
}

// DAO成员管理
const addToDaoMember = async (user: any) => {
  if (!confirm(`确认将 ${user.username || user.email} 加入DAO成员？`)) return
  
  updating.value = true
  try {
    const response = await fetch(`/api/admin/dao-members/${user.id}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const result = await response.json()
    
    if (result.success) {
      alert('用户已加入DAO成员')
      await loadUsers()
    } else {
      alert(result.message || '操作失败')
    }
  } catch (error) {
    console.error('加入DAO成员失败:', error)
    alert('操作失败')
  } finally {
    updating.value = false
  }
}

const removeFromDaoMember = async (user: any) => {
  if (!confirm(`确认将 ${user.username || user.email} 从DAO成员中移除？`)) return
  
  updating.value = true
  try {
    const response = await fetch(`/api/admin/dao-members/${user.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    const result = await response.json()
    
    if (result.success) {
      alert('用户已从DAO成员中移除')
      await loadUsers()
    } else {
      alert(result.message || '操作失败')
    }
  } catch (error) {
    console.error('移除DAO成员失败:', error)
    alert('操作失败')
  } finally {
    updating.value = false
  }
}

// 编辑用户
const editUser = (user: any) => {
  currentUser.value = user
  editForm.username = user.username || ''
  editForm.email = user.email
  editForm.role = user.role
  editForm.location = user.location || ''
  editForm.bio = user.bio || ''
  showEditModal.value = true
}

const closeEditModal = () => {
  showEditModal.value = false
  currentUser.value = null
}

const saveUser = async () => {
  if (!currentUser.value) return
  
  saving.value = true
  try {
    const response = await fetch(`/api/admin/users/${currentUser.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(editForm)
    })
    
    const result = await response.json()
    
    if (result.success) {
      alert('用户信息更新成功')
      closeEditModal()
      await loadUsers()
    } else {
      alert(result.message || '更新失败')
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    alert('更新失败')
  } finally {
    saving.value = false
  }
}

// 工具函数
const getUserInitial = (user: any) => {
  if (user.username) return user.username.charAt(0).toUpperCase()
  if (user.email) return user.email.charAt(0).toUpperCase()
  return '?'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}
</script>

<style scoped>
/* 基础样式 */
.admin-user-page {
  min-height: 100vh;
  background: #f8fafc;
}

.admin-user-container {
  padding-top: 96px;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin: 0;
}

.stats-bar {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #4f46e5;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-group {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 150px;
}

/* 用户表格 */
.users-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.users-table table {
  width: 100%;
  border-collapse: collapse;
}

.users-table th,
.users-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.users-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #6b7280;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #1f2937;
}

.user-email {
  font-size: 0.875rem;
  color: #6b7280;
}

.user-location {
  font-size: 0.75rem;
  color: #9ca3af;
}

.role-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.role-badge.admin {
  background: #fef3c7;
  color: #d97706;
}

.role-badge.user {
  background: #e0e7ff;
  color: #4338ca;
}

.dao-status {
  font-size: 0.875rem;
  color: #6b7280;
}

.dao-status.active {
  color: #059669;
  font-weight: 600;
}

.actions {
  display: flex;
  gap: 8px;
}

.btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.75rem;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-outline:hover {
  background: #f9fafb;
}

.btn-primary {
  background: #4f46e5;
  color: white;
}

.btn-primary:hover {
  background: #4338ca;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f9fafb;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.form-textarea {
  resize: vertical;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #4f46e5;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #1f2937;
}

.empty-state p {
  margin: 0;
  color: #6b7280;
}

/* 响应式 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-section {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-group {
    flex-wrap: wrap;
  }
  
  .search-input {
    width: 100%;
  }
  
  .users-table {
    overflow-x: auto;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style>
