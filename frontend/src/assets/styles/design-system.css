/* 小概率项目设计系统 - 基于原型精确还原 */

/* =============== CSS变量定义 =============== */
:root {
  /* 主色彩系统 - 基于原型渐变色 */
  --color-primary: #667eea;
  --color-primary-dark: #764ba2;
  --color-primary-light: #8b5cf6;
  --color-secondary: #ec4899;
  --color-accent: #f59e0b;
  --color-success: #10b981;

  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --gradient-auth-brand: linear-gradient(135deg, #8b5cf6 0%, #ec4899 30%, #f59e0b 60%, #10b981 100%);
  --gradient-purple: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  --gradient-yellow: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  --gradient-blue: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

  /* 中性色系 */
  --color-gray-50: #f8fafc;
  --color-gray-100: #f1f5f9;
  --color-gray-200: #e2e8f0;
  --color-gray-300: #cbd5e1;
  --color-gray-400: #94a3b8;
  --color-gray-500: #64748b;
  --color-gray-600: #475569;
  --color-gray-700: #334155;
  --color-gray-800: #1e293b;
  --color-gray-900: #0f172a;
  --color-white: #ffffff;
  --color-black: #1a1a1a;

  /* 语义化颜色 */
  --color-success-light: #dcfce7;
  --color-success-dark: #166534;
  --color-warning-light: #fef3c7;
  --color-warning-dark: #92400e;
  --color-error-light: #fee2e2;
  --color-error-dark: #dc2626;
  --color-info-light: #dbeafe;
  --color-info-dark: #1d4ed8;

  /* 字体系统 */
  --font-family-primary: 'Inter', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;

  /* 字体大小 - 基于原型 */
  --font-size-xs: 10px;      /* timeline-phase */
  --font-size-sm: 12px;      /* hero-stat-label, skill-tag */
  --font-size-base: 14px;    /* nav-menu, chapter-content */
  --font-size-md: 16px;      /* hero-btn, cta-btn */
  --font-size-lg: 18px;      /* hero-subtitle, cta-subtitle */
  --font-size-xl: 20px;      /* footer-logo */
  --font-size-2xl: 24px;     /* logo, brand-tagline */
  --font-size-3xl: 36px;     /* cta-title */
  --font-size-4xl: 42px;     /* brand-logo */
  --font-size-5xl: 48px;     /* hero-title */

  /* 字体重量 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* 间距系统 - 基于原型精确值 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-15: 60px;
  --space-20: 80px;
  --space-24: 96px;
  --space-30: 120px;

  /* 圆角系统 */
  --radius-sm: 6px;          /* nav-btn */
  --radius-base: 8px;        /* hero-btn, join-btn */
  --radius-md: 12px;         /* story-chapter */
  --radius-lg: 16px;         /* creative-elements */
  --radius-xl: 20px;         /* hero-badge, auth-container */
  --radius-full: 50%;        /* 圆形元素 */

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.08);    /* auth-container */

  /* 毛玻璃效果 */
  --backdrop-blur: blur(20px);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);

  /* 过渡动画 */
  --transition-fast: all 0.15s ease;
  --transition-base: all 0.2s ease;
  --transition-slow: all 0.3s ease;
  --transition-colors: color 0.2s ease;

  /* 布局尺寸 */
  --container-max-width: 1200px;
  --navbar-height: 64px;      /* 基于原型 nav-container height */
  --sidebar-width: 280px;
  --content-max-width: 800px;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* =============== 基础重置和全局样式 =============== */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.6;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  background: var(--color-white);
  color: var(--color-black);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* =============== 布局组件 =============== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--space-5);
}

.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
}

/* =============== 字体工具类 =============== */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }
.font-black { font-weight: var(--font-weight-black); }

/* =============== 按钮组件系统 =============== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border: none;
  border-radius: var(--radius-sm);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-base);
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-sm);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-base);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-md);
  border-radius: var(--radius-base);
}

/* 按钮变体 */
.btn-primary {
  background: var(--gradient-primary);
  color: var(--color-white);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
}

.btn-secondary:hover {
  background: var(--color-primary);
  color: var(--color-white);
}

.btn-ghost {
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-gray-200);
}

.btn-ghost:hover {
  background: var(--color-gray-50);
  color: var(--color-black);
}

.btn-white {
  background: var(--color-white);
  color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.btn-white:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* =============== 卡片组件 =============== */
.card {
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--color-gray-200);
}

.card-body {
  padding: var(--space-5);
}

.card-footer {
  padding: var(--space-4) var(--space-5);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
}

/* =============== 徽章和标签 =============== */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.badge-success {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.badge-warning {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.badge-primary {
  background: var(--color-primary);
  color: var(--color-white);
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-colors);
}

.tag:hover {
  background: var(--color-gray-200);
}

.tag-skill {
  background: var(--color-info-light);
  color: var(--color-info-dark);
}

.tag-skill.match {
  background: var(--gradient-yellow);
  color: var(--color-white);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.3);
}

/* =============== 动画效果 =============== */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1); 
    opacity: 1; 
  }
  50% { 
    transform: scale(1.05); 
    opacity: 0.8; 
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 动画工具类 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* =============== 工具类 =============== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.no-underline {
  text-decoration: none;
}

.pointer {
  cursor: pointer;
}

.select-none {
  user-select: none;
}

/* =============== 响应式断点 =============== */
@media (max-width: 640px) {
  :root {
    --container-padding: var(--space-4);
    --font-size-5xl: 36px;  /* 移动端缩小标题 */
    --font-size-3xl: 28px;
  }
  
  .container {
    padding: 0 var(--container-padding);
  }
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
  
  .btn-lg {
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-base);
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
} 