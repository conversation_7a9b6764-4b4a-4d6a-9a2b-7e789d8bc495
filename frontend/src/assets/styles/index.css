@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 导入设计系统 */
@import './design-system.css';

/* 全局样式 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --glass-bg: rgba(255, 255, 255, 0.95);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-soft: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 毛玻璃效果 */
.glass-effect {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

/* 按钮样式 */
.btn {
  @apply px-6 py-3 rounded-xl font-medium transition-all duration-300 cursor-pointer inline-flex items-center justify-center gap-2;
}

.btn-primary {
  background: var(--primary-gradient);
  @apply text-white shadow-lg hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
}

.btn-ghost {
  @apply bg-transparent text-gray-600 hover:bg-gray-100;
}

/* 输入框样式 */
.input-field {
  @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-2xl shadow-soft border border-gray-100;
}

/* 文本样式 */
.text-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-soft {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}

/* 页面过渡 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态 */
.loading-spinner {
  @apply inline-block w-6 h-6 border-2 border-gray-200 border-t-primary rounded-full animate-spin;
} 