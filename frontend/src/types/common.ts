// 通用选项类型
export interface Option<T = any> {
  label: string
  value: T
  disabled?: boolean
  icon?: string
  color?: string
}

// 选项组
export interface OptionGroup<T = any> {
  label: string
  options: Option<T>[]
}

// 菜单项
export interface MenuItem {
  id: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
  hidden?: boolean
  badge?: string | number
  meta?: Record<string, any>
}

// 导航项
export interface NavItem {
  name: string
  path: string
  icon?: string
  active?: boolean
  children?: NavItem[]
}

// 面包屑
export interface Breadcrumb {
  label: string
  path?: string
  icon?: string
}

// 标签页
export interface Tab {
  key: string
  label: string
  icon?: string
  closable?: boolean
  disabled?: boolean
  content?: any
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  fixed?: 'left' | 'right'
  render?: (value: any, record: any, index: number) => any
}

// 表格配置
export interface TableConfig {
  columns: TableColumn[]
  data: any[]
  loading?: boolean
  pagination?: {
    total: number
    current: number
    pageSize: number
    showSizeChanger?: boolean
    showQuickJumper?: boolean
  }
  selection?: {
    type: 'checkbox' | 'radio'
    selectedRowKeys?: string[]
    onSelectionChange?: (selectedRowKeys: string[], selectedRows: any[]) => void
  }
}

// 表单字段类型
export interface FormField {
  name: string
  label?: string
  type: 'text' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'number' | 'email' | 'password'
  value?: any
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  options?: Option[]
  rules?: ValidationRule[]
  help?: string
  error?: string
}

// 验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
}

// 统计数据
export interface StatItem {
  label: string
  value: number | string
  unit?: string
  trend?: 'up' | 'down' | 'stable'
  percentage?: number
  color?: string
  icon?: string
}

// 通知消息
export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  closable?: boolean
  icon?: string
  actions?: NotificationAction[]
  createdAt: string
}

// 通知操作
export interface NotificationAction {
  label: string
  action: () => void
  type?: 'primary' | 'default' | 'danger'
}

// 加载状态
export interface LoadingState {
  loading: boolean
  error?: string
  message?: string
}

// 位置信息
export interface Location {
  country?: string
  province?: string
  city?: string
  district?: string
  address?: string
  latitude?: number
  longitude?: number
}

// 时间范围
export interface DateRange {
  start: string
  end: string
}

// 文件信息
export interface FileInfo {
  id: string
  name: string
  originalName: string
  size: number
  type: string
  url: string
  path: string
  uploadedAt: string
  uploadedBy?: string
}

// 颜色主题
export interface ColorTheme {
  primary: string
  secondary: string
  success: string
  warning: string
  error: string
  info: string
  background: string
  surface: string
  text: string
  border: string
}

// 设备信息
export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop'
  os: string
  browser: string
  screen: {
    width: number
    height: number
  }
  userAgent: string
}

// 权限信息
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  module: string
  actions: string[]
}

// 角色信息
export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: Permission[]
  isDefault?: boolean
}

// 键值对
export interface KeyValuePair<T = any> {
  key: string
  value: T
}

// 地理位置
export interface GeoLocation {
  latitude: number
  longitude: number
  accuracy?: number
  altitude?: number
  altitudeAccuracy?: number
  heading?: number
  speed?: number
  timestamp?: number
} 