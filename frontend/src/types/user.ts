// 用户角色类型
export type UserRole = 'developer' | 'admin' | 'client' | 'investor'

// 用户状态
export type UserStatus = 'active' | 'pending' | 'suspended'

// 技能水平
export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert'

// 用户基本信息
export interface User {
  id: number | string
  username: string
  email: string
  avatar?: string
  role: UserRole
  bio?: string
  location?: string
  githubUrl?: string
  portfolioUrl?: string
  createdAt?: string
  updatedAt?: string
  
  // 个人信息
  profile?: UserProfile
  
  // 统计数据
  stats?: UserStats
}

// 用户详细资料
export interface UserProfile {
  realName?: string
  bio?: string
  location?: string
  company?: string
  position?: string
  website?: string
  github?: string
  linkedin?: string
  
  // 技能
  skills: UserSkill[]
  
  // 偏好设置
  preferences: UserPreferences
  
  // 联系方式
  contact?: UserContact
}

// 用户技能
export interface UserSkill {
  id: string
  name: string
  level: SkillLevel
  category: string
  verified: boolean
  endorsements: number
  experience?: string
}

// 用户统计
export interface UserStats {
  projectsCompleted: number
  projectsInProgress: number
  totalEarnings: number
  rating: number
  reviewCount: number
  successRate: number
}

// 用户偏好设置
export interface UserPreferences {
  workType: string[] // 'remote', 'onsite', 'hybrid'
  projectType: string[] // 'frontend', 'backend', 'fullstack', 'mobile', 'design'
  budgetRange: [number, number]
  timeZone: string
  availability: string[] // 'weekdays', 'weekends', 'evenings'
  notifications: NotificationSettings
}

// 通知设置
export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
  
  // 具体通知类型
  projectUpdates: boolean
  messages: boolean
  marketing: boolean
  security: boolean
}

// 联系方式
export interface UserContact {
  phone?: string
  wechat?: string
  qq?: string
  telegram?: string
}

// 用户经历
export interface UserExperience {
  id: string
  type: 'education' | 'work' | 'project'
  title: string
  organization: string
  description?: string
  startDate: string
  endDate?: string
  current: boolean
  skills: string[]
}

// 用户认证信息
export interface UserVerification {
  emailVerified: boolean
  phoneVerified: boolean
  identityVerified: boolean
  skillsVerified: boolean
  verificationDate?: string
}

// 登录凭证
export interface LoginCredentials {
  email: string
  password: string
}

// 注册数据
export interface RegisterData {
  username?: string
  email: string
  password: string
  confirmPassword: string
  verificationCode: string
}

// 用户搜索筛选
export interface UserFilter {
  skills?: string[]
  location?: string
  rating?: number
  availability?: string[]
  experience?: SkillLevel
  verified?: boolean
} 