// 项目状态
export type ProjectStatus = 'draft' | 'published' | 'in_progress' | 'completed' | 'cancelled' | 'paused'

// 项目类型
export type ProjectType = 'web' | 'mobile' | 'desktop' | 'api' | 'blockchain' | 'ai' | 'game' | 'other'

// 项目优先级
export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent'

// 项目预算类型
export type BudgetType = 'fixed' | 'hourly' | 'milestone' | 'equity'

// 项目时长类型
export type DurationType = 'days' | 'weeks' | 'months' | 'years'

// 项目基本信息
export interface Project {
  id: string
  title: string
  description: string
  shortDescription: string
  
  // 项目状态
  status: ProjectStatus
  type: ProjectType
  priority: ProjectPriority
  
  // 创建者信息
  createdBy: string
  createdAt: string
  updatedAt: string
  
  // 项目详情
  details: ProjectDetails
  
  // 预算和时间
  budget: ProjectBudget
  duration: ProjectDuration
  
  // 技能要求
  requiredSkills: ProjectSkill[]
  
  // 项目成员
  team: ProjectMember[]
  
  // 项目阶段
  phases: ProjectPhase[]
  
  // 项目文件
  attachments: ProjectAttachment[]
  
  // 项目统计
  stats: ProjectStats
  
  // 项目标签
  tags: string[]
}

// 项目详细信息
export interface ProjectDetails {
  category: string
  subCategory?: string
  targetAudience?: string
  businessModel?: string
  
  // 技术栈
  techStack: string[]
  
  // 项目亮点
  highlights: string[]
  
  // 项目要求
  requirements: string[]
  
  // 项目目标
  objectives: string[]
  
  // 项目里程碑
  milestones: ProjectMilestone[]
}

// 项目预算
export interface ProjectBudget {
  type: BudgetType
  amount: number
  currency: string
  
  // 预算分配
  breakdown?: BudgetBreakdown[]
  
  // 付款计划
  paymentSchedule?: PaymentSchedule[]
}

// 预算分配
export interface BudgetBreakdown {
  category: string
  amount: number
  percentage: number
  description?: string
}

// 付款计划
export interface PaymentSchedule {
  phase: string
  amount: number
  dueDate: string
  description?: string
}

// 项目时长
export interface ProjectDuration {
  estimated: number
  type: DurationType
  startDate?: string
  endDate?: string
  flexible: boolean
}

// 项目技能要求
export interface ProjectSkill {
  name: string
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert'
  required: boolean
  category: string
  description?: string
}

// 项目成员
export interface ProjectMember {
  userId: string
  role: string
  joinDate: string
  status: 'active' | 'pending' | 'left'
  contribution: number
  permissions: string[]
}

// 项目阶段
export interface ProjectPhase {
  id: string
  name: string
  description: string
  startDate: string
  endDate?: string
  status: 'pending' | 'active' | 'completed' | 'cancelled'
  progress: number
  tasks: ProjectTask[]
}

// 项目任务
export interface ProjectTask {
  id: string
  title: string
  description?: string
  assignee?: string
  status: 'todo' | 'in_progress' | 'review' | 'completed'
  priority: ProjectPriority
  dueDate?: string
  estimatedHours?: number
  actualHours?: number
  tags: string[]
}

// 项目里程碑
export interface ProjectMilestone {
  id: string
  title: string
  description: string
  dueDate: string
  status: 'pending' | 'completed'
  value: number
  requirements: string[]
}

// 项目附件
export interface ProjectAttachment {
  id: string
  name: string
  type: string
  size: number
  url: string
  uploadedBy: string
  uploadedAt: string
}

// 项目统计
export interface ProjectStats {
  views: number
  likes: number
  applications: number
  shares: number
  bookmarks: number
  
  // 进度统计
  progress: number
  tasksCompleted: number
  totalTasks: number
  
  // 团队统计
  teamSize: number
  activeMembers: number
}

// 项目申请
export interface ProjectApplication {
  id: string
  projectId: string
  userId: string
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn'
  message: string
  proposedBudget?: number
  proposedDuration?: number
  coverLetter?: string
  portfolio?: string[]
  appliedAt: string
  respondedAt?: string
}

// 项目筛选
export interface ProjectFilter {
  category?: string[]
  type?: ProjectType[]
  budget?: {
    min?: number
    max?: number
    type?: BudgetType[]
  }
  duration?: {
    min?: number
    max?: number
    type?: DurationType
  }
  skills?: string[]
  status?: ProjectStatus[]
  priority?: ProjectPriority[]
  location?: string
  remote?: boolean
  tags?: string[]
  
  // 排序
  sortBy?: 'created' | 'updated' | 'budget' | 'duration' | 'popularity'
  sortOrder?: 'asc' | 'desc'
}

// 项目搜索结果
export interface ProjectSearchResult {
  projects: Project[]
  total: number
  page: number
  pageSize: number
  hasNext: boolean
  hasPrev: boolean
} 