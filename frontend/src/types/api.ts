// API 响应基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 分页响应
export interface PaginatedResponse<T = any> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 认证响应
export interface AuthResponse {
  success: boolean
  data: {
    user: any
    token: string
  }
  message: string
}

// 文件上传响应
export interface FileUploadResponse {
  url: string
  filename: string
  originalName: string
  size: number
  type: string
  path: string
}

// 请求配置
export interface RequestConfig {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
  withCredentials?: boolean
}

// API 错误类型
export interface ApiError {
  message: string
  code: number
  details?: any
  stack?: string
}

// HTTP 方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求参数类型
export interface RequestParams {
  [key: string]: any
}

// 查询参数类型
export interface QueryParams {
  page?: number
  pageSize?: number
  sort?: string
  order?: 'asc' | 'desc'
  search?: string
  [key: string]: any
} 