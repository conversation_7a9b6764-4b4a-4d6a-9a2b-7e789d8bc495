import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './assets/styles/index.css'
import { useUserStore } from './stores/user'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 初始化用户信息
const userStore = useUserStore()
if (userStore.token) {
  console.log('🔄 应用启动时检测到token，初始化用户信息...')
  userStore.initUser()
}

app.mount('#app') 