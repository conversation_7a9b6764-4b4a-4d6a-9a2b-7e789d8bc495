// 密码强度检查工具
export interface PasswordStrength {
  score: number; // 0-4，4为最强
  level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
  feedback: string[];
  color: string;
}

// 常见密码列表
const COMMON_PASSWORDS = [
  '123456', '123456789', 'qwerty', 'password', '12345678',
  '111111', '123123', 'admin', 'letmein', 'welcome',
  'monkey', '1234567890', 'dragon', 'sunshine', 'princess',
  'password123', '000000', 'qwerty123', 'abc123', 'password1',
  'iloveyou', 'welcome123', 'admin123', 'root', 'test',
  'guest', 'user', 'demo', 'sample', 'example'
];

// 检查密码强度
export function checkPasswordStrength(password: string): PasswordStrength {
  let score = 0;
  const feedback: string[] = [];

  // 基本长度检查
  if (password.length < 8) {
    feedback.push('密码至少需要8个字符');
    return {
      score: 0,
      level: 'weak',
      feedback,
      color: '#ef4444'
    };
  }

  // 长度加分
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;

  // 复杂度检查
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSymbol = /[@$!%*?&]/.test(password);

  if (hasLower) score += 1;
  if (hasUpper) score += 1;
  if (hasNumber) score += 1;
  if (hasSymbol) score += 1;

  // 常见密码检查
  if (COMMON_PASSWORDS.includes(password.toLowerCase())) {
    score = Math.max(0, score - 3);
    feedback.push('这是一个常见密码，建议使用更复杂的密码');
  }

  // 重复字符检查
  const hasRepeatingChars = /(.)\1{2,}/.test(password);
  if (hasRepeatingChars) {
    score = Math.max(0, score - 1);
    feedback.push('避免使用重复字符');
  }

  // 序列字符检查
  const hasSequence = /(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i.test(password);
  if (hasSequence) {
    score = Math.max(0, score - 1);
    feedback.push('避免使用连续字符');
  }

  // 生成建议
  if (!hasLower && !hasUpper) feedback.push('建议包含大小写字母');
  if (!hasNumber) feedback.push('建议包含数字');
  if (!hasSymbol) feedback.push('建议包含特殊字符(@$!%*?&)');

  // 确定强度等级
  let level: PasswordStrength['level'];
  let color: string;

  if (score <= 1) {
    level = 'weak';
    color = '#ef4444';
  } else if (score <= 2) {
    level = 'fair';
    color = '#f97316';
  } else if (score <= 3) {
    level = 'good';
    color = '#eab308';
  } else if (score <= 4) {
    level = 'strong';
    color = '#22c55e';
  } else {
    level = 'very-strong';
    color = '#16a34a';
  }

  return {
    score: Math.min(score, 4),
    level,
    feedback,
    color
  };
}

// 检查是否为常见密码
export function isCommonPassword(password: string): boolean {
  return COMMON_PASSWORDS.includes(password.toLowerCase());
}

// 密码强度文本
export function getPasswordStrengthText(level: PasswordStrength['level']): string {
  switch (level) {
    case 'weak': return '弱';
    case 'fair': return '一般';
    case 'good': return '良好';
    case 'strong': return '强';
    case 'very-strong': return '很强';
    default: return '未知';
  }
} 