// 前端验证器 - 与后端验证规则保持一致

// 密码验证规则：至少8位，包含大写字母、小写字母、数字和特殊符号
export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

// 邮箱验证规则
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

// 邮箱验证
export function validateEmail(email: string): ValidationResult {
  if (!email) {
    return { isValid: false, message: '邮箱是必填项' };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { isValid: false, message: '请输入有效的邮箱地址' };
  }
  
  return { isValid: true };
}

// 密码验证
export function validatePassword(password: string): ValidationResult {
  if (!password) {
    return { isValid: false, message: '密码是必填项' };
  }
  
  if (password.length < 8) {
    return { isValid: false, message: '密码至少8个字符' };
  }
  
  if (password.length > 128) {
    return { isValid: false, message: '密码不能超过128个字符' };
  }
  
  if (!PASSWORD_REGEX.test(password)) {
    return { 
      isValid: false, 
      message: '密码必须包含至少一个大写字母、一个小写字母、一个数字和一个特殊符号(@$!%*?&)' 
    };
  }
  
  return { isValid: true };
}

// 确认密码验证
export function validateConfirmPassword(password: string, confirmPassword: string): ValidationResult {
  if (!confirmPassword) {
    return { isValid: false, message: '确认密码是必填项' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, message: '确认密码与密码不匹配' };
  }
  
  return { isValid: true };
}

// 注册数据验证
export function validateRegistrationData(data: {
  email: string;
  password: string;
  confirmPassword: string;
}): {
  isValid: boolean;
  errors: { [key: string]: string };
} {
  const errors: { [key: string]: string } = {};
  
  // 验证邮箱
  const emailResult = validateEmail(data.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.message!;
  }
  
  // 验证密码
  const passwordResult = validatePassword(data.password);
  if (!passwordResult.isValid) {
    errors.password = passwordResult.message!;
  }
  
  // 验证确认密码
  const confirmPasswordResult = validateConfirmPassword(data.password, data.confirmPassword);
  if (!confirmPasswordResult.isValid) {
    errors.confirmPassword = confirmPasswordResult.message!;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// 登录数据验证
export function validateLoginData(data: {
  email: string;
  password: string;
}): {
  isValid: boolean;
  errors: { [key: string]: string };
} {
  const errors: { [key: string]: string } = {};
  
  // 验证邮箱
  const emailResult = validateEmail(data.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.message!;
  }
  
  // 验证密码不为空
  if (!data.password) {
    errors.password = '密码是必填项';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

// 修改密码数据验证
export function validatePasswordChangeData(data: {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}): {
  isValid: boolean;
  errors: { [key: string]: string };
} {
  const errors: { [key: string]: string } = {};
  
  // 验证当前密码
  if (!data.currentPassword) {
    errors.currentPassword = '当前密码是必填项';
  }
  
  // 验证新密码
  const newPasswordResult = validatePassword(data.newPassword);
  if (!newPasswordResult.isValid) {
    errors.newPassword = newPasswordResult.message!;
  }
  
  // 验证确认新密码
  const confirmNewPasswordResult = validateConfirmPassword(data.newPassword, data.confirmNewPassword);
  if (!confirmNewPasswordResult.isValid) {
    errors.confirmNewPassword = confirmNewPasswordResult.message!;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}