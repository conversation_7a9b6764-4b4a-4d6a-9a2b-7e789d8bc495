/**
 * 微信支付工具类
 * 处理微信支付相关的业务逻辑
 */

import { http } from './request'

// 微信支付相关接口类型定义
export interface WechatPayOrderRequest {
  amount: number
  description: string
  orderType: 'certification' | 'contact_view' | 'other'
  userId?: number
  metadata?: Record<string, any>
}

export interface WechatPayOrderResponse {
  success: boolean
  data: {
    orderId: string
    qrCodeUrl: string
    prepayId: string
    codeUrl: string
    orderStatus: 'NOTPAY' | 'SUCCESS' | 'REFUND' | 'CLOSED' | 'REVOKED' | 'USERPAYING' | 'PAYERROR'
    expiresAt: string
  }
  message?: string
}

export interface PaymentStatusResponse {
  success: boolean
  data: {
    orderId: string
    status: 'NOTPAY' | 'SUCCESS' | 'REFUND' | 'CLOSED' | 'REVOKED' | 'USERPAYING' | 'PAYERROR'
    transactionId?: string
    paidAt?: string
    amount: number
  }
  message?: string
}

export class WechatPayService {
  private static instance: WechatPayService
  
  // 微信支付配置
  private readonly config = {
    merchantId: '1624570874', // 您提供的商户号
    appId: '', // 需要配置
    apiKey: '', // 需要配置
    apiV3Key: '', // 需要配置
    certSerialNo: '', // 需要配置
    privateKey: '', // 需要配置
    notifyUrl: `${window.location.origin}/api/wechat/notify`, // 支付结果通知地址
    baseUrl: 'https://api.mch.weixin.qq.com', // 微信支付API基础地址
  }

  public static getInstance(): WechatPayService {
    if (!WechatPayService.instance) {
      WechatPayService.instance = new WechatPayService()
    }
    return WechatPayService.instance
  }

  /**
   * 创建支付订单
   * @param orderData 订单数据
   * @returns 订单创建结果
   */
  public async createPayOrder(orderData: WechatPayOrderRequest): Promise<WechatPayOrderResponse> {
    try {
      console.log('创建微信支付订单:', orderData)
      
      // 调用后端API创建订单
      const response = await http.post('/payment/wechat/create', {
        amount: Math.round(orderData.amount * 100), // 转换为分
        description: orderData.description,
        orderType: orderData.orderType,
        userId: orderData.userId,
        metadata: orderData.metadata,
        notifyUrl: this.config.notifyUrl,
        merchantId: this.config.merchantId
      })

      if (response.success) {
        return {
          success: true,
          data: {
            orderId: response.data.orderId,
            qrCodeUrl: response.data.qrCodeUrl,
            prepayId: response.data.prepayId,
            codeUrl: response.data.codeUrl,
            orderStatus: response.data.orderStatus || 'NOTPAY',
            expiresAt: response.data.expiresAt
          }
        }
      } else {
        throw new Error(response.message || '创建订单失败')
      }
    } catch (error) {
      console.error('创建微信支付订单失败:', error)
      return {
        success: false,
        data: {
          orderId: '',
          qrCodeUrl: '',
          prepayId: '',
          codeUrl: '',
          orderStatus: 'PAYERROR',
          expiresAt: ''
        },
        message: error instanceof Error ? error.message : '创建订单失败'
      }
    }
  }

  /**
   * 查询支付状态
   * @param orderId 订单ID
   * @returns 支付状态查询结果
   */
  public async queryPaymentStatus(orderId: string): Promise<PaymentStatusResponse> {
    try {
      console.log('查询支付状态:', orderId)
      
      const response = await http.get(`/payment/wechat/query/${orderId}`)
      
      if (response.success) {
        return {
          success: true,
          data: {
            orderId: response.data.orderId,
            status: response.data.status,
            transactionId: response.data.transactionId,
            paidAt: response.data.paidAt,
            amount: response.data.amount
          }
        }
      } else {
        throw new Error(response.message || '查询支付状态失败')
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
      return {
        success: false,
        data: {
          orderId,
          status: 'PAYERROR',
          amount: 0
        },
        message: error instanceof Error ? error.message : '查询支付状态失败'
      }
    }
  }

  /**
   * 取消订单
   * @param orderId 订单ID
   * @returns 取消结果
   */
  public async cancelOrder(orderId: string): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('取消订单:', orderId)
      
      const response = await http.post(`/payment/wechat/cancel/${orderId}`)
      
      return {
        success: response.success,
        message: response.message
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '取消订单失败'
      }
    }
  }

  /**
   * 生成二维码URL
   * @param codeUrl 微信支付返回的code_url
   * @returns 二维码图片URL
   */
  public generateQRCodeUrl(codeUrl: string): string {
    // 使用QR码生成服务，这里使用免费的API
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(codeUrl)}`
  }

  /**
   * 轮询支付状态
   * @param orderId 订单ID
   * @param onStatusChange 状态变化回调
   * @param maxAttempts 最大轮询次数
   * @param interval 轮询间隔(毫秒)
   */
  public async pollPaymentStatus(
    orderId: string,
    onStatusChange: (status: PaymentStatusResponse) => void,
    maxAttempts: number = 60,
    interval: number = 2000
  ): Promise<void> {
    let attempts = 0
    
    const poll = async () => {
      if (attempts >= maxAttempts) {
        console.log('支付状态轮询达到最大次数')
        onStatusChange({
          success: false,
          data: { orderId, status: 'CLOSED', amount: 0 },
          message: '支付超时'
        })
        return
      }

      attempts++
      
      try {
        const result = await this.queryPaymentStatus(orderId)
        onStatusChange(result)
        
        // 如果支付成功或失败，停止轮询
        if (result.success && ['SUCCESS', 'REFUND', 'CLOSED', 'REVOKED', 'PAYERROR'].includes(result.data.status)) {
          return
        }
        
        // 继续轮询
        setTimeout(poll, interval)
      } catch (error) {
        console.error('轮询支付状态失败:', error)
        setTimeout(poll, interval)
      }
    }
    
    // 开始轮询
    poll()
  }

  /**
   * 格式化金额显示
   * @param amount 金额（元）
   * @returns 格式化后的金额字符串
   */
  public formatAmount(amount: number): string {
    return `¥${amount.toFixed(2)}`
  }

  /**
   * 验证订单金额
   * @param amount 金额
   * @returns 是否有效
   */
  public validateAmount(amount: number): boolean {
    return amount > 0 && amount <= 100000 && Number.isFinite(amount)
  }
}

// 导出单例实例
export const wechatPayService = WechatPayService.getInstance()

// 支付状态常量
export const PAYMENT_STATUS = {
  NOTPAY: 'NOTPAY',       // 未支付
  SUCCESS: 'SUCCESS',      // 支付成功
  REFUND: 'REFUND',       // 转入退款
  CLOSED: 'CLOSED',       // 已关闭
  REVOKED: 'REVOKED',     // 已撤销（刷卡支付）
  USERPAYING: 'USERPAYING', // 用户支付中
  PAYERROR: 'PAYERROR'    // 支付失败
} as const

// 订单类型常量
export const ORDER_TYPE = {
  CERTIFICATION: 'certification',   // 简历认证
  CONTACT_VIEW: 'contact_view',    // 联系方式查看
  OTHER: 'other'                   // 其他
} as const