import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse, RequestConfig } from '@/types/api'

// 创建axios实例
const createAxiosInstance = (config?: RequestConfig): AxiosInstance => {
  const instance = axios.create({
    baseURL: config?.baseURL || import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: config?.timeout || 10000,
    headers: {
      'Content-Type': 'application/json',
      ...config?.headers
    },
    withCredentials: config?.withCredentials || true
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 添加认证token
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      
      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId()
      
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const data = response.data
      console.log('📡 HTTP响应:', { url: response.config.url, status: response.status, data })
      
      // 如果是标准的ApiResponse格式
      if (data && typeof data === 'object' && 'success' in data) {
        if (!data.success) {
          console.error('❌ API返回失败:', data)
          return Promise.reject(new Error(data.message || data.error || '请求失败'))
        }
        // 对于认证接口，返回完整的响应对象
        if (response.config.url?.includes('/auth/')) {
          console.log('🔐 认证API响应，返回完整对象:', data)
          return data
        }
        // 对于所有成功的API响应，返回完整的响应对象，不要只返回data部分
        console.log('📊 标准API响应，返回完整对象:', data)
        return data
      }
      
      return data
    },
    (error) => {
      // 处理不同类型的错误
      if (error.response) {
        const { status, data } = error.response
        
        switch (status) {
          case 401:
            // 对于登录和注册接口，不要自动清除token和跳转
            if (error.response.config.url?.includes('/auth/login') || 
                error.response.config.url?.includes('/auth/register')) {
              console.log('🔐 登录/注册请求401错误，不自动跳转')
              break
            }
            // 对于其他接口的401错误，清除token并跳转到登录页
            console.log('🔐 Token过期或无效，清除登录状态')
            localStorage.removeItem('token')
            // 使用事件通知用户状态变化，避免直接跳转
            window.dispatchEvent(new CustomEvent('auth-expired'))
            break
          case 403:
            // 无权限
            console.error('无权限访问')
            break
          case 404:
            // 资源不存在
            console.error('资源不存在')
            break
          case 422:
            // 验证错误
            console.error('验证错误:', data.message)
            break
          case 500:
            // 服务器错误
            console.error('服务器错误')
            break
          default:
            console.error('请求错误:', data?.message || error.message)
        }
        
        // 返回更详细的错误信息
        const errorMessage = data?.message || data?.error || '请求失败'
        const errorWithResponse = new Error(errorMessage) as any
        errorWithResponse.response = error.response
        return Promise.reject(errorWithResponse)
      } else if (error.request) {
        // 网络错误
        console.error('网络错误:', error.message)
        return Promise.reject(new Error('网络连接失败'))
      } else {
        // 其他错误
        console.error('请求配置错误:', error.message)
        return Promise.reject(error)
      }
    }
  )

  return instance
}

// 生成请求ID
const generateRequestId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 默认axios实例
const request = createAxiosInstance()

// 通用请求方法
export const http = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return request.get(url, config)
  },
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.post(url, data, config)
  },
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.put(url, data, config)
  },
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {
    return request.delete(url, config)
  },
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
    return request.patch(url, data, config)
  },
  
  // 上传文件
  upload: <T = any>(url: string, file: File, config?: AxiosRequestConfig): Promise<T> => {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers
      }
    })
  },
  
  // 下载文件
  download: (url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> => {
    return request.get(url, {
      ...config,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

export default request
export { createAxiosInstance } 