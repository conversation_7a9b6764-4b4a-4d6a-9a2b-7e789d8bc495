import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User } from '@/types/user'
import { authApi } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')

  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      try {
        loading.value = true
        const userData = await authApi.getProfile()
        user.value = userData
        console.log('✅ 用户信息初始化成功:', userData?.username)
        return userData
      } catch (error: any) {
        // 静默处理初始化失败，通常是token过期或无效
        console.log('🔄 用户信息初始化失败，清除无效token:', error?.message)
        logout()
        return null
      } finally {
        loading.value = false
      }
    }
    return null
  }

  // 登录
  const login = async (credentials: { email: string; password: string }) => {
    try {
      loading.value = true
      console.log('🔐 开始登录请求:', credentials.email)
      
      const response = await authApi.login(credentials)
      console.log('✅ 登录API响应:', response)
      
      // 检查响应结构
      if (!response) {
        throw new Error('服务器无响应')
      }
      
      if (!response.success) {
        throw new Error(response.message || '登录失败')
      }
      
      if (!response.data) {
        throw new Error('服务器响应格式错误：缺少data字段')
      }
      
      if (!response.data.token) {
        throw new Error('服务器响应格式错误：缺少token字段')
      }
      
      if (!response.data.user) {
        throw new Error('服务器响应格式错误：缺少user字段')
      }
      
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', response.data.token)
      
      console.log('✅ 登录成功，用户:', user.value?.username)
      return response
    } catch (error: any) {
      console.error('❌ 登录失败详情:', error)
      
      // 提供用户友好的错误信息
      let userErrorMessage = '登录失败，请稍后重试'
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 401) {
          userErrorMessage = '邮箱或密码错误，请检查后重试'
        } else if (status === 400) {
          userErrorMessage = data?.message || '请求参数错误'
        } else if (status === 429) {
          userErrorMessage = '登录尝试过于频繁，请稍后再试'
        } else if (status >= 500) {
          userErrorMessage = '服务器暂时无法处理请求，请稍后重试'
        } else {
          userErrorMessage = data?.message || userErrorMessage
        }
      } else if (error.message) {
        userErrorMessage = error.message
      }
      
      throw new Error(userErrorMessage)
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (userData: {
    email: string
    password: string
    confirmPassword: string
    verificationCode: string
  }) => {
    try {
      loading.value = true
      console.log('📝 开始注册请求:', userData.email)
      
      const response = await authApi.register(userData)
      console.log('✅ 注册API响应:', response)
      
      // 检查响应结构
      if (!response) {
        throw new Error('服务器无响应')
      }
      
      if (!response.success) {
        throw new Error(response.message || '注册失败')
      }
      
      if (!response.data) {
        throw new Error('服务器响应格式错误：缺少data字段')
      }
      
      if (!response.data.token) {
        throw new Error('服务器响应格式错误：缺少token字段')
      }
      
      if (!response.data.user) {
        throw new Error('服务器响应格式错误：缺少user字段')
      }
      
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', response.data.token)
      
      console.log('✅ 注册成功，用户:', user.value?.username)
      return response
    } catch (error: any) {
      console.error('❌ 注册失败详情:', error)
      
      // 提供用户友好的错误信息
      let userErrorMessage = '注册失败，请稍后重试'
      
      if (error.response) {
        const status = error.response.status
        const data = error.response.data
        
        if (status === 409) {
          userErrorMessage = '该邮箱已被注册，请使用其他邮箱'
        } else if (status === 400) {
          userErrorMessage = data?.message || '请求参数错误'
        } else if (status === 422) {
          userErrorMessage = data?.message || '输入信息格式错误'
        } else if (status >= 500) {
          userErrorMessage = '服务器暂时无法处理请求，请稍后重试'
        } else {
          userErrorMessage = data?.message || userErrorMessage
        }
      } else if (error.message) {
        userErrorMessage = error.message
      }
      
      throw new Error(userErrorMessage)
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem('token')
    console.log('🔒 用户已登出')
  }

  // 监听token过期事件
  const handleAuthExpired = () => {
    console.log('🔐 检测到token过期，自动登出')
    logout()
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<User>) => {
    try {
      loading.value = true
      const updatedUser = await authApi.updateProfile(profileData)
      user.value = updatedUser
      return updatedUser
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    user,
    token,
    loading,
    isAuthenticated,
    isAdmin,
    initUser,
    login,
    register,
    logout,
    updateProfile,
    handleAuthExpired
  }
}) 