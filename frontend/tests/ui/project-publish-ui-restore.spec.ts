import { test, expect } from '@playwright/test'

test.describe('项目发布页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/project-publish')
    await page.waitForSelector('.wizard-container')
  })

  test('页面整体布局验证', async ({ page }) => {
    // 验证背景渐变
    const body = page.locator('.project-publish-page')
    await expect(body).toHaveCSS('background', /linear-gradient.*667eea.*764ba2/)
    
    // 验证容器样式
    const container = page.locator('.wizard-container')
    await expect(container).toHaveCSS('background-color', 'rgb(255, 255, 255)')
    await expect(container).toHaveCSS('border-radius', '20px')
    await expect(container).toHaveCSS('box-shadow', /0px 20px 60px/)
    
    // 验证最大宽度
    await expect(container).toHaveCSS('max-width', '900px')
  })

  test('进度指示器样式验证', async ({ page }) => {
    // 验证步骤圆圈尺寸
    const stepCircle = page.locator('.step-circle').first()
    await expect(stepCircle).toHaveCSS('width', '50px')
    await expect(stepCircle).toHaveCSS('height', '50px')
    await expect(stepCircle).toHaveCSS('border-radius', '50%')
    await expect(stepCircle).toHaveCSS('border-width', '3px')
    
    // 验证激活状态颜色
    const activeStep = page.locator('.step.active .step-circle')
    await expect(activeStep).toHaveCSS('background-color', 'rgb(102, 126, 234)') // #667eea
    await expect(activeStep).toHaveCSS('border-color', 'rgb(102, 126, 234)')
    await expect(activeStep).toHaveCSS('color', 'rgb(255, 255, 255)')
    
    // 验证连接线样式
    const stepLine = page.locator('.step:not(:last-child)::after').first()
    await expect(stepLine).toHaveCSS('height', '2px')
    await expect(stepLine).toHaveCSS('background-color', 'rgb(102, 126, 234)')
    
    // 验证步骤标题样式
    const stepTitle = page.locator('.step-title').first()
    await expect(stepTitle).toHaveCSS('font-size', '14px')
    await expect(stepTitle).toHaveCSS('font-weight', '600')
  })

  test('表单元素样式验证', async ({ page }) => {
    // 验证输入框样式
    const input = page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')
    await expect(input).toHaveCSS('padding', '12px')
    await expect(input).toHaveCSS('border-width', '2px')
    await expect(input).toHaveCSS('border-radius', '8px')
    await expect(input).toHaveCSS('border-color', 'rgb(203, 213, 225)') // #cbd5e1
    
    // 验证焦点状态
    await input.focus()
    await expect(input).toHaveCSS('border-color', 'rgb(102, 126, 234)') // #667eea
    await expect(input).toHaveCSS('box-shadow', /0px 0px 0px 2px.*rgba\(102, 126, 234, 0\.2\)/)
    
    // 验证标签样式
    const label = page.locator('.form-label').first()
    await expect(label).toHaveCSS('font-size', '14px')
    await expect(label).toHaveCSS('font-weight', '500')
    await expect(label).toHaveCSS('color', 'rgb(51, 65, 85)') // #334155
    
    // 验证提示文字样式
    const hint = page.locator('.form-hint').first()
    await expect(hint).toHaveCSS('font-size', '12px')
    await expect(hint).toHaveCSS('color', 'rgb(107, 114, 128)') // #6b7280
  })

  test('按钮样式验证', async ({ page }) => {
    // 验证主按钮样式
    const primaryBtn = page.locator('.btn-primary')
    await expect(primaryBtn).toHaveCSS('background-color', 'rgb(102, 126, 234)') // #667eea
    await expect(primaryBtn).toHaveCSS('color', 'rgb(255, 255, 255)')
    await expect(primaryBtn).toHaveCSS('border-radius', '8px')
    await expect(primaryBtn).toHaveCSS('padding', '12px 24px')
    await expect(primaryBtn).toHaveCSS('font-size', '14px')
    await expect(primaryBtn).toHaveCSS('font-weight', '500')
    
    // 验证按钮悬停效果
    await primaryBtn.hover()
    await expect(primaryBtn).toHaveCSS('background-color', 'rgb(90, 103, 216)') // #5a67d8
    
    // 验证禁用状态
    if (await primaryBtn.isDisabled()) {
      await expect(primaryBtn).toHaveCSS('opacity', '0.5')
    }
  })

  test('错误状态样式验证', async ({ page }) => {
    // 触发错误状态
    const titleInput = page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')
    await titleInput.fill('短')
    await titleInput.blur()
    
    // 验证错误输入框样式
    await expect(titleInput).toHaveCSS('border-color', 'rgb(239, 68, 68)') // #ef4444
    
    // 验证错误消息样式
    const errorMsg = page.locator('.error-message')
    await expect(errorMsg).toHaveCSS('color', 'rgb(239, 68, 68)') // #ef4444
    await expect(errorMsg).toHaveCSS('font-size', '12px')
    await expect(errorMsg).toHaveCSS('margin-top', '4px')
  })

  test('响应式设计验证', async ({ page }) => {
    // 桌面端验证
    await page.setViewportSize({ width: 1200, height: 800 })
    
    const container = page.locator('.wizard-container')
    await expect(container).toHaveCSS('max-width', '900px')
    
    // 验证表单网格布局
    const formGrid = page.locator('.form-grid')
    await expect(formGrid).toHaveCSS('display', 'grid')
    await expect(formGrid).toHaveCSS('grid-template-columns', 'repeat(2, minmax(0, 1fr))')
    
    // 移动端验证
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 验证移动端步骤指示器
    const progressSteps = page.locator('.progress-steps')
    await expect(progressSteps).toHaveCSS('flex-direction', 'column')
    
    // 验证移动端表单网格
    await expect(formGrid).toHaveCSS('grid-template-columns', 'repeat(1, minmax(0, 1fr))')
  })

  test('动画效果验证', async ({ page }) => {
    // 验证步骤内容过渡动画
    const stepContent = page.locator('.step-content.active')
    await expect(stepContent).toHaveCSS('opacity', '1')
    await expect(stepContent).toHaveCSS('transform', 'translateX(0px)')
    await expect(stepContent).toHaveCSS('transition-duration', '0.3s')
    
    // 验证按钮悬停过渡
    const primaryBtn = page.locator('.btn-primary')
    await expect(primaryBtn).toHaveCSS('transition-duration', '0.2s')
    await expect(primaryBtn).toHaveCSS('transition-property', 'all')
  })

  test('颜色主题一致性验证', async ({ page }) => {
    // 验证主色调使用
    const primaryColor = 'rgb(102, 126, 234)' // #667eea
    const hoverColor = 'rgb(90, 103, 216)' // #5a67d8
    
    // 激活步骤
    await expect(page.locator('.step.active .step-circle')).toHaveCSS('background-color', primaryColor)
    
    // 主按钮
    await expect(page.locator('.btn-primary')).toHaveCSS('background-color', primaryColor)
    
    // 焦点状态
    const input = page.locator('input').first()
    await input.focus()
    await expect(input).toHaveCSS('border-color', primaryColor)
    
    // 连接线
    const stepLine = page.locator('.step.active::after').first()
    await expect(stepLine).toHaveCSS('background-color', primaryColor)
  })

  test('字体规范验证', async ({ page }) => {
    // 验证标题字体
    const progressTitle = page.locator('.progress-title')
    await expect(progressTitle).toHaveCSS('font-size', '28px')
    await expect(progressTitle).toHaveCSS('font-weight', '700')
    
    // 验证步骤标题字体
    const stepTitle = page.locator('.step-title')
    await expect(stepTitle).toHaveCSS('font-size', '32px')
    await expect(stepTitle).toHaveCSS('font-weight', '700')
    
    // 验证描述字体
    const stepDesc = page.locator('.step-description')
    await expect(stepDesc).toHaveCSS('font-size', '18px')
    await expect(stepDesc).toHaveCSS('color', 'rgb(100, 116, 139)') // #64748b
    
    // 验证表单标签字体
    const formLabel = page.locator('.form-label')
    await expect(formLabel).toHaveCSS('font-size', '14px')
    await expect(formLabel).toHaveCSS('font-weight', '500')
  })

  test('间距和布局验证', async ({ page }) => {
    // 验证容器内边距
    const formContainer = page.locator('.form-container')
    await expect(formContainer).toHaveCSS('padding', '32px')
    
    // 验证表单组间距
    const formGroup = page.locator('.form-group').first()
    await expect(formGroup).toHaveCSS('margin-bottom', '24px')
    
    // 验证按钮区域间距
    const formActions = page.locator('.form-actions')
    await expect(formActions).toHaveCSS('margin-top', '40px')
    await expect(formActions).toHaveCSS('padding-top', '30px')
    
    // 验证步骤间距
    const progressSteps = page.locator('.progress-steps')
    await expect(progressSteps).toHaveCSS('margin-bottom', '24px')
  })

  test('阴影效果验证', async ({ page }) => {
    // 验证主容器阴影
    const container = page.locator('.wizard-container')
    await expect(container).toHaveCSS('box-shadow', '0px 20px 60px rgba(0, 0, 0, 0.1)')
    
    // 验证输入框焦点阴影
    const input = page.locator('input').first()
    await input.focus()
    await expect(input).toHaveCSS('box-shadow', /0px 0px 0px 2px rgba\(102, 126, 234, 0\.2\)/)
    
    // 验证按钮悬停阴影
    const primaryBtn = page.locator('.btn-primary')
    await primaryBtn.hover()
    await expect(primaryBtn).toHaveCSS('box-shadow', /0px 4px 12px rgba\(102, 126, 234, 0\.3\)/)
  })

  test('可访问性验证', async ({ page }) => {
    // 验证必填字段标识
    const requiredFields = page.locator('.required')
    await expect(requiredFields.first()).toHaveCSS('color', 'rgb(239, 68, 68)') // #ef4444
    
    // 验证标签与输入框关联
    const labels = page.locator('.form-label')
    const inputs = page.locator('.form-input')
    
    for (let i = 0; i < await labels.count(); i++) {
      const label = labels.nth(i)
      const input = inputs.nth(i)
      
      // 验证标签文本存在
      await expect(label).not.toBeEmpty()
      
      // 验证输入框有placeholder
      await expect(input).toHaveAttribute('placeholder')
    }
    
    // 验证错误消息的可访问性
    const titleInput = page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')
    await titleInput.fill('短')
    await titleInput.blur()
    
    const errorMsg = page.locator('.error-message')
    await expect(errorMsg).toBeVisible()
    await expect(errorMsg).toHaveAttribute('role', 'alert')
  })
})

test.describe('视觉回归测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/project-publish')
    await page.waitForSelector('.wizard-container')
  })

  test('第一步页面截图对比', async ({ page }) => {
    // 等待动画完成
    await page.waitForTimeout(500)
    
    // 截图对比
    await expect(page.locator('.wizard-container')).toHaveScreenshot('step-1-layout.png')
  })

  test('填写表单状态截图对比', async ({ page }) => {
    // 填写部分表单
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '创新AI项目')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', 'AI助手平台')
    
    await expect(page.locator('.wizard-container')).toHaveScreenshot('step-1-filled.png')
  })

  test('错误状态截图对比', async ({ page }) => {
    // 触发错误状态
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '短')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    
    await expect(page.locator('.wizard-container')).toHaveScreenshot('step-1-error.png')
  })

  test('第二步页面截图对比', async ({ page }) => {
    // 填写第一步并进入第二步
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '测试项目')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '测试概括')
    await page.selectOption('select:first-of-type', 'web')
    await page.selectOption('select:nth-of-type(2)', '10000-50000')
    await page.fill('textarea[placeholder*="详细描述你的项目"]', 
      '这是一个测试项目描述，包含了足够的字符数量来满足最少50个字符的要求。')
    
    await page.click('.btn-primary')
    await page.waitForTimeout(500)
    
    await expect(page.locator('.wizard-container')).toHaveScreenshot('step-2-layout.png')
  })

  test('移动端布局截图对比', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    await expect(page.locator('.wizard-container')).toHaveScreenshot('mobile-layout.png')
  })
}) 