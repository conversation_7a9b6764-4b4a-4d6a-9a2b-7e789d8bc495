import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import Projects from '@/views/Projects.vue'
import * as projectApi from '@/api/project'

// Mock API
vi.mock('@/api/project', () => ({
  getProjects: vi.fn(),
  getFeaturedProjects: vi.fn(),
  applyToProject: vi.fn()
}))

// Mock 路由
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/projects', component: Projects },
    { path: '/project/:id', component: { template: '<div>Project Detail</div>' } }
  ]
})

// Mock 项目数据
const mockProjectsResponse = {
  data: {
    projects: [
      {
        id: 1,
        title: '测试项目1',
        description: '这是一个测试项目',
        tagline: '测试项目简介',
        category: 'web',
        reviewStatus: 'approved',
        requirements: [
          {
            role: '前端开发工程师',
            skillName: ['Vue.js', 'TypeScript'],
            cooperation: 'salary',
            salaryAmount: '15000'
          }
        ],
        workArrangement: 'partTime',
        location: '远程',
        timeframe: '3个月',
        budget: 15000,
        currentMilestone: 'development',
        createdAt: '2024-01-01T00:00:00.000Z',
        applicants: []
      },
      {
        id: 2,
        title: '测试项目2',
        description: '这是另一个测试项目',
        tagline: '免费项目',
        category: 'mobile',
        reviewStatus: 'approved',
        requirements: [
          {
            role: 'iOS开发工程师',
            skillName: ['Swift', 'iOS'],
            cooperation: 'equity',
            salaryAmount: ''
          }
        ],
        workArrangement: 'fullTime',
        location: '北京',
        timeframe: '6个月',
        budget: 0,
        currentMilestone: 'team-building',
        createdAt: '2024-01-02T00:00:00.000Z',
        applicants: []
      }
    ],
    total: 2
  }
}

describe('Projects 组件', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 设置 API mock 返回值
    vi.mocked(projectApi.getProjects).mockResolvedValue(mockProjectsResponse)
    vi.mocked(projectApi.getFeaturedProjects).mockResolvedValue({ data: [] })
  })

  it('应该正确加载和显示项目列表', async () => {
    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    // 等待组件挂载完成
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 验证 API 被调用
    expect(projectApi.getProjects).toHaveBeenCalled()
    
    // 验证项目数据被正确转换和显示
    expect(wrapper.vm.projects).toHaveLength(2)
    expect(wrapper.vm.projects[0].title).toBe('测试项目1')
    expect(wrapper.vm.projects[0].type).toBe('paid') // 因为有salary
    expect(wrapper.vm.projects[1].type).toBe('free') // 因为是equity
  })

  it('应该正确过滤免费区和付费区项目', async () => {
    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 测试默认显示免费区
    expect(wrapper.vm.activeTab).toBe('free')
    expect(wrapper.vm.filteredProjects).toHaveLength(1)
    expect(wrapper.vm.filteredProjects[0].title).toBe('测试项目2')

    // 切换到付费区
    wrapper.vm.switchTab('paid')
    expect(wrapper.vm.filteredProjects).toHaveLength(1)
    expect(wrapper.vm.filteredProjects[0].title).toBe('测试项目1')
  })

  it('应该能够点击项目卡片跳转到详情页', async () => {
    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    const pushSpy = vi.spyOn(router, 'push')

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 模拟点击项目卡片
    const project = wrapper.vm.projects[0]
    wrapper.vm.goToProjectDetail(project)

    expect(pushSpy).toHaveBeenCalledWith('/project/1')
  })

  it('应该能够申请项目', async () => {
    // Mock window.prompt
    const originalPrompt = window.prompt
    window.prompt = vi.fn().mockReturnValue('我很感兴趣这个项目')

    // Mock window.alert
    const originalAlert = window.alert
    window.alert = vi.fn()

    vi.mocked(projectApi.applyToProject).mockResolvedValue({ success: true })

    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 模拟申请项目
    const project = wrapper.vm.projects[0]
    const mockEvent = { stopPropagation: vi.fn() } as any
    
    await wrapper.vm.applyForProject(project, mockEvent)

    // 验证 API 被调用
    expect(projectApi.applyToProject).toHaveBeenCalledWith(1, {
      message: '我很感兴趣这个项目',
      expectedSalary: 15000,
      coverLetter: '感谢您考虑我的申请！'
    })

    // 验证成功提示
    expect(window.alert).toHaveBeenCalledWith('申请提交成功！项目负责人会尽快回复您。')

    // 验证事件冒泡被阻止
    expect(mockEvent.stopPropagation).toHaveBeenCalled()

    // 恢复原始函数
    window.prompt = originalPrompt
    window.alert = originalAlert
  })

  it('应该处理API错误', async () => {
    // Mock API 返回错误
    vi.mocked(projectApi.getProjects).mockRejectedValue(new Error('网络错误'))

    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 验证错误状态
    expect(wrapper.vm.error).toBe('加载项目失败，请稍后重试')
    expect(wrapper.vm.loading).toBe(false)
  })

  it('应该只显示已审核通过的项目', async () => {
    // 添加一个未审核通过的项目
    const responseWithPending = {
      ...mockProjectsResponse,
      data: {
        ...mockProjectsResponse.data,
        projects: [
          ...mockProjectsResponse.data.projects,
          {
            id: 3,
            title: '待审核项目',
            reviewStatus: 'pending',
            // ... 其他字段
          }
        ]
      }
    }
    
    vi.mocked(projectApi.getProjects).mockResolvedValue(responseWithPending)

    const wrapper = mount(Projects, {
      global: {
        plugins: [router]
      }
    })

    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))

    // 验证只显示已审核通过的项目
    expect(wrapper.vm.projects).toHaveLength(2)
    expect(wrapper.vm.projects.find(p => p.title === '待审核项目')).toBeUndefined()
  })
}) 