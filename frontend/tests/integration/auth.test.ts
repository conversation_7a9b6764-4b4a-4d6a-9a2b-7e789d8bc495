import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import Auth from '@/views/Auth.vue'
import { useUserStore } from '@/stores/user'

// 模拟HTTP请求
vi.mock('@/utils/request', () => ({
  http: {
    post: vi.fn()
  }
}))

describe('Authentication Integration', () => {
  let wrapper: any
  let router: any
  let userStore: any

  beforeEach(async () => {
    // 设置Pinia
    const pinia = createPinia()
    setActivePinia(pinia)
    userStore = useUserStore()

    // 设置路由
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
        { path: '/auth', name: 'auth', component: Auth },
        { path: '/projects', name: 'projects', component: { template: '<div>Projects</div>' } }
      ]
    })

    await router.push('/auth')
    await router.isReady()

    wrapper = mount(Auth, {
      global: {
        plugins: [router, pinia]
      }
    })
  })

  describe('Login Flow', () => {
    it('should render login form by default', () => {
      expect(wrapper.find('[data-testid="login-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="email-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="password-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="login-button"]').exists()).toBe(true)
    })

    it('should validate required fields', async () => {
      const loginButton = wrapper.find('[data-testid="login-button"]')
      await loginButton.trigger('click')

      // 检查验证错误消息
      expect(wrapper.find('.error-message').exists()).toBe(true)
    })

    it('should submit login form with valid data', async () => {
      const { http } = await import('@/utils/request')
      vi.mocked(http.post).mockResolvedValue({
        data: {
          token: 'fake-jwt-token',
          user: { id: 1, email: '<EMAIL>', name: 'Test User' }
        }
      })

      // 填写表单
      const emailInput = wrapper.find('[data-testid="email-input"]')
      const passwordInput = wrapper.find('[data-testid="password-input"]')
      
      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')

      // 提交表单
      const loginButton = wrapper.find('[data-testid="login-button"]')
      await loginButton.trigger('click')

      // 验证API调用
      expect(http.post).toHaveBeenCalledWith('/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })

  describe('Registration Flow', () => {
    beforeEach(async () => {
      // 切换到注册模式
      const registerLink = wrapper.find('[data-testid="switch-to-register"]')
      await registerLink.trigger('click')
    })

    it('should render registration form', () => {
      expect(wrapper.find('[data-testid="register-form"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="name-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="email-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="password-input"]').exists()).toBe(true)
      expect(wrapper.find('[data-testid="confirm-password-input"]').exists()).toBe(true)
    })

    it('should validate password confirmation', async () => {
      const passwordInput = wrapper.find('[data-testid="password-input"]')
      const confirmPasswordInput = wrapper.find('[data-testid="confirm-password-input"]')
      
      await passwordInput.setValue('password123')
      await confirmPasswordInput.setValue('different-password')

      const registerButton = wrapper.find('[data-testid="register-button"]')
      await registerButton.trigger('click')

      // 检查密码不匹配的错误
      expect(wrapper.text()).toContain('密码不匹配')
    })

    it('should submit registration form with valid data', async () => {
      const { http } = await import('@/utils/request')
      vi.mocked(http.post).mockResolvedValue({
        data: {
          message: '注册成功',
          user: { id: 1, email: '<EMAIL>', name: 'New User' }
        }
      })

      // 填写注册表单
      const nameInput = wrapper.find('[data-testid="name-input"]')
      const emailInput = wrapper.find('[data-testid="email-input"]')
      const passwordInput = wrapper.find('[data-testid="password-input"]')
      const confirmPasswordInput = wrapper.find('[data-testid="confirm-password-input"]')
      
      await nameInput.setValue('New User')
      await emailInput.setValue('<EMAIL>')
      await passwordInput.setValue('password123')
      await confirmPasswordInput.setValue('password123')

      // 提交表单
      const registerButton = wrapper.find('[data-testid="register-button"]')
      await registerButton.trigger('click')

      // 验证API调用
      expect(http.post).toHaveBeenCalledWith('/auth/register', {
        name: 'New User',
        email: '<EMAIL>',
        password: 'password123'
      })
    })
  })

  describe('User Store Integration', () => {
    it('should update user store on successful login', async () => {
      const mockUserData = {
        id: 1,
        email: '<EMAIL>',
        name: 'Test User'
      }

      // 模拟登录成功
      await userStore.login('<EMAIL>', 'password123')

      // 验证用户状态更新
      expect(userStore.isAuthenticated).toBe(true)
      expect(userStore.currentUser).toBeDefined()
    })

    it('should handle logout properly', async () => {
      // 先登录
      await userStore.login('<EMAIL>', 'password123')
      expect(userStore.isAuthenticated).toBe(true)

      // 登出
      await userStore.logout()
      expect(userStore.isAuthenticated).toBe(false)
      expect(userStore.currentUser).toBeNull()
    })
  })

  describe('Navigation Integration', () => {
    it('should redirect to home after successful login', async () => {
      // 模拟成功登录
      const { http } = await import('@/utils/request')
      vi.mocked(http.post).mockResolvedValue({
        data: {
          token: 'fake-jwt-token',
          user: { id: 1, email: '<EMAIL>', name: 'Test User' }
        }
      })

      // 登录并检查路由跳转
      await userStore.login('<EMAIL>', 'password123')
      
      // 注意：在实际应用中，这里应该检查路由跳转
      // 但在单元测试中，我们主要验证store状态
      expect(userStore.isAuthenticated).toBe(true)
    })
  })
}) 