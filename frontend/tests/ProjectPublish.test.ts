import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import ProjectPublish from '@/views/ProjectPublish.vue'
import { useUserStore } from '@/stores/user'

// Mock API 模块
vi.mock('@/api/project', () => ({
  createProject: vi.fn()
}))

// Mock router
const mockRouter = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: { template: '<div>Home</div>' } },
    { path: '/projects', component: { template: '<div>Projects</div>' } }
  ]
})

// Mock 组件
vi.mock('@/components/layout/Navbar.vue', () => ({
  default: {
    name: 'Navbar',
    template: '<div data-testid="navbar">Navbar</div>'
  }
}))

describe('ProjectPublish 组件测试', () => {
  let wrapper: VueWrapper<any>
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    
    // 设置用户状态
    const userStore = useUserStore()
    userStore.user = {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      role: 'developer'
    }
    userStore.isAuthenticated = true

    wrapper = mount(ProjectPublish, {
      global: {
        plugins: [pinia, mockRouter]
      }
    })
  })

  describe('组件渲染', () => {
    it('应该正确渲染项目发布页面', () => {
      expect(wrapper.find('[data-testid="navbar"]').exists()).toBe(true)
      expect(wrapper.find('.wizard-container').exists()).toBe(true)
      expect(wrapper.find('.progress-steps').exists()).toBe(true)
    })

    it('应该显示3个步骤指示器', () => {
      const steps = wrapper.findAll('.step')
      expect(steps).toHaveLength(3)
      expect(steps[0].text()).toContain('基本信息')
      expect(steps[1].text()).toContain('团队需求')
      expect(steps[2].text()).toContain('项目分析')
    })

    it('初始状态应该在第一步', () => {
      expect(wrapper.vm.currentStep).toBe(1)
      expect(wrapper.find('.step.active').text()).toContain('基本信息')
      expect(wrapper.find('[v-show="currentStep === 1"]').isVisible()).toBe(true)
    })
  })

  describe('表单验证', () => {
    it('第一步表单验证应该正常工作', async () => {
      // 初始状态下一步按钮应该被禁用
      expect(wrapper.vm.canProceed).toBe(false)
      
      // 填写第一步必填字段
      wrapper.vm.formData.title = '测试项目'
      wrapper.vm.formData.summary = '测试项目概述'
      wrapper.vm.formData.category = 'web'
      wrapper.vm.formData.budgetRange = '10000-50000'
      wrapper.vm.formData.description = '这是一个测试项目描述，需要超过50个字符才能通过验证。'
      
      await wrapper.vm.$nextTick()
      expect(wrapper.vm.canProceed).toBe(true)
    })

    it('项目描述少于50字符应该无法继续', async () => {
      wrapper.vm.formData.title = '测试项目'
      wrapper.vm.formData.summary = '测试项目概述'
      wrapper.vm.formData.category = 'web'
      wrapper.vm.formData.budgetRange = '10000-50000'
      wrapper.vm.formData.description = '太短了'
      
      await wrapper.vm.$nextTick()
      expect(wrapper.vm.canProceed).toBe(false)
    })

    it('第二步表单验证应该正常工作', async () => {
      // 跳到第二步
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      // 初始状态应该无法继续（团队成员信息不完整）
      expect(wrapper.vm.canProceed).toBe(false)
      
      // 填写第二步必填字段
      wrapper.vm.formData.teamInfo[0].name = '张三'
      wrapper.vm.formData.teamInfo[0].role = '项目经理'
      wrapper.vm.formData.teamInvestment = '全职投入'
      wrapper.vm.formData.recruitmentInfo[0].position = '前端开发'
      wrapper.vm.formData.recruitmentInfo[0].cooperation = 'salary'
      wrapper.vm.formData.workType = 'remote'
      wrapper.vm.formData.workArrangement = 'fullTime'
      
      await wrapper.vm.$nextTick()
      expect(wrapper.vm.canProceed).toBe(true)
    })

    it('第三步表单验证应该正常工作', async () => {
      // 跳到第三步
      wrapper.vm.currentStep = 3
      await wrapper.vm.$nextTick()
      
      // 初始状态应该无法继续
      expect(wrapper.vm.canProceed).toBe(false)
      
      // 填写第三步必填字段
      wrapper.vm.formData.recentProgress = '项目进展良好'
      wrapper.vm.formData.userAnalysis = '用户分析内容'
      wrapper.vm.formData.projectOrigin = '项目起源'
      wrapper.vm.formData.competitiveAdvantage = '竞争优势'
      wrapper.vm.formData.businessModel = 'subscription'
      wrapper.vm.formData.businessDescription = '商业模式描述'
      wrapper.vm.formData.durationMonths = '6'
      
      await wrapper.vm.$nextTick()
      expect(wrapper.vm.canProceed).toBe(true)
    })
  })

  describe('步骤导航', () => {
    beforeEach(async () => {
      // 填写第一步数据
      wrapper.vm.formData.title = '测试项目'
      wrapper.vm.formData.summary = '测试项目概述'
      wrapper.vm.formData.category = 'web'
      wrapper.vm.formData.budgetRange = '10000-50000'
      wrapper.vm.formData.description = '这是一个测试项目描述，需要超过50个字符才能通过验证。'
      await wrapper.vm.$nextTick()
    })

    it('应该能够前进到下一步', async () => {
      expect(wrapper.vm.currentStep).toBe(1)
      
      await wrapper.vm.nextStep()
      expect(wrapper.vm.currentStep).toBe(2)
      
      // 检查第二步是否显示
      expect(wrapper.find('.step.active').text()).toContain('团队需求')
    })

    it('应该能够返回到上一步', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      await wrapper.vm.prevStep()
      expect(wrapper.vm.currentStep).toBe(1)
      
      // 检查第一步是否显示
      expect(wrapper.find('.step.active').text()).toContain('基本信息')
    })

    it('在第一步时不应该显示上一步按钮', () => {
      expect(wrapper.vm.currentStep).toBe(1)
      expect(wrapper.find('.btn-secondary').exists()).toBe(false)
    })

    it('在最后一步时应该显示提交按钮', async () => {
      wrapper.vm.currentStep = 3
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.btn-primary').text()).toContain('提交审核')
    })
  })

  describe('动态表单功能', () => {
    it('应该能够添加团队成员', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      const initialLength = wrapper.vm.formData.teamInfo.length
      expect(initialLength).toBe(1)
      
      await wrapper.vm.addTeamMember()
      expect(wrapper.vm.formData.teamInfo.length).toBe(initialLength + 1)
    })

    it('应该能够删除团队成员', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      // 添加一个成员
      await wrapper.vm.addTeamMember()
      expect(wrapper.vm.formData.teamInfo.length).toBe(2)
      
      // 删除第二个成员
      await wrapper.vm.removeTeamMember(1)
      expect(wrapper.vm.formData.teamInfo.length).toBe(1)
    })

    it('不应该允许删除最后一个团队成员', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      expect(wrapper.vm.formData.teamInfo.length).toBe(1)
      // 尝试删除唯一的成员，应该不会减少
      await wrapper.vm.removeTeamMember(0)
      expect(wrapper.vm.formData.teamInfo.length).toBe(1)
    })

    it('应该能够添加招募需求', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      const initialLength = wrapper.vm.formData.recruitmentInfo.length
      expect(initialLength).toBe(1)
      
      await wrapper.vm.addRequirement()
      expect(wrapper.vm.formData.recruitmentInfo.length).toBe(initialLength + 1)
    })

    it('应该能够删除招募需求', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      // 添加一个需求
      await wrapper.vm.addRequirement()
      expect(wrapper.vm.formData.recruitmentInfo.length).toBe(2)
      
      // 删除第二个需求
      await wrapper.vm.removeRequirement(1)
      expect(wrapper.vm.formData.recruitmentInfo.length).toBe(1)
    })
  })

  describe('表单提交', () => {
    beforeEach(async () => {
      // 填写完整表单数据
      wrapper.vm.formData = {
        title: '测试项目',
        summary: '测试项目概述',
        category: 'web',
        budgetRange: '10000-50000',
        description: '这是一个测试项目描述，需要超过50个字符才能通过验证。',
        demoUrl: 'https://example.com',
        teamInfo: [{
          name: '张三',
          role: '项目经理',
          background: '5年经验',
          introduction: '负责项目管理'
        }],
        teamInvestment: '全职投入',
        recruitmentInfo: [{
          position: '前端开发',
          cooperation: 'salary',
          skills: 'Vue.js, React',
          salary: '8000-12000',
          equity: ''
        }],
        workType: 'remote',
        workArrangement: 'fullTime',
        workLocation: '北京',
        recentProgress: '项目进展良好',
        userAnalysis: '用户分析内容',
        projectOrigin: '项目起源',
        competitiveAdvantage: '竞争优势',
        businessModel: 'subscription',
        businessDescription: '商业模式描述',
        durationMonths: '6'
      }
      
      wrapper.vm.currentStep = 3
      await wrapper.vm.$nextTick()
    })

    it('应该能够成功提交项目', async () => {
      const { createProject } = await import('@/api/project')
      vi.mocked(createProject).mockResolvedValue({
        success: true,
        data: { projectId: 1 }
      })

      // Mock alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})
      
      // Mock router push
      const pushSpy = vi.spyOn(mockRouter, 'push').mockImplementation(() => Promise.resolve())

      await wrapper.vm.submitProject()

      expect(createProject).toHaveBeenCalledWith({
        ...wrapper.vm.formData,
        teamInfo: JSON.stringify(wrapper.vm.formData.teamInfo),
        recruitmentInfo: JSON.stringify(wrapper.vm.formData.recruitmentInfo),
        durationMonths: 6
      })
      
      expect(alertSpy).toHaveBeenCalledWith(expect.stringContaining('提交成功'))
      expect(pushSpy).toHaveBeenCalledWith('/projects')

      alertSpy.mockRestore()
      pushSpy.mockRestore()
    })

    it('应该正确处理提交失败', async () => {
      const { createProject } = await import('@/api/project')
      vi.mocked(createProject).mockRejectedValue(new Error('网络错误'))

      // Mock alert
      const alertSpy = vi.spyOn(window, 'alert').mockImplementation(() => {})

      await wrapper.vm.submitProject()

      expect(alertSpy).toHaveBeenCalledWith(expect.stringContaining('提交失败'))
      expect(wrapper.vm.isSubmitting).toBe(false)

      alertSpy.mockRestore()
    })

    it('提交过程中应该显示加载状态', async () => {
      const { createProject } = await import('@/api/project')
      
      // Mock 一个延迟的 promise
      let resolvePromise: (value: any) => void
      const delayedPromise = new Promise(resolve => {
        resolvePromise = resolve
      })
      vi.mocked(createProject).mockReturnValue(delayedPromise)

      // 开始提交
      const submitPromise = wrapper.vm.submitProject()
      
      // 检查加载状态
      expect(wrapper.vm.isSubmitting).toBe(true)
      
      // 解决 promise
      resolvePromise!({ success: true, data: { projectId: 1 } })
      await submitPromise
      
      expect(wrapper.vm.isSubmitting).toBe(false)
    })
  })

  describe('用户体验功能', () => {
    it('应该在步骤切换时滚动到顶部', async () => {
      // Mock scrollTo
      const mockScrollTo = vi.fn()
      Object.defineProperty(document, 'querySelector', {
        value: vi.fn(() => ({ scrollTo: mockScrollTo })),
        writable: true
      })

      await wrapper.vm.nextStep()
      expect(mockScrollTo).toHaveBeenCalledWith({ top: 0, behavior: 'smooth' })
    })

    it('应该显示字符计数提示', () => {
      const hints = wrapper.findAll('.form-hint')
      expect(hints.length).toBeGreaterThan(0)
      expect(hints.some(hint => hint.text().includes('字符'))).toBe(true)
    })

    it('应该正确显示必填字段标识', () => {
      const requiredLabels = wrapper.findAll('.required')
      expect(requiredLabels.length).toBeGreaterThan(0)
      requiredLabels.forEach(label => {
        expect(label.text()).toBe('*')
      })
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端正确显示', async () => {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768
      })

      // 触发 resize 事件
      window.dispatchEvent(new Event('resize'))
      await wrapper.vm.$nextTick()

      // 检查是否应用了移动端样式类
      expect(wrapper.find('.wizard-container').exists()).toBe(true)
    })
  })

  describe('边界情况', () => {
    it('应该正确处理空的可选字段', async () => {
      wrapper.vm.formData.demoUrl = ''
      wrapper.vm.formData.workLocation = ''
      
      // 填写其他必填字段...
      wrapper.vm.formData.title = '测试项目'
      wrapper.vm.formData.summary = '测试项目概述'
      // ... 其他必填字段
      
      expect(() => wrapper.vm.submitProject()).not.toThrow()
    })

    it('应该正确处理特殊字符', async () => {
      wrapper.vm.formData.title = '测试项目!@#$%^&*()'
      wrapper.vm.formData.description = '包含特殊字符的描述: <script>alert("test")</script>'
      
      expect(wrapper.vm.formData.title).toBe('测试项目!@#$%^&*()')
      expect(wrapper.vm.formData.description).toContain('<script>')
    })

    it('应该正确处理长文本', async () => {
      const longText = 'a'.repeat(1000)
      wrapper.vm.formData.description = longText
      
      expect(wrapper.vm.formData.description.length).toBe(1000)
    })
  })
}) 