import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createTestingPinia } from '@pinia/testing'
import ProjectPublish from '@/views/ProjectPublish.vue'

// Mock router
const mockPush = vi.fn()
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush
  })
}))

// Mock Navbar component
vi.mock('@/components/layout/Navbar.vue', () => ({
  default: {
    name: 'Navbar',
    template: '<div>Navbar</div>'
  }
}))

// Mock API
vi.mock('@/api/project', () => ({
  createProject: vi.fn()
}))

describe('ProjectPublish.vue', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ProjectPublish, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })
  })

  describe('UI渲染测试', () => {
    it('应该渲染所有4个步骤', () => {
      const steps = wrapper.findAll('.step')
      expect(steps).toHaveLength(4)
    })

    it('第一步应该是激活状态', () => {
      const firstStep = wrapper.find('.step.active')
      expect(firstStep.exists()).toBe(true)
      expect(firstStep.find('.step-circle').text()).toBe('1')
    })

    it('应该显示正确的步骤标题', () => {
      const title = wrapper.find('.progress-title')
      expect(title.text()).toBe('项目基本信息')
    })

    it('应该显示下一步按钮但不显示上一步按钮', () => {
      const nextBtn = wrapper.find('.btn-primary')
      const prevBtn = wrapper.find('.btn-secondary')
      
      expect(nextBtn.exists()).toBe(true)
      expect(nextBtn.text()).toContain('下一步')
      expect(prevBtn.exists()).toBe(false)
    })
  })

  describe('表单验证测试', () => {
    it('空表单时下一步按钮应该被禁用', () => {
      const nextBtn = wrapper.find('.btn-primary')
      expect(nextBtn.element.disabled).toBe(true)
    })

    it('项目名称验证 - 空值', async () => {
      const titleInput = wrapper.find('input[placeholder="给你的项目起一个吸引人的名字"]')
      await titleInput.setValue('')
      await titleInput.trigger('blur')
      
      const errorMsg = wrapper.find('.error-message')
      expect(errorMsg.exists()).toBe(true)
      expect(errorMsg.text()).toBe('项目名称不能为空')
    })

    it('项目名称验证 - 太短', async () => {
      const titleInput = wrapper.find('input[placeholder="给你的项目起一个吸引人的名字"]')
      await titleInput.setValue('短')
      await titleInput.trigger('blur')
      
      const errorMsg = wrapper.find('.error-message')
      expect(errorMsg.text()).toBe('项目名称至少5个字符')
    })

    it('项目名称验证 - 太长', async () => {
      const titleInput = wrapper.find('input[placeholder="给你的项目起一个吸引人的名字"]')
      await titleInput.setValue('这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的项目名称超过100个字符的限制测试')
      await titleInput.trigger('blur')
      
      const errorMsg = wrapper.find('.error-message')
      expect(errorMsg.text()).toBe('项目名称不能超过100个字符')
    })

    it('一句话概括验证 - 超长', async () => {
      const summaryInput = wrapper.find('input[placeholder="用一句话概括项目的核心价值"]')
      await summaryInput.setValue('这个概括超过了二十个字符的限制测试')
      await summaryInput.trigger('blur')
      
      const errorMsg = wrapper.find('.error-message')
      expect(errorMsg.text()).toBe('不能超过20个字符')
    })

    it('项目描述验证 - 太短', async () => {
      const descInput = wrapper.find('textarea[placeholder*="详细描述你的项目"]')
      await descInput.setValue('太短的描述')
      await descInput.trigger('blur')
      
      const errorMsg = wrapper.find('.error-message')
      expect(errorMsg.text()).toBe('项目描述至少50个字符')
    })
  })

  describe('表单交互测试', () => {
    it('填写有效数据后下一步按钮应该启用', async () => {
      // 填写所有必填字段
      await wrapper.find('input[placeholder="给你的项目起一个吸引人的名字"]')
        .setValue('创新项目名称')
      await wrapper.find('input[placeholder="用一句话概括项目的核心价值"]')
        .setValue('创新的概括')
      await wrapper.find('select').setValue('web')
      await wrapper.findAll('select')[1].setValue('10000-50000')
      await wrapper.find('textarea[placeholder*="详细描述你的项目"]')
        .setValue('这是一个详细的项目描述，包含了功能特点、技术要求、目标用户、预期效果等详细信息，总字符数超过五十个字符的要求。')
      
      const nextBtn = wrapper.find('.btn-primary')
      expect(nextBtn.element.disabled).toBe(false)
    })

    it('点击下一步应该切换到第二步', async () => {
      // 填写有效数据
      await wrapper.find('input[placeholder="给你的项目起一个吸引人的名字"]')
        .setValue('创新项目名称')
      await wrapper.find('input[placeholder="用一句话概括项目的核心价值"]')
        .setValue('创新的概括')
      await wrapper.find('select').setValue('web')
      await wrapper.findAll('select')[1].setValue('10000-50000')
      await wrapper.find('textarea[placeholder*="详细描述你的项目"]')
        .setValue('这是一个详细的项目描述，包含了功能特点、技术要求、目标用户、预期效果等详细信息，总字符数超过五十个字符的要求。')
      
      const nextBtn = wrapper.find('.btn-primary')
      await nextBtn.trigger('click')
      
      // 检查是否切换到第二步
      const activeStep = wrapper.find('.step.active .step-circle')
      expect(activeStep.text()).toBe('2')
      
      const title = wrapper.find('.progress-title')
      expect(title.text()).toBe('团队和需求信息')
    })

    it('在第二步应该显示上一步按钮', async () => {
      // 先切换到第二步
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      const prevBtn = wrapper.find('.btn-secondary')
      expect(prevBtn.exists()).toBe(true)
      expect(prevBtn.text()).toContain('上一步')
    })
  })

  describe('动态表单项测试', () => {
    it('应该有一个默认的团队成员', () => {
      expect(wrapper.vm.formData.teamInfo).toHaveLength(1)
    })

    it('应该能添加新的团队成员', async () => {
      const addBtn = wrapper.find('.add-btn')
      await addBtn.trigger('click')
      
      expect(wrapper.vm.formData.teamInfo).toHaveLength(2)
    })

    it('应该能删除团队成员（保留至少一个）', async () => {
      // 先添加一个成员
      wrapper.vm.addTeamMember()
      expect(wrapper.vm.formData.teamInfo).toHaveLength(2)
      
      // 删除一个成员
      wrapper.vm.removeTeamMember(1)
      expect(wrapper.vm.formData.teamInfo).toHaveLength(1)
    })

    it('应该有一个默认的招募需求', () => {
      expect(wrapper.vm.formData.recruitmentInfo).toHaveLength(1)
    })

    it('应该能添加新的招募需求', async () => {
      wrapper.vm.addRequirement()
      expect(wrapper.vm.formData.recruitmentInfo).toHaveLength(2)
    })
  })

  describe('步骤状态管理测试', () => {
    it('已完成的步骤应该有completed类', async () => {
      wrapper.vm.currentStep = 3
      await wrapper.vm.$nextTick()
      
      const firstStep = wrapper.findAll('.step')[0]
      const secondStep = wrapper.findAll('.step')[1]
      
      expect(firstStep.classes()).toContain('completed')
      expect(secondStep.classes()).toContain('completed')
    })

    it('当前步骤应该有active类', async () => {
      wrapper.vm.currentStep = 2
      await wrapper.vm.$nextTick()
      
      const secondStep = wrapper.findAll('.step')[1]
      expect(secondStep.classes()).toContain('active')
    })
  })

  describe('提交功能测试', () => {
    it('在第四步应该显示提交按钮', async () => {
      wrapper.vm.currentStep = 4
      await wrapper.vm.$nextTick()
      
      const submitBtn = wrapper.find('.btn-primary')
      expect(submitBtn.text()).toContain('提交审核')
    })

    it('提交时应该显示加载状态', async () => {
      wrapper.vm.currentStep = 4
      wrapper.vm.isSubmitting = true
      await wrapper.vm.$nextTick()
      
      const submitBtn = wrapper.find('.btn-primary')
      expect(submitBtn.text()).toBe('提交中...')
      expect(submitBtn.element.disabled).toBe(true)
    })
  })

  describe('错误提示显示测试', () => {
    it('表单有错误时应该显示验证提示', async () => {
      // 确保有验证错误
      wrapper.vm.errors.title = '项目名称不能为空'
      await wrapper.vm.$nextTick()
      
      const validationHint = wrapper.find('.validation-hint')
      expect(validationHint.exists()).toBe(true)
      expect(validationHint.text()).toContain('请完善表单信息后继续')
    })

    it('表单无错误时不应该显示验证提示', async () => {
      // 清空所有错误
      Object.keys(wrapper.vm.errors).forEach(key => {
        wrapper.vm.errors[key] = ''
      })
      await wrapper.vm.$nextTick()
      
      const validationHint = wrapper.find('.validation-hint')
      expect(validationHint.exists()).toBe(false)
    })
  })

  describe('响应式设计测试', () => {
    it('应该有适当的CSS类用于响应式设计', () => {
      expect(wrapper.find('.wizard-container').exists()).toBe(true)
      expect(wrapper.find('.form-grid').exists()).toBe(true)
      expect(wrapper.find('.form-actions').exists()).toBe(true)
    })
  })
})

// 表单验证函数单独测试
describe('表单验证函数测试', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(ProjectPublish, {
      global: {
        plugins: [createTestingPinia({
          createSpy: vi.fn
        })]
      }
    })
  })

  it('validateField - 项目名称验证', () => {
    expect(wrapper.vm.validateField('title', '')).toBe('项目名称不能为空')
    expect(wrapper.vm.validateField('title', '短')).toBe('项目名称至少5个字符')
    expect(wrapper.vm.validateField('title', 'a'.repeat(101))).toBe('项目名称不能超过100个字符')
    expect(wrapper.vm.validateField('title', '合适的项目名称')).toBe('')
  })

  it('validateField - 一句话概括验证', () => {
    expect(wrapper.vm.validateField('summary', '')).toBe('一句话概括不能为空')
    expect(wrapper.vm.validateField('summary', 'a'.repeat(21))).toBe('不能超过20个字符')
    expect(wrapper.vm.validateField('summary', '合适的概括')).toBe('')
  })

  it('validateField - 项目描述验证', () => {
    expect(wrapper.vm.validateField('description', '')).toBe('项目描述不能为空')
    expect(wrapper.vm.validateField('description', '太短')).toBe('项目描述至少50个字符')
    expect(wrapper.vm.validateField('description', 'a'.repeat(50))).toBe('')
  })

  it('validateCurrentStep - 第一步验证', () => {
    // 空表单应该验证失败
    expect(wrapper.vm.validateCurrentStep()).toBe(false)
    
    // 填写完整表单应该验证成功
    wrapper.vm.formData.title = '测试项目'
    wrapper.vm.formData.summary = '测试概括'
    wrapper.vm.formData.category = 'web'
    wrapper.vm.formData.budgetRange = '10000-50000'
    wrapper.vm.formData.description = 'a'.repeat(50)
    wrapper.vm.formData.teamInfo[0].name = '张三'
    wrapper.vm.formData.teamInfo[0].role = '开发者'
    
    expect(wrapper.vm.validateCurrentStep()).toBe(true)
  })
}) 