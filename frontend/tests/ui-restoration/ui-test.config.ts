import { PlaywrightTestConfig, devices } from '@playwright/test'

// 声明process类型
declare const process: {
  env: {
    CI?: string
  }
}

/**
 * UI还原测试配置
 * 用于精确对比页面实现与原型设计的一致性
 */
const config: PlaywrightTestConfig = {
  testDir: './restoration',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // 测试超时设置
  timeout: 30 * 1000,
  expect: {
    // 动画等待时间
    toHaveScreenshot: { 
      threshold: 0.05,
      animations: 'disabled'
    },
  },
  
  // 输出目录
  outputDir: 'test-results/',
  
  // 报告配置
  reporter: [
    ['html', { 
      outputFolder: 'ui-restoration-report',
      open: process.env.CI ? 'never' : 'on-failure'
    }],
    ['json', { 
      outputFile: 'ui-restoration-results.json' 
    }],
    ['list']
  ],
  
  // 全局设置
  use: {
    baseURL: 'http://localhost:3001',
    // 等待设置
    actionTimeout: 0,
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'on-first-retry',
  },

  projects: [
    {
      name: 'Desktop Chrome',
      use: { 
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 }
      },
    },
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['iPhone 12']
      },
    },
  ],
}

export default config 