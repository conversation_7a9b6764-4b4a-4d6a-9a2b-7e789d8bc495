import { Page, expect } from '@playwright/test'

/**
 * UI对比工具类
 * 提供精确的UI还原验证功能
 */
export class UIComparison {
  constructor(private page: Page) {}

  /**
   * 验证元素的精确CSS样式
   */
  async verifyElementStyles(selector: string, expectedStyles: Record<string, string>) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    for (const [property, value] of Object.entries(expectedStyles)) {
      await expect(element).toHaveCSS(property, value)
    }
  }

  /**
   * 验证字体样式
   */
  async verifyFontStyles(selector: string, expectedFont: {
    family?: string
    size?: string
    weight?: string
    color?: string
    lineHeight?: string
  }) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    if (expectedFont.family) {
      await expect(element).toHaveCSS('font-family', expectedFont.family)
    }
    if (expectedFont.size) {
      await expect(element).toHaveCSS('font-size', expectedFont.size)
    }
    if (expectedFont.weight) {
      await expect(element).toHaveCSS('font-weight', expectedFont.weight)
    }
    if (expectedFont.color) {
      await expect(element).toHaveCSS('color', expectedFont.color)
    }
    if (expectedFont.lineHeight) {
      await expect(element).toHaveCSS('line-height', expectedFont.lineHeight)
    }
  }

  /**
   * 验证间距和布局
   */
  async verifySpacing(selector: string, expectedSpacing: {
    margin?: string
    padding?: string
    gap?: string
  }) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    if (expectedSpacing.margin) {
      await expect(element).toHaveCSS('margin', expectedSpacing.margin)
    }
    if (expectedSpacing.padding) {
      await expect(element).toHaveCSS('padding', expectedSpacing.padding)
    }
    if (expectedSpacing.gap) {
      await expect(element).toHaveCSS('gap', expectedSpacing.gap)
    }
  }

  /**
   * 验证渐变背景
   */
  async verifyGradientBackground(selector: string, expectedGradient: string) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    const computedStyle = await element.evaluate(el => {
      return window.getComputedStyle(el).backgroundImage
    })
    
    // 检查是否包含渐变
    expect(computedStyle).toContain('linear-gradient')
    
    // 可以进一步验证渐变的颜色值
    if (expectedGradient.includes('#667eea')) {
      expect(computedStyle).toContain('rgb(102, 126, 234)')
    }
    if (expectedGradient.includes('#764ba2')) {
      expect(computedStyle).toContain('rgb(118, 75, 162)')
    }
  }

  /**
   * 验证毛玻璃效果
   */
  async verifyGlassEffect(selector: string) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    // 验证backdrop-filter
    await expect(element).toHaveCSS('backdrop-filter', 'blur(20px)')
    
    // 验证半透明背景
    const bgColor = await element.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // 检查背景颜色是否包含透明度
    expect(bgColor).toMatch(/rgba?\(\d+,\s*\d+,\s*\d+,\s*0\.\d+\)/)
  }

  /**
   * 验证按钮样式和交互
   */
  async verifyButtonStyles(selector: string, expectedButton: {
    background?: string
    color?: string
    borderRadius?: string
    padding?: string
    fontSize?: string
    fontWeight?: string
    hoverEffect?: boolean
  }) {
    const button = this.page.locator(selector)
    await expect(button).toBeVisible()
    
    // 验证基础样式
    if (expectedButton.background) {
      await expect(button).toHaveCSS('background-color', expectedButton.background)
    }
    if (expectedButton.color) {
      await expect(button).toHaveCSS('color', expectedButton.color)
    }
    if (expectedButton.borderRadius) {
      await expect(button).toHaveCSS('border-radius', expectedButton.borderRadius)
    }
    if (expectedButton.padding) {
      await expect(button).toHaveCSS('padding', expectedButton.padding)
    }
    if (expectedButton.fontSize) {
      await expect(button).toHaveCSS('font-size', expectedButton.fontSize)
    }
    if (expectedButton.fontWeight) {
      await expect(button).toHaveCSS('font-weight', expectedButton.fontWeight)
    }
    
    // 验证悬停效果
    if (expectedButton.hoverEffect) {
      await button.hover()
      // 等待悬停动画完成
      await this.page.waitForTimeout(300)
      
      // 验证悬停状态的transform
      const transform = await button.evaluate(el => {
        return window.getComputedStyle(el).transform
      })
      
      expect(transform).toMatch(/translateY\(-?\d+px\)/)
    }
  }

  /**
   * 验证卡片组件样式
   */
  async verifyCardStyles(selector: string, expectedCard: {
    background?: string
    borderRadius?: string
    boxShadow?: string
    padding?: string
    hoverEffect?: boolean
  }) {
    const card = this.page.locator(selector)
    await expect(card).toBeVisible()
    
    // 验证基础样式
    if (expectedCard.background) {
      await expect(card).toHaveCSS('background-color', expectedCard.background)
    }
    if (expectedCard.borderRadius) {
      await expect(card).toHaveCSS('border-radius', expectedCard.borderRadius)
    }
    if (expectedCard.boxShadow) {
      await expect(card).toHaveCSS('box-shadow', expectedCard.boxShadow)
    }
    if (expectedCard.padding) {
      await expect(card).toHaveCSS('padding', expectedCard.padding)
    }
    
    // 验证悬停效果
    if (expectedCard.hoverEffect) {
      await card.hover()
      await this.page.waitForTimeout(300)
      
      // 验证悬停状态的transform和box-shadow
      const transform = await card.evaluate(el => {
        return window.getComputedStyle(el).transform
      })
      
      const boxShadow = await card.evaluate(el => {
        return window.getComputedStyle(el).boxShadow
      })
      
      expect(transform).toMatch(/translateY\(-?\d+px\)/)
      expect(boxShadow).toContain('rgba')
    }
  }

  /**
   * 验证动画效果
   */
  async verifyAnimationEffect(selector: string, expectedAnimation: {
    name?: string
    duration?: string
    timing?: string
    iteration?: string
  }) {
    const element = this.page.locator(selector)
    await expect(element).toBeVisible()
    
    if (expectedAnimation.name) {
      await expect(element).toHaveCSS('animation-name', expectedAnimation.name)
    }
    if (expectedAnimation.duration) {
      await expect(element).toHaveCSS('animation-duration', expectedAnimation.duration)
    }
    if (expectedAnimation.timing) {
      await expect(element).toHaveCSS('animation-timing-function', expectedAnimation.timing)
    }
    if (expectedAnimation.iteration) {
      await expect(element).toHaveCSS('animation-iteration-count', expectedAnimation.iteration)
    }
  }

  /**
   * 验证响应式布局
   */
  async verifyResponsiveLayout(selector: string, breakpoints: {
    mobile?: { width: number; height: number }
    tablet?: { width: number; height: number }
    desktop?: { width: number; height: number }
  }) {
    const element = this.page.locator(selector)
    
    // 测试移动端
    if (breakpoints.mobile) {
      await this.page.setViewportSize(breakpoints.mobile)
      await this.page.waitForTimeout(300)
      await expect(element).toBeVisible()
    }
    
    // 测试平板端
    if (breakpoints.tablet) {
      await this.page.setViewportSize(breakpoints.tablet)
      await this.page.waitForTimeout(300)
      await expect(element).toBeVisible()
    }
    
    // 测试桌面端
    if (breakpoints.desktop) {
      await this.page.setViewportSize(breakpoints.desktop)
      await this.page.waitForTimeout(300)
      await expect(element).toBeVisible()
    }
  }

  /**
   * 禁用动画以确保截图一致性
   */
  async disableAnimations() {
    await this.page.addStyleTag({
      content: `
        *, *::before, *::after {
          animation-duration: 0s !important;
          animation-delay: 0s !important;
          transition-duration: 0s !important;
          transition-delay: 0s !important;
        }
      `
    })
  }

  /**
   * 等待页面完全加载
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle')
    
    // 等待所有图片加载
    await this.page.waitForFunction(() => {
      const images = Array.from(document.images)
      return images.every(img => img.complete)
    })
    
    // 等待所有字体加载
    await this.page.waitForFunction(() => document.fonts.ready)
  }

  /**
   * 生成精确的截图对比
   */
  async takeAccurateScreenshot(selector: string, name: string, options?: {
    fullPage?: boolean
    animations?: 'disabled' | 'allow'
    threshold?: number
    maxDiffPixels?: number
  }) {
    await this.waitForPageLoad()
    
    if (options?.animations === 'disabled') {
      await this.disableAnimations()
    }
    
    const element = this.page.locator(selector)
    
    if (options?.fullPage) {
      await expect(this.page).toHaveScreenshot(name, {
        fullPage: true,
        animations: options.animations,
        threshold: options.threshold || 0.05,
        maxDiffPixels: options.maxDiffPixels
      })
    } else {
      await expect(element).toHaveScreenshot(name, {
        animations: options?.animations,
        threshold: options?.threshold || 0.05,
        maxDiffPixels: options?.maxDiffPixels
      })
    }
  }
}

/**
 * 原型设计规范配置
 */
export const PROTOTYPE_SPECS = {
  // 字体规范
  fonts: {
    primary: '"Inter", "PingFang SC", "Helvetica Neue", Arial, sans-serif',
    sizes: {
      xs: '10px',
      sm: '12px',
      base: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
      '3xl': '36px',
      '4xl': '42px',
      '5xl': '48px'
    },
    weights: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
      black: '900'
    }
  },
  
  // 颜色规范
  colors: {
    primary: '#667eea',
    primaryDark: '#764ba2',
    secondary: '#ec4899',
    accent: '#f59e0b',
    success: '#10b981',
    white: '#ffffff',
    black: '#1a1a1a',
    gray: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1e293b',
      900: '#0f172a'
    }
  },
  
  // 间距规范
  spacing: {
    1: '4px',
    2: '8px',
    3: '12px',
    4: '16px',
    5: '20px',
    6: '24px',
    8: '32px',
    10: '40px',
    12: '48px',
    15: '60px',
    20: '80px',
    24: '96px',
    30: '120px'
  },
  
  // 圆角规范
  radius: {
    sm: '6px',
    base: '8px',
    md: '12px',
    lg: '16px',
    xl: '20px',
    full: '50%'
  },
  
  // 渐变规范
  gradients: {
    primary: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    hero: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
    authBrand: 'linear-gradient(135deg, #8b5cf6 0%, #ec4899 30%, #f59e0b 60%, #10b981 100%)'
  },
  
  // 阴影规范
  shadows: {
    sm: '0 1px 2px rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.07)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.1)',
    '2xl': '0 25px 50px rgba(0, 0, 0, 0.08)'
  }
} 