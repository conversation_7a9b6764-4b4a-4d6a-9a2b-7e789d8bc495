import { test, expect } from '@playwright/test'

/**
 * 认证页面UI还原测试
 * 验证Auth.vue页面与原型auth.html的100%一致性
 */

test.describe('认证页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问认证页面
    await page.goto('/auth')
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.auth-page', { timeout: 10000 })
    // 等待所有异步组件加载完成
    await page.waitForTimeout(1000)
  })

  test('页面整体布局和背景装饰', async ({ page }) => {
    // 验证页面根元素
    const authPage = page.locator('.auth-page')
    await expect(authPage).toBeVisible()
    
    // 验证背景装饰
    const bgDecoration = page.locator('.bg-decoration')
    await expect(bgDecoration).toBeVisible()
    await expect(bgDecoration).toHaveCSS('position', 'fixed')
    
    // 验证背景形状数量
    const bgShapes = page.locator('.bg-shape')
    await expect(bgShapes).toHaveCount(3)
    
    // 验证每个背景形状的基本样式
    for (let i = 0; i < 3; i++) {
      const shape = bgShapes.nth(i)
      await expect(shape).toHaveCSS('border-radius', '50%')
      await expect(shape).toHaveCSS('position', 'absolute')
    }
  })

  test('主容器布局验证', async ({ page }) => {
    const authContainer = page.locator('.auth-container')
    await expect(authContainer).toBeVisible()
    
    // 验证容器样式
    await expect(authContainer).toHaveCSS('display', 'flex')
    await expect(authContainer).toHaveCSS('background-color', 'rgb(255, 255, 255)')
    await expect(authContainer).toHaveCSS('border-radius', '20px')
    await expect(authContainer).toHaveCSS('overflow', 'hidden')
    
    // 验证最小高度
    const minHeight = await authContainer.evaluate(el => 
      window.getComputedStyle(el).minHeight
    )
    expect(minHeight).toBe('650px')
  })

  test('左侧品牌区域完整验证', async ({ page }) => {
    const brandSection = page.locator('.brand-section')
    await expect(brandSection).toBeVisible()
    
    // 验证品牌区域背景渐变
    const brandBg = await brandSection.evaluate(el => 
      window.getComputedStyle(el).background
    )
    expect(brandBg).toContain('linear-gradient')
    
    // 验证品牌Logo
    const brandLogo = page.locator('.brand-logo')
    await expect(brandLogo).toHaveText('小概率')
    await expect(brandLogo).toHaveCSS('font-size', '42px')
    await expect(brandLogo).toHaveCSS('font-weight', '900')
    
    // 验证品牌标语
    const brandTagline = page.locator('.brand-tagline')
    await expect(brandTagline).toHaveText('梦想与现实的桥梁')
    await expect(brandTagline).toHaveCSS('font-size', '24px')
    await expect(brandTagline).toHaveCSS('font-weight', '600')
  })

  test('故事章节内容验证', async ({ page }) => {
    const storyChapters = page.locator('.story-chapter')
    await expect(storyChapters).toHaveCount(3)
    
    // 验证每个章节的标题和内容
    const expectedChapters = [
      { emoji: '🌟', title: '第一章：灵感的诞生', content: '每个伟大的项目都始于一个简单的想法' },
      { emoji: '🤝', title: '第二章：缘分的相遇', content: '通过智能匹配算法，让有想法的人与有技能的人相遇' },
      { emoji: '🚀', title: '第三章：梦想起航', content: '从原型设计到产品发布，从种子轮到IPO' }
    ]
    
    for (let i = 0; i < expectedChapters.length; i++) {
      const chapter = storyChapters.nth(i)
      const chapterTitle = chapter.locator('.chapter-title')
      const chapterEmoji = chapter.locator('.chapter-emoji')
      const chapterContent = chapter.locator('.chapter-content')
      
      await expect(chapterEmoji).toHaveText(expectedChapters[i].emoji)
      await expect(chapterTitle).toContainText(expectedChapters[i].title)
      await expect(chapterContent).toContainText(expectedChapters[i].content)
      
      // 验证章节样式
      await expect(chapter).toHaveCSS('background-color', /rgba\(255, 255, 255, 0\.1\)/)
      await expect(chapter).toHaveCSS('border-radius', '12px')
      await expect(chapter).toHaveCSS('padding', '20px')
    }
  })

  test('创意元素区域验证', async ({ page }) => {
    const creativeElements = page.locator('.creative-elements')
    await expect(creativeElements).toBeVisible()
    
    // 验证创意项目数量
    const creativeItems = page.locator('.creative-item')
    await expect(creativeItems).toHaveCount(3)
    
    // 验证每个创意项目
    const expectedItems = [
      { icon: '💡', label: '想法' },
      { icon: '⚡', label: '执行' },
      { icon: '🎯', label: '成功' }
    ]
    
    for (let i = 0; i < expectedItems.length; i++) {
      const item = creativeItems.nth(i)
      const icon = item.locator('.creative-icon')
      const label = item.locator('.creative-label')
      
      await expect(icon).toHaveText(expectedItems[i].icon)
      await expect(label).toHaveText(expectedItems[i].label)
      
      // 验证图标样式
      await expect(icon).toHaveCSS('width', '48px')
      await expect(icon).toHaveCSS('height', '48px')
      await expect(icon).toHaveCSS('border-radius', '50%')
    }
  })

  test('激励引言区域验证', async ({ page }) => {
    const inspirationQuote = page.locator('.inspiration-quote')
    await expect(inspirationQuote).toBeVisible()
    
    // 验证引言内容
    const quoteText = page.locator('.quote-text')
    await expect(quoteText).toContainText('技术不仅仅是代码，更是连接人心、实现梦想的桥梁')
    
    // 验证引言作者
    const quoteAuthor = page.locator('.quote-author')
    await expect(quoteAuthor).toContainText('DevMatch 创始人')
  })

  test('右侧表单区域布局验证', async ({ page }) => {
    const formSection = page.locator('.form-section')
    await expect(formSection).toBeVisible()
    
    // 验证表单容器
    const formContainer = page.locator('.form-container')
    await expect(formContainer).toBeVisible()
    
    // 验证表单标题（默认为登录状态）
    const formTitle = page.locator('.form-title')
    await expect(formTitle).toHaveText('开启你的故事')
    
    // 验证表单副标题
    const formSubtitle = page.locator('.form-subtitle')
    await expect(formSubtitle).toHaveText('每一次登录都是新章节的开始')
  })

  test('登录表单字段验证', async ({ page }) => {
    // 验证表单字段数量（登录状态：邮箱、密码）
    const formGroups = page.locator('.form-group')
    await expect(formGroups).toHaveCount(2)
    
    // 验证邮箱字段
    const emailGroup = formGroups.nth(0)
    const emailLabel = emailGroup.locator('.form-label')
    const emailInput = emailGroup.locator('.form-input')
    
    await expect(emailLabel).toHaveText('邮箱地址')
    await expect(emailInput).toHaveAttribute('type', 'email')
    await expect(emailInput).toHaveAttribute('placeholder', '请输入邮箱地址')
    
    // 验证密码字段
    const passwordGroup = formGroups.nth(1)
    const passwordLabel = passwordGroup.locator('.form-label')
    const passwordInput = passwordGroup.locator('.form-input')
    
    await expect(passwordLabel).toHaveText('密码')
    await expect(passwordInput).toHaveAttribute('type', 'password')
    await expect(passwordInput).toHaveAttribute('placeholder', '请输入密码')
  })

  test('表单选项和按钮验证', async ({ page }) => {
    // 验证"记住我"选项
    const formOptions = page.locator('.form-options')
    await expect(formOptions).toBeVisible()
    
    const rememberCheckbox = page.locator('input[type="checkbox"]')
    await expect(rememberCheckbox).toBeVisible()
    
    const checkmarkText = page.locator('.checkmark')
    await expect(checkmarkText).toHaveText('记住我')
    
    // 验证忘记密码链接
    const forgotLink = page.locator('.forgot-link')
    await expect(forgotLink).toHaveText('忘记密码？')
    
    // 验证提交按钮
    const submitBtn = page.locator('.submit-btn')
    await expect(submitBtn).toHaveText('开始我的旅程')
    await expect(submitBtn).toHaveAttribute('type', 'submit')
  })

  test('登录/注册切换功能验证', async ({ page }) => {
    // 验证切换按钮
    const switchBtn = page.locator('.switch-btn')
    await expect(switchBtn).toHaveText('创建新故事')
    
    // 点击切换到注册模式
    await switchBtn.click()
    await page.waitForTimeout(100) // 等待状态更新
    
    // 验证注册模式下的表单标题
    const formTitle = page.locator('.form-title')
    await expect(formTitle).toHaveText('创造你的故事')
    
    // 验证注册模式下的表单副标题
    const formSubtitle = page.locator('.form-subtitle')
    await expect(formSubtitle).toHaveText('每个传奇都有一个开始')
    
    // 验证注册模式下的表单字段数量（用户名、邮箱、密码、确认密码）
    const formGroups = page.locator('.form-group')
    await expect(formGroups).toHaveCount(4)
    
    // 验证用户名字段
    const usernameGroup = formGroups.nth(0)
    const usernameLabel = usernameGroup.locator('.form-label')
    const usernameInput = usernameGroup.locator('.form-input')
    
    await expect(usernameLabel).toHaveText('用户名')
    await expect(usernameInput).toHaveAttribute('placeholder', '请输入用户名')
    
    // 验证确认密码字段
    const confirmPasswordGroup = formGroups.nth(3)
    const confirmPasswordLabel = confirmPasswordGroup.locator('.form-label')
    const confirmPasswordInput = confirmPasswordGroup.locator('.form-input')
    
    await expect(confirmPasswordLabel).toHaveText('确认密码')
    await expect(confirmPasswordInput).toHaveAttribute('placeholder', '请再次输入密码')
    
    // 验证协议同意选项
    const agreementCheckbox = page.locator('input[type="checkbox"]')
    await expect(agreementCheckbox).toBeVisible()
    
    const agreementText = page.locator('.checkmark')
    await expect(agreementText).toContainText('我同意')
    
    // 验证提交按钮文本
    const submitBtn = page.locator('.submit-btn')
    await expect(submitBtn).toHaveText('开始创作故事')
    
    // 验证切换按钮文本
    await expect(switchBtn).toHaveText('继续我的故事')
  })

  test('社交登录区域验证', async ({ page }) => {
    const socialLogin = page.locator('.social-login')
    await expect(socialLogin).toBeVisible()
    
    // 验证分割线
    const divider = page.locator('.divider span')
    await expect(divider).toHaveText('或使用以下方式')
    
    // 验证社交登录按钮
    const socialButtons = page.locator('.social-btn')
    await expect(socialButtons).toHaveCount(2)
    
    // 验证GitHub按钮
    const githubBtn = socialButtons.nth(0)
    const githubIcon = githubBtn.locator('.social-icon')
    await expect(githubIcon).toHaveText('🐱')
    await expect(githubBtn).toContainText('GitHub')
    
    // 验证LinkedIn按钮
    const linkedinBtn = socialButtons.nth(1)
    const linkedinIcon = linkedinBtn.locator('.social-icon')
    await expect(linkedinIcon).toHaveText('💼')
    await expect(linkedinBtn).toContainText('LinkedIn')
  })

  test('表单样式和交互验证', async ({ page }) => {
    // 验证表单输入框样式
    const formInputs = page.locator('.form-input')
    
    for (let i = 0; i < await formInputs.count(); i++) {
      const input = formInputs.nth(i)
      await expect(input).toHaveCSS('border-radius', '12px')
      await expect(input).toHaveCSS('padding', '16px')
      
      // 测试焦点样式
      await input.focus()
      await expect(input).toBeFocused()
    }
    
    // 验证提交按钮样式
    const submitBtn = page.locator('.submit-btn')
    await expect(submitBtn).toHaveCSS('border-radius', '12px')
    await expect(submitBtn).toHaveCSS('font-weight', '600')
    
    // 测试按钮悬停效果
    await submitBtn.hover()
    // 悬停后应该有变化，但具体值可能因浏览器而异
    await expect(submitBtn).toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 })
    const authContainer = page.locator('.auth-container')
    await expect(authContainer).toBeVisible()
    
    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(authContainer).toBeVisible()
    
    // 测试手机端
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(authContainer).toBeVisible()
    
    // 恢复默认视口
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('页面截图对比 - 登录状态', async ({ page }) => {
    // 等待动画完成
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('auth-page-login.png', {
      fullPage: true,
      animations: 'disabled'
    })
    
    // 品牌区域截图
    const brandSection = page.locator('.brand-section')
    await expect(brandSection).toHaveScreenshot('auth-brand-section.png', {
      animations: 'disabled'
    })
    
    // 表单区域截图
    const formSection = page.locator('.form-section')
    await expect(formSection).toHaveScreenshot('auth-form-login.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 注册状态', async ({ page }) => {
    // 切换到注册模式
    const switchBtn = page.locator('.switch-btn')
    await switchBtn.click()
    await page.waitForTimeout(500)
    
    // 注册状态全页面截图
    await expect(page).toHaveScreenshot('auth-page-register.png', {
      fullPage: true,
      animations: 'disabled'
    })
    
    // 注册表单截图
    const formSection = page.locator('.form-section')
    await expect(formSection).toHaveScreenshot('auth-form-register.png', {
      animations: 'disabled'
    })
  })
}) 