import { test, expect } from '@playwright/test'

test.describe('咨询建议页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/advice')
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.advice-page', { timeout: 10000 })
    // 等待数据加载
    await page.waitForTimeout(1000)
  })

  test('页面整体结构和导航栏', async ({ page }) => {
    // 验证页面根元素
    const pageElement = page.locator('.advice-page')
    await expect(pageElement).toBeVisible()
    
    // 验证导航栏
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    await expect(navbar).toHaveCSS('position', 'fixed')
    await expect(navbar).toHaveCSS('z-index', '1000')
    
    // 验证Logo
    const logo = page.locator('.logo')
    await expect(logo).toBeVisible()
    await expect(logo).toContainText('小概率')
  })

  test('DAO治理中心主体验证', async ({ page }) => {
    // 验证DAO区域
    const daoSection = page.locator('.dao-section')
    await expect(daoSection).toBeVisible()
    
    // 验证页面头部
    const sectionHeader = page.locator('.section-header')
    await expect(sectionHeader).toBeVisible()
    
    // 验证主标题
    const sectionTitle = page.locator('.section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('DAO治理中心')
    
    // 验证副标题
    const sectionSubtitle = page.locator('.section-subtitle')
    await expect(sectionSubtitle).toBeVisible()
    await expect(sectionSubtitle).toContainText('社区驱动，共建未来')
  })

  test('统计数据行验证', async ({ page }) => {
    // 验证统计数据行
    const statsRow = page.locator('.stats-row')
    await expect(statsRow).toBeVisible()
    
    // 验证统计项目数量
    const statItems = page.locator('.stat-item')
    await expect(statItems).toHaveCount(4)
    
    // 验证总提案数
    const totalProposalsStat = statItems.nth(0)
    await expect(totalProposalsStat.locator('.stat-label')).toContainText('总提案数')
    await expect(totalProposalsStat.locator('.stat-number')).toBeVisible()
    
    // 验证活跃提案
    const activeProposalsStat = statItems.nth(1)
    await expect(activeProposalsStat.locator('.stat-label')).toContainText('活跃提案')
    await expect(activeProposalsStat.locator('.stat-number')).toBeVisible()
    
    // 验证总投票数
    const totalVotesStat = statItems.nth(2)
    await expect(totalVotesStat.locator('.stat-label')).toContainText('总投票数')
    await expect(totalVotesStat.locator('.stat-number')).toBeVisible()
    
    // 验证参与人数
    const participantStat = statItems.nth(3)
    await expect(participantStat.locator('.stat-label')).toContainText('参与人数')
    await expect(participantStat.locator('.stat-number')).toBeVisible()
  })

  test('过滤和排序区域验证', async ({ page }) => {
    // 验证过滤区域
    const filterSection = page.locator('.filter-section')
    await expect(filterSection).toBeVisible()
    
    // 验证过滤选项
    const filterOptions = page.locator('.filter-options')
    await expect(filterOptions).toBeVisible()
    
    const filterOptionElements = page.locator('.filter-option')
    await expect(filterOptionElements).toHaveCount.greaterThan(0)
    
    // 验证活跃过滤选项
    const activeFilter = page.locator('.filter-option.active')
    await expect(activeFilter).toBeVisible()
    
    // 验证排序选择器
    const sortSelect = page.locator('.sort-select')
    await expect(sortSelect).toBeVisible()
    
    // 验证排序选项
    const sortOptions = sortSelect.locator('option')
    await expect(sortOptions).toHaveCount(3)
    
    await expect(sortOptions.nth(0)).toHaveAttribute('value', 'latest')
    await expect(sortOptions.nth(1)).toHaveAttribute('value', 'popular')
    await expect(sortOptions.nth(2)).toHaveAttribute('value', 'urgent')
  })

  test('治理提案列表验证', async ({ page }) => {
    // 验证提案网格
    const proposalsGrid = page.locator('.proposals-grid')
    await expect(proposalsGrid).toBeVisible()
    
    // 验证提案卡片
    const proposalCards = page.locator('.proposal-card')
    await expect(proposalCards).toHaveCount.greaterThan(0)
    
    // 验证第一个提案卡片的结构
    const firstProposal = proposalCards.nth(0)
    
    // 验证提案头部
    const proposalHeader = firstProposal.locator('.proposal-header')
    await expect(proposalHeader).toBeVisible()
    
    const proposalId = firstProposal.locator('.proposal-id')
    const proposalStatus = firstProposal.locator('.proposal-status')
    await expect(proposalId).toBeVisible()
    await expect(proposalStatus).toBeVisible()
    await expect(proposalStatus).toHaveClass(/status-/)
    
    // 验证提案主体
    const proposalBody = firstProposal.locator('.proposal-body')
    await expect(proposalBody).toBeVisible()
    
    const proposalTitle = firstProposal.locator('.proposal-title')
    const proposalMeta = firstProposal.locator('.proposal-meta')
    const proposalSummary = firstProposal.locator('.proposal-summary')
    await expect(proposalTitle).toBeVisible()
    await expect(proposalMeta).toBeVisible()
    await expect(proposalSummary).toBeVisible()
  })

  test('提案卡片进度条验证', async ({ page }) => {
    // 验证第一个提案的进度容器
    const firstProposal = page.locator('.proposal-card').nth(0)
    const progressContainer = firstProposal.locator('.progress-container')
    await expect(progressContainer).toBeVisible()
    
    // 验证进度标签
    const progressLabels = progressContainer.locator('.progress-labels')
    await expect(progressLabels).toBeVisible()
    
    const labelSpans = progressLabels.locator('span')
    await expect(labelSpans.nth(0)).toContainText('支持数')
    await expect(labelSpans.nth(1)).toContainText('已收集')
    
    // 验证进度条
    const progressBar = progressContainer.locator('.progress-bar')
    await expect(progressBar).toBeVisible()
    
    const progressFill = progressContainer.locator('.progress-fill')
    await expect(progressFill).toBeVisible()
    await expect(progressFill).toHaveCSS('width')
  })

  test('提案卡片底部验证', async ({ page }) => {
    // 验证第一个提案的底部
    const firstProposal = page.locator('.proposal-card').nth(0)
    const proposalFooter = firstProposal.locator('.proposal-footer')
    await expect(proposalFooter).toBeVisible()
    
    // 验证统计信息
    const stats = proposalFooter.locator('.stats')
    await expect(stats).toBeVisible()
    
    const statElements = stats.locator('.stat')
    await expect(statElements).toHaveCount(2)
    
    // 验证建议统计
    const suggestionsStat = statElements.nth(0)
    await expect(suggestionsStat.locator('i')).toHaveClass(/fa-lightbulb/)
    await expect(suggestionsStat.locator('span')).toContainText('个建议')
    
    // 验证关注统计
    const followersStat = statElements.nth(1)
    await expect(followersStat.locator('i')).toHaveClass(/fa-users/)
    await expect(followersStat.locator('span')).toContainText('关注')
    
    // 验证参与按钮
    const participateBtn = proposalFooter.locator('.btn.btn-outline')
    await expect(participateBtn).toBeVisible()
    await expect(participateBtn).toContainText('参与建议')
  })

  test('建议提交面板验证', async ({ page }) => {
    // 验证建议面板
    const suggestionPanel = page.locator('.suggestion-panel')
    await expect(suggestionPanel).toBeVisible()
    
    // 验证面板头部
    const panelHeader = suggestionPanel.locator('.panel-header')
    await expect(panelHeader).toBeVisible()
    
    const panelTitle = suggestionPanel.locator('.panel-title')
    const panelSubtitle = suggestionPanel.locator('.panel-subtitle')
    await expect(panelTitle).toBeVisible()
    await expect(panelTitle).toContainText('发起治理提案')
    await expect(panelSubtitle).toBeVisible()
    await expect(panelSubtitle).toContainText('作为DevMatch DAO成员')
    
    // 验证统计信息
    const stats = panelHeader.locator('.stats')
    await expect(stats).toBeVisible()
    await expect(stats.locator('i')).toHaveClass(/fa-lightbulb/)
    await expect(stats).toContainText('本周已收集')
  })

  test('提案表单验证', async ({ page }) => {
    // 验证表单存在
    const form = page.locator('.suggestion-panel form')
    await expect(form).toBeVisible()
    
    // 验证标题字段
    const titleGroup = form.locator('.form-group').nth(0)
    await expect(titleGroup.locator('.form-label')).toContainText('提案标题')
    await expect(titleGroup.locator('.form-control')).toHaveAttribute('placeholder', '简洁明确地描述您的提案内容')
    await expect(titleGroup.locator('.character-count')).toContainText('/60')
    
    // 验证分类字段
    const categoryGroup = form.locator('.form-group').nth(1)
    await expect(categoryGroup.locator('.form-label')).toContainText('提案分类')
    
    const categorySelect = categoryGroup.locator('select')
    await expect(categorySelect).toBeVisible()
    
    const categoryOptions = categorySelect.locator('option')
    await expect(categoryOptions).toHaveCount(7) // 包括默认选项
    
    // 验证描述字段
    const descriptionGroup = form.locator('.form-group').nth(2)
    await expect(descriptionGroup.locator('.form-label')).toContainText('提案详情')
    await expect(descriptionGroup.locator('textarea')).toHaveAttribute('placeholder')
    await expect(descriptionGroup.locator('.character-count')).toContainText('/1500')
    
    // 验证支持论据字段
    const evidenceGroup = form.locator('.form-group').nth(3)
    await expect(evidenceGroup.locator('.form-label')).toContainText('支持论据')
    await expect(evidenceGroup.locator('textarea')).toBeVisible()
    await expect(evidenceGroup.locator('.character-count')).toContainText('/1000')
  })

  test('影响评估字段验证', async ({ page }) => {
    // 验证影响评估字段
    const impactGroup = page.locator('.form-group').nth(4)
    await expect(impactGroup.locator('.form-label')).toContainText('影响评估')
    
    // 验证影响选项
    const impactOptions = impactGroup.locator('.filter-options')
    await expect(impactOptions).toBeVisible()
    
    const impactOptionElements = impactOptions.locator('.filter-option')
    await expect(impactOptionElements).toHaveCount.greaterThan(0)
    
    // 验证按钮组
    const btnGroup = page.locator('.btn-group')
    await expect(btnGroup).toBeVisible()
    
    const resetBtn = btnGroup.locator('.btn.btn-outline')
    const submitBtn = btnGroup.locator('.btn.btn-primary')
    await expect(resetBtn).toBeVisible()
    await expect(resetBtn).toContainText('重置')
    await expect(submitBtn).toBeVisible()
    await expect(submitBtn).toContainText('发起提案')
  })

  test('分类指南验证', async ({ page }) => {
    // 验证分类指南区域
    const categoriesGuide = page.locator('.categories-guide')
    await expect(categoriesGuide).toBeVisible()
    
    // 验证分类标题
    const categoriesTitle = page.locator('.categories-title')
    await expect(categoriesTitle).toBeVisible()
    await expect(categoriesTitle).toContainText('DAO治理分类指南')
  })

  test('过滤选项交互验证', async ({ page }) => {
    // 获取过滤选项
    const filterOptions = page.locator('.filter-option')
    const optionCount = await filterOptions.count()
    
    if (optionCount > 1) {
      // 点击第二个过滤选项
      await filterOptions.nth(1).click()
      await page.waitForTimeout(500)
      
      // 验证选项切换
      const newActiveFilter = page.locator('.filter-option.active')
      await expect(newActiveFilter).toBeVisible()
      
      // 验证提案列表更新
      const proposalsGrid = page.locator('.proposals-grid')
      await expect(proposalsGrid).toBeVisible()
    }
  })

  test('排序功能验证', async ({ page }) => {
    // 测试排序选择器
    const sortSelect = page.locator('.sort-select')
    
    // 切换到"最受欢迎"
    await sortSelect.selectOption('popular')
    await page.waitForTimeout(500)
    
    // 验证选择生效
    await expect(sortSelect).toHaveValue('popular')
    
    // 切换到"最紧急"
    await sortSelect.selectOption('urgent')
    await page.waitForTimeout(500)
    
    await expect(sortSelect).toHaveValue('urgent')
  })

  test('表单输入验证', async ({ page }) => {
    // 测试表单输入
    const titleInput = page.locator('input[type="text"]')
    await titleInput.fill('测试提案标题')
    await expect(titleInput).toHaveValue('测试提案标题')
    
    // 验证字符计数更新
    const titleCount = page.locator('.form-group').nth(0).locator('.character-count')
    await expect(titleCount).toContainText('8/60')
    
    // 测试分类选择
    const categorySelect = page.locator('select')
    await categorySelect.selectOption('feature')
    await expect(categorySelect).toHaveValue('feature')
    
    // 测试描述输入
    const descriptionTextarea = page.locator('textarea').nth(0)
    await descriptionTextarea.fill('这是一个测试提案的详细描述')
    await expect(descriptionTextarea).toHaveValue('这是一个测试提案的详细描述')
  })

  test('影响评估选择验证', async ({ page }) => {
    // 测试影响评估选项选择
    const impactOptions = page.locator('.form-group').nth(4).locator('.filter-option')
    const optionCount = await impactOptions.count()
    
    if (optionCount > 0) {
      // 点击第一个影响选项
      await impactOptions.nth(0).click()
      await page.waitForTimeout(300)
      
      // 验证选项激活
      const activeImpact = page.locator('.form-group').nth(4).locator('.filter-option.active')
      await expect(activeImpact).toBeVisible()
    }
  })

  test('提案参与按钮验证', async ({ page }) => {
    // 点击第一个提案的参与按钮
    const firstParticipateBtn = page.locator('.proposal-card').nth(0).locator('.btn.btn-outline')
    await firstParticipateBtn.click()
    await page.waitForTimeout(500)
    
    // 验证按钮交互（可能有状态变化或弹窗）
    await expect(firstParticipateBtn).toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 桌面端验证
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
    
    const proposalsGrid = page.locator('.proposals-grid')
    await expect(proposalsGrid).toBeVisible()
    
    // 平板端验证
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    await expect(proposalsGrid).toBeVisible()
    
    // 手机端验证
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    await expect(proposalsGrid).toBeVisible()
    
    // 恢复桌面端
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
  })

  test('页面截图对比 - 完整页面', async ({ page }) => {
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    // 滚动到顶部
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('advice-full.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })

  test('页面截图对比 - DAO治理中心头部', async ({ page }) => {
    // 等待头部区域加载
    await page.waitForSelector('.section-header', { timeout: 5000 })
    await page.waitForTimeout(1000)
    
    // 头部区域截图
    const sectionHeader = page.locator('.section-header')
    await expect(sectionHeader).toHaveScreenshot('advice-header.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 提案列表', async ({ page }) => {
    // 滚动到提案列表区域
    await page.locator('.proposals-grid').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 提案列表截图
    const proposalsGrid = page.locator('.proposals-grid')
    await expect(proposalsGrid).toHaveScreenshot('advice-proposals.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 建议提交面板', async ({ page }) => {
    // 滚动到建议面板区域
    await page.locator('.suggestion-panel').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 建议面板截图
    const suggestionPanel = page.locator('.suggestion-panel')
    await expect(suggestionPanel).toHaveScreenshot('advice-suggestion-panel.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 提案卡片详情', async ({ page }) => {
    // 第一个提案卡片截图
    const firstProposal = page.locator('.proposal-card').nth(0)
    await expect(firstProposal).toHaveScreenshot('advice-proposal-card.png', {
      animations: 'disabled'
    })
  })
}) 