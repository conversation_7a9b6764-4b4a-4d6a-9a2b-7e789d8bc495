import { test, expect } from '@playwright/test'

test.describe('技能学院页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/academy')
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.academy-page', { timeout: 10000 })
    // 等待数据加载
    await page.waitForTimeout(1000)
  })

  test('页面整体结构和导航栏', async ({ page }) => {
    // 验证页面根元素
    const pageElement = page.locator('.academy-page')
    await expect(pageElement).toBeVisible()
    
    // 验证导航栏
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    await expect(navbar).toHaveCSS('position', 'fixed')
    await expect(navbar).toHaveCSS('top', '0px')
    await expect(navbar).toHaveCSS('z-index', '1000')
    
    // 验证Logo
    const logo = page.locator('.logo')
    await expect(logo).toBeVisible()
    await expect(logo).toContainText('小概率')
  })

  test('英雄区域验证', async ({ page }) => {
    // 验证英雄区域容器
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toBeVisible()
    
    const heroContainer = page.locator('.hero-container')
    await expect(heroContainer).toBeVisible()
    
    // 验证标语
    const heroBadge = page.locator('.hero-badge')
    await expect(heroBadge).toBeVisible()
    await expect(heroBadge).toContainText('🎓 技能提升平台')
    
    // 验证标题
    const heroTitle = page.locator('.hero-title')
    await expect(heroTitle).toBeVisible()
    await expect(heroTitle).toContainText('技能学院')
    
    // 验证副标题
    const heroSubtitle = page.locator('.hero-subtitle')
    await expect(heroSubtitle).toBeVisible()
    await expect(heroSubtitle).toContainText('从基础到进阶，系统化学习路径')
    
    // 验证按钮
    const heroButtons = page.locator('.hero-buttons')
    await expect(heroButtons).toBeVisible()
    
    const primaryBtn = page.locator('.hero-btn-primary')
    const secondaryBtn = page.locator('.hero-btn-secondary')
    await expect(primaryBtn).toBeVisible()
    await expect(primaryBtn).toContainText('开始学习')
    await expect(secondaryBtn).toBeVisible()
    await expect(secondaryBtn).toContainText('查看项目')
  })

  test('英雄统计数据验证', async ({ page }) => {
    // 验证统计数据容器
    const heroStats = page.locator('.hero-stats')
    await expect(heroStats).toBeVisible()
    
    // 验证统计项目
    const heroStatElements = page.locator('.hero-stat')
    await expect(heroStatElements).toHaveCount(3)
    
    // 验证第一个统计项（精品课程）
    const courseStat = heroStatElements.nth(0)
    await expect(courseStat.locator('.hero-stat-number')).toContainText('200+')
    await expect(courseStat.locator('.hero-stat-label')).toContainText('精品课程')
    
    // 验证第二个统计项（学习者）
    const studentStat = heroStatElements.nth(1)
    await expect(studentStat.locator('.hero-stat-number')).toContainText('50K+')
    await expect(studentStat.locator('.hero-stat-label')).toContainText('学习者')
    
    // 验证第三个统计项（专业导师）
    const mentorStat = heroStatElements.nth(2)
    await expect(mentorStat.locator('.hero-stat-number')).toContainText('100+')
    await expect(mentorStat.locator('.hero-stat-label')).toContainText('专业导师')
  })

  test('技能树可视化验证', async ({ page }) => {
    // 验证技能树容器
    const skillTree = page.locator('.skill-tree')
    await expect(skillTree).toBeVisible()
    
    // 验证技能树标题
    const treeTitle = page.locator('.tree-title')
    await expect(treeTitle).toBeVisible()
    await expect(treeTitle).toContainText('技能成长路径')
    
    // 验证技能等级
    const skillLevels = page.locator('.skill-levels')
    await expect(skillLevels).toBeVisible()
    
    const skillLevelElements = page.locator('.skill-level')
    await expect(skillLevelElements).toHaveCount(5)
    
    // 验证第一个技能等级
    const firstLevel = skillLevelElements.nth(0)
    await expect(firstLevel.locator('.level-dot')).toBeVisible()
    await expect(firstLevel.locator('.level-text')).toContainText('入门基础')
    
    // 验证激活状态
    const activeLevels = page.locator('.skill-level .level-dot.active')
    await expect(activeLevels).toHaveCount.greaterThan(0)
  })

  test('学习路径区域验证', async ({ page }) => {
    // 验证学习路径区域
    const learningPaths = page.locator('.learning-paths')
    await expect(learningPaths).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.learning-paths .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('学习路径')
    
    // 验证副标题
    const sectionSubtitle = page.locator('.learning-paths .section-subtitle')
    await expect(sectionSubtitle).toBeVisible()
    await expect(sectionSubtitle).toContainText('选择适合你的学习路径')
    
    // 验证路径网格
    const pathsGrid = page.locator('.paths-grid')
    await expect(pathsGrid).toBeVisible()
    
    // 验证路径卡片
    const pathCards = page.locator('.path-card')
    await expect(pathCards).toHaveCount.greaterThan(0)
    
    // 验证第一个路径卡片的结构
    const firstPathCard = pathCards.nth(0)
    await expect(firstPathCard.locator('.path-icon')).toBeVisible()
    await expect(firstPathCard.locator('.path-title')).toBeVisible()
    await expect(firstPathCard.locator('.path-desc')).toBeVisible()
    await expect(firstPathCard.locator('.path-meta')).toBeVisible()
    await expect(firstPathCard.locator('.path-features')).toBeVisible()
    await expect(firstPathCard.locator('.path-btn')).toBeVisible()
  })

  test('学习路径卡片详细验证', async ({ page }) => {
    // 验证路径卡片元数据
    const pathCards = page.locator('.path-card')
    const firstPathCard = pathCards.nth(0)
    
    // 验证路径时长
    const pathDuration = firstPathCard.locator('.path-duration')
    await expect(pathDuration).toBeVisible()
    
    // 验证路径等级
    const pathLevel = firstPathCard.locator('.path-level')
    await expect(pathLevel).toBeVisible()
    await expect(pathLevel).toHaveClass(/level-/)
    
    // 验证路径特色列表
    const pathFeatures = firstPathCard.locator('.path-features li')
    await expect(pathFeatures).toHaveCount.greaterThan(0)
    
    // 验证开始学习按钮
    const pathBtn = firstPathCard.locator('.path-btn')
    await expect(pathBtn).toBeVisible()
    await expect(pathBtn).toContainText('开始学习')
  })

  test('实战项目区域验证', async ({ page }) => {
    // 验证实战项目区域
    const projectsSection = page.locator('.projects-section')
    await expect(projectsSection).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.projects-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('实战项目练习')
    
    // 验证副标题
    const sectionSubtitle = page.locator('.projects-section .section-subtitle')
    await expect(sectionSubtitle).toBeVisible()
    await expect(sectionSubtitle).toContainText('通过真实项目练习')
    
    // 验证项目标签
    const projectTabs = page.locator('.projects-tabs')
    await expect(projectTabs).toBeVisible()
    
    const projectTabElements = page.locator('.project-tab')
    await expect(projectTabElements).toHaveCount.greaterThan(0)
    
    // 验证活跃标签
    const activeTab = page.locator('.project-tab.active')
    await expect(activeTab).toBeVisible()
    
    // 验证项目网格
    const projectsGrid = page.locator('.projects-grid')
    await expect(projectsGrid).toBeVisible()
    
    // 验证项目卡片
    const projectCards = page.locator('.project-card')
    await expect(projectCards).toHaveCount.greaterThan(0)
  })

  test('项目卡片详细验证', async ({ page }) => {
    // 验证第一个项目卡片
    const firstProjectCard = page.locator('.project-card').nth(0)
    
    // 验证项目图片
    const projectImage = firstProjectCard.locator('.project-image')
    await expect(projectImage).toBeVisible()
    
    // 验证项目内容
    const projectContent = firstProjectCard.locator('.project-content')
    await expect(projectContent).toBeVisible()
    
    // 验证项目标题
    const projectTitle = firstProjectCard.locator('.project-title')
    await expect(projectTitle).toBeVisible()
    
    // 验证项目描述
    const projectDesc = firstProjectCard.locator('.project-desc')
    await expect(projectDesc).toBeVisible()
    
    // 验证技能标签
    const projectSkills = firstProjectCard.locator('.project-skills')
    await expect(projectSkills).toBeVisible()
    
    const skillTags = firstProjectCard.locator('.skill-tag')
    await expect(skillTags).toHaveCount.greaterThan(0)
    
    // 验证项目底部
    const projectFooter = firstProjectCard.locator('.project-footer')
    await expect(projectFooter).toBeVisible()
    
    // 验证难度标识
    const projectDifficulty = firstProjectCard.locator('.project-difficulty')
    await expect(projectDifficulty).toBeVisible()
    await expect(projectDifficulty).toHaveClass(/difficulty-/)
    
    // 验证项目按钮
    const projectBtn = firstProjectCard.locator('.project-btn')
    await expect(projectBtn).toBeVisible()
    await expect(projectBtn).toContainText('开始项目')
  })

  test('项目标签切换功能验证', async ({ page }) => {
    // 获取项目标签
    const projectTabs = page.locator('.project-tab')
    const tabCount = await projectTabs.count()
    
    if (tabCount > 1) {
      // 点击第二个标签
      await projectTabs.nth(1).click()
      await page.waitForTimeout(500)
      
      // 验证标签切换
      const newActiveTab = page.locator('.project-tab.active')
      await expect(newActiveTab).toBeVisible()
      
      // 验证项目列表更新
      const projectsGrid = page.locator('.projects-grid')
      await expect(projectsGrid).toBeVisible()
    }
  })

  test('导师团队区域验证', async ({ page }) => {
    // 验证导师团队区域
    const mentorsSection = page.locator('.mentors-section')
    await expect(mentorsSection).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.mentors-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('专业导师团队')
    
    // 验证副标题
    const sectionSubtitle = page.locator('.mentors-section .section-subtitle')
    await expect(sectionSubtitle).toBeVisible()
    await expect(sectionSubtitle).toContainText('来自一线大厂的资深工程师')
    
    // 验证导师网格
    const mentorsGrid = page.locator('.mentors-grid')
    await expect(mentorsGrid).toBeVisible()
    
    // 验证导师卡片
    const mentorCards = page.locator('.mentor-card')
    await expect(mentorCards).toHaveCount.greaterThan(0)
    
    // 验证第一个导师卡片结构
    const firstMentorCard = mentorCards.nth(0)
    await expect(firstMentorCard.locator('.mentor-avatar')).toBeVisible()
    await expect(firstMentorCard.locator('.mentor-name')).toBeVisible()
    await expect(firstMentorCard.locator('.mentor-title')).toBeVisible()
    await expect(firstMentorCard.locator('.mentor-company')).toBeVisible()
    await expect(firstMentorCard.locator('.mentor-skills')).toBeVisible()
  })

  test('导师卡片详细验证', async ({ page }) => {
    // 验证导师技能标签
    const firstMentorCard = page.locator('.mentor-card').nth(0)
    
    const mentorSkills = firstMentorCard.locator('.mentor-skills')
    await expect(mentorSkills).toBeVisible()
    
    const mentorSkillElements = firstMentorCard.locator('.mentor-skill')
    await expect(mentorSkillElements).toHaveCount.greaterThan(0)
    
    // 验证导师信息格式
    const mentorCompany = firstMentorCard.locator('.mentor-company')
    await expect(mentorCompany).toBeVisible()
    await expect(mentorCompany).toContainText('年经验')
  })

  test('CTA区域验证', async ({ page }) => {
    // 验证CTA区域
    const ctaSection = page.locator('.cta-section')
    await expect(ctaSection).toBeVisible()
    
    // 验证CTA标题
    const ctaTitle = page.locator('.cta-title')
    await expect(ctaTitle).toBeVisible()
    await expect(ctaTitle).toContainText('开始你的技能提升之旅')
    
    // 验证CTA副标题
    const ctaSubtitle = page.locator('.cta-subtitle')
    await expect(ctaSubtitle).toBeVisible()
    await expect(ctaSubtitle).toContainText('不要让技能短板限制你的发展')
    
    // 验证CTA按钮
    const ctaButtons = page.locator('.cta-buttons')
    await expect(ctaButtons).toBeVisible()
    
    const ctaPrimary = page.locator('.cta-btn-primary')
    const ctaSecondary = page.locator('.cta-btn-secondary')
    await expect(ctaPrimary).toBeVisible()
    await expect(ctaPrimary).toContainText('免费试学')
    await expect(ctaSecondary).toBeVisible()
    await expect(ctaSecondary).toContainText('咨询课程')
  })

  test('路径卡片悬停效果验证', async ({ page }) => {
    // 验证路径卡片悬停效果
    const pathCards = page.locator('.path-card')
    const firstPathCard = pathCards.nth(0)
    
    // 悬停前
    await expect(firstPathCard).toBeVisible()
    
    // 悬停
    await firstPathCard.hover()
    await page.waitForTimeout(300)
    
    // 验证悬停效果（卡片应该有变化）
    await expect(firstPathCard).toBeVisible()
  })

  test('项目卡片悬停效果验证', async ({ page }) => {
    // 验证项目卡片悬停效果
    const projectCards = page.locator('.project-card')
    const firstProjectCard = projectCards.nth(0)
    
    // 悬停前
    await expect(firstProjectCard).toBeVisible()
    
    // 悬停
    await firstProjectCard.hover()
    await page.waitForTimeout(300)
    
    // 验证悬停效果
    await expect(firstProjectCard).toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 桌面端验证
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
    
    const heroContainer = page.locator('.hero-container')
    await expect(heroContainer).toBeVisible()
    
    // 平板端验证
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    await expect(heroContainer).toBeVisible()
    
    // 手机端验证
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    await expect(heroContainer).toBeVisible()
    
    // 恢复桌面端
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
  })

  test('页面截图对比 - 完整页面', async ({ page }) => {
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    // 滚动到顶部
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('academy-full.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 英雄区域', async ({ page }) => {
    // 等待英雄区域加载
    await page.waitForSelector('.hero-section', { timeout: 5000 })
    await page.waitForTimeout(1000)
    
    // 英雄区域截图
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toHaveScreenshot('academy-hero.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 学习路径区域', async ({ page }) => {
    // 滚动到学习路径区域
    await page.locator('.learning-paths').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 学习路径区域截图
    const learningPaths = page.locator('.learning-paths')
    await expect(learningPaths).toHaveScreenshot('academy-learning-paths.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 实战项目区域', async ({ page }) => {
    // 滚动到实战项目区域
    await page.locator('.projects-section').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 实战项目区域截图
    const projectsSection = page.locator('.projects-section')
    await expect(projectsSection).toHaveScreenshot('academy-projects.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 导师团队区域', async ({ page }) => {
    // 滚动到导师团队区域
    await page.locator('.mentors-section').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 导师团队区域截图
    const mentorsSection = page.locator('.mentors-section')
    await expect(mentorsSection).toHaveScreenshot('academy-mentors.png', {
      animations: 'disabled'
    })
  })
}) 