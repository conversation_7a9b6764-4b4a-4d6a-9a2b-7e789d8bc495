import { test, expect } from '@playwright/test'

test.describe('投资生态页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/investment')
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.investment-page', { timeout: 10000 })
    // 等待数据加载
    await page.waitForTimeout(1000)
  })

  test('页面整体结构和导航栏', async ({ page }) => {
    // 验证页面根元素
    const pageElement = page.locator('.investment-page')
    await expect(pageElement).toBeVisible()
    
    // 验证导航栏
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    await expect(navbar).toHaveCSS('position', 'fixed')
    await expect(navbar).toHaveCSS('top', '0px')
    await expect(navbar).toHaveCSS('z-index', '1000')
    
    // 验证Logo
    const logo = page.locator('.logo')
    await expect(logo).toBeVisible()
    await expect(logo).toContainText('小概率')
  })

  test('英雄区域验证', async ({ page }) => {
    // 验证英雄区域容器
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toBeVisible()
    
    const heroContainer = page.locator('.hero-container')
    await expect(heroContainer).toBeVisible()
    
    // 验证标语
    const heroBadge = page.locator('.hero-badge')
    await expect(heroBadge).toBeVisible()
    await expect(heroBadge).toContainText('💰 投资孵化平台')
    
    // 验证标题
    const heroTitle = page.locator('.hero-title')
    await expect(heroTitle).toBeVisible()
    await expect(heroTitle).toContainText('投资生态')
    
    // 验证副标题
    const heroSubtitle = page.locator('.hero-subtitle')
    await expect(heroSubtitle).toBeVisible()
    await expect(heroSubtitle).toContainText('连接优质项目与专业投资人')
  })

  test('投资统计数据验证', async ({ page }) => {
    // 验证统计数据容器
    const heroStats = page.locator('.hero-stats')
    await expect(heroStats).toBeVisible()
    
    // 验证统计项目数量
    const heroStatElements = page.locator('.hero-stat')
    await expect(heroStatElements).toHaveCount(4)
    
    // 验证累计投资金额
    const investmentStat = heroStatElements.nth(0)
    await expect(investmentStat.locator('.hero-stat-number')).toContainText('5.8亿')
    await expect(investmentStat.locator('.hero-stat-label')).toContainText('累计投资金额')
    
    // 验证投资项目数
    const projectStat = heroStatElements.nth(1)
    await expect(projectStat.locator('.hero-stat-number')).toContainText('320+')
    await expect(projectStat.locator('.hero-stat-label')).toContainText('投资项目数')
    
    // 验证注册投资人
    const investorStat = heroStatElements.nth(2)
    await expect(investorStat.locator('.hero-stat-number')).toContainText('1200+')
    await expect(investorStat.locator('.hero-stat-label')).toContainText('注册投资人')
    
    // 验证成功退出项目
    const exitStat = heroStatElements.nth(3)
    await expect(exitStat.locator('.hero-stat-number')).toContainText('45')
    await expect(exitStat.locator('.hero-stat-label')).toContainText('成功退出项目')
  })

  test('双列布局区域验证', async ({ page }) => {
    // 验证双列区域
    const dualSection = page.locator('.dual-section')
    await expect(dualSection).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.dual-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('投资生态体系')
    
    // 验证副标题
    const sectionSubtitle = page.locator('.dual-section .section-subtitle')
    await expect(sectionSubtitle).toBeVisible()
    await expect(sectionSubtitle).toContainText('无论你是投资人还是创业者')
    
    // 验证双列网格
    const dualGrid = page.locator('.dual-grid')
    await expect(dualGrid).toBeVisible()
    
    // 验证两个卡片
    const sectionCards = page.locator('.section-card')
    await expect(sectionCards).toHaveCount(2)
  })

  test('投资人生态卡片验证', async ({ page }) => {
    // 验证投资人卡片
    const investorCard = page.locator('.investor-card')
    await expect(investorCard).toBeVisible()
    
    // 验证卡片标题
    const cardTitle = investorCard.locator('.card-title')
    await expect(cardTitle).toBeVisible()
    await expect(cardTitle).toContainText('💼 投资人生态')
    
    // 验证卡片副标题
    const cardSubtitle = investorCard.locator('.card-subtitle')
    await expect(cardSubtitle).toBeVisible()
    await expect(cardSubtitle).toContainText('加入小概率投资人网络')
    
    // 验证权益列表
    const benefitList = investorCard.locator('.benefit-list')
    await expect(benefitList).toBeVisible()
    
    const benefitItems = investorCard.locator('.benefit-item')
    await expect(benefitItems).toHaveCount(5)
    
    // 验证第一个权益项
    const firstBenefit = benefitItems.nth(0)
    await expect(firstBenefit.locator('.benefit-icon')).toContainText('✓')
    await expect(firstBenefit.locator('.benefit-text')).toContainText('独家项目池')
    
    // 验证CTA按钮
    const cardCta = investorCard.locator('.card-cta')
    await expect(cardCta).toBeVisible()
    await expect(cardCta).toContainText('申请成为投资人')
  })

  test('创业者生态卡片验证', async ({ page }) => {
    // 验证创业者卡片
    const entrepreneurCard = page.locator('.entrepreneur-card')
    await expect(entrepreneurCard).toBeVisible()
    
    // 验证卡片标题
    const cardTitle = entrepreneurCard.locator('.card-title')
    await expect(cardTitle).toBeVisible()
    await expect(cardTitle).toContainText('🚀 创业者生态')
    
    // 验证卡片副标题
    const cardSubtitle = entrepreneurCard.locator('.card-subtitle')
    await expect(cardSubtitle).toBeVisible()
    await expect(cardSubtitle).toContainText('从想法到成功')
    
    // 验证权益列表
    const benefitList = entrepreneurCard.locator('.benefit-list')
    await expect(benefitList).toBeVisible()
    
    const benefitItems = entrepreneurCard.locator('.benefit-item')
    await expect(benefitItems).toHaveCount(5)
    
    // 验证第一个权益项
    const firstBenefit = benefitItems.nth(0)
    await expect(firstBenefit.locator('.benefit-icon')).toContainText('✓')
    await expect(firstBenefit.locator('.benefit-text')).toContainText('团队构建')
    
    // 验证CTA按钮
    const cardCta = entrepreneurCard.locator('.card-cta')
    await expect(cardCta).toBeVisible()
    await expect(cardCta).toContainText('提交创业计划')
  })

  test('独立开发大赛区域验证', async ({ page }) => {
    // 验证大赛区域
    const competitionSection = page.locator('.competition-section')
    await expect(competitionSection).toBeVisible()
    
    // 验证大赛背景
    const competitionBg = page.locator('.competition-bg')
    await expect(competitionBg).toBeVisible()
    
    // 验证大赛标题
    const competitionTitle = page.locator('.competition-title')
    await expect(competitionTitle).toBeVisible()
    await expect(competitionTitle).toContainText('小概率独立开发大赛')
    
    // 验证大赛副标题
    const competitionSubtitle = page.locator('.competition-subtitle')
    await expect(competitionSubtitle).toBeVisible()
    await expect(competitionSubtitle).toContainText('季度盛典')
    
    // 验证奖金高亮
    const prizeHighlight = page.locator('.prize-highlight')
    await expect(prizeHighlight).toBeVisible()
    
    const prizeAmount = page.locator('.prize-amount')
    await expect(prizeAmount).toBeVisible()
    await expect(prizeAmount).toContainText('￥500万')
    
    const prizeDesc = page.locator('.prize-desc')
    await expect(prizeDesc).toBeVisible()
    await expect(prizeDesc).toContainText('总奖金池 + 1000万投资基金')
  })

  test('大赛奖项网格验证', async ({ page }) => {
    // 验证大赛网格
    const competitionGrid = page.locator('.competition-grid')
    await expect(competitionGrid).toBeVisible()
    
    // 验证大赛特色项目
    const competitionFeatures = page.locator('.competition-feature')
    await expect(competitionFeatures).toHaveCount(5)
    
    // 验证特等奖
    const firstFeature = competitionFeatures.nth(0)
    await expect(firstFeature.locator('.comp-feature-icon')).toContainText('🏆')
    await expect(firstFeature.locator('.comp-feature-title')).toContainText('特等奖')
    await expect(firstFeature.locator('.comp-feature-desc')).toContainText('100万现金')
    
    // 验证一等奖
    const secondFeature = competitionFeatures.nth(1)
    await expect(secondFeature.locator('.comp-feature-icon')).toContainText('🥇')
    await expect(secondFeature.locator('.comp-feature-title')).toContainText('一等奖')
    await expect(secondFeature.locator('.comp-feature-desc')).toContainText('50万现金')
    
    // 验证二等奖
    const thirdFeature = competitionFeatures.nth(2)
    await expect(thirdFeature.locator('.comp-feature-icon')).toContainText('🥈')
    await expect(thirdFeature.locator('.comp-feature-title')).toContainText('二等奖')
    await expect(thirdFeature.locator('.comp-feature-desc')).toContainText('20万现金')
  })

  test('大赛CTA按钮验证', async ({ page }) => {
    // 验证大赛CTA区域
    const competitionCta = page.locator('.competition-cta')
    await expect(competitionCta).toBeVisible()
    
    // 验证大赛按钮
    const compBtns = page.locator('.comp-btn')
    await expect(compBtns).toHaveCount(2)
    
    // 验证报名按钮
    const joinBtn = compBtns.nth(0)
    await expect(joinBtn).toBeVisible()
    await expect(joinBtn).toContainText('立即报名参赛')
    
    // 验证查看往期按钮
    const viewBtn = compBtns.nth(1)
    await expect(viewBtn).toBeVisible()
    await expect(viewBtn).toContainText('查看往期获奖')
    await expect(viewBtn).toHaveClass(/comp-btn-secondary/)
  })

  test('投资机会CTA区域验证', async ({ page }) => {
    // 验证投资机会CTA区域
    const opportunityCta = page.locator('.opportunity-cta')
    await expect(opportunityCta).toBeVisible()
    
    // 验证标题
    const opportunityTitle = page.locator('.opportunity-title')
    await expect(opportunityTitle).toBeVisible()
    await expect(opportunityTitle).toContainText('抓住下一个投资机会')
    
    // 验证副标题
    const opportunitySubtitle = page.locator('.opportunity-subtitle')
    await expect(opportunitySubtitle).toBeVisible()
    await expect(opportunitySubtitle).toContainText('优质项目稀缺，机会转瞬即逝')
    
    // 验证按钮组
    const opportunityButtons = page.locator('.opportunity-buttons')
    await expect(opportunityButtons).toBeVisible()
    
    const opportunityBtns = page.locator('.opportunity-btn')
    await expect(opportunityBtns).toHaveCount(2)
    
    // 验证投资人认证按钮
    const certBtn = opportunityBtns.nth(0)
    await expect(certBtn).toBeVisible()
    await expect(certBtn).toContainText('申请投资人认证')
    await expect(certBtn).toHaveClass(/btn-purple/)
    
    // 验证了解投资流程按钮
    const learnBtn = opportunityBtns.nth(1)
    await expect(learnBtn).toBeVisible()
    await expect(learnBtn).toContainText('了解投资流程')
    await expect(learnBtn).toHaveClass(/btn-outline/)
  })

  test('权益列表详细验证', async ({ page }) => {
    // 验证投资人权益详细内容
    const investorCard = page.locator('.investor-card')
    const investorBenefits = investorCard.locator('.benefit-item')
    
    // 验证专业DD支持
    const ddBenefit = investorBenefits.nth(1)
    await expect(ddBenefit.locator('.benefit-text')).toContainText('专业DD支持')
    
    // 验证投后管理
    const postBenefit = investorBenefits.nth(2)
    await expect(postBenefit.locator('.benefit-text')).toContainText('投后管理')
    
    // 验证退出通道
    const exitBenefit = investorBenefits.nth(3)
    await expect(exitBenefit.locator('.benefit-text')).toContainText('退出通道')
    
    // 验证社区网络
    const networkBenefit = investorBenefits.nth(4)
    await expect(networkBenefit.locator('.benefit-text')).toContainText('社区网络')
    
    // 验证创业者权益详细内容
    const entrepreneurCard = page.locator('.entrepreneur-card')
    const entrepreneurBenefits = entrepreneurCard.locator('.benefit-item')
    
    // 验证技术支持
    const techBenefit = entrepreneurBenefits.nth(1)
    await expect(techBenefit.locator('.benefit-text')).toContainText('技术支持')
    
    // 验证资金扶持
    const fundBenefit = entrepreneurBenefits.nth(2)
    await expect(fundBenefit.locator('.benefit-text')).toContainText('资金扶持')
    
    // 验证商业赋能
    const businessBenefit = entrepreneurBenefits.nth(3)
    await expect(businessBenefit.locator('.benefit-text')).toContainText('商业赋能')
    
    // 验证成长加速
    const growthBenefit = entrepreneurBenefits.nth(4)
    await expect(growthBenefit.locator('.benefit-text')).toContainText('成长加速')
  })

  test('按钮交互功能验证', async ({ page }) => {
    // 验证投资人申请按钮点击
    const investorCta = page.locator('.investor-card .card-cta')
    await investorCta.click()
    await page.waitForTimeout(500)
    
    // 验证创业计划提交按钮点击
    const entrepreneurCta = page.locator('.entrepreneur-card .card-cta')
    await entrepreneurCta.click()
    await page.waitForTimeout(500)
    
    // 验证大赛报名按钮点击
    const joinCompBtn = page.locator('.comp-btn').nth(0)
    await joinCompBtn.click()
    await page.waitForTimeout(500)
    
    // 验证投资人认证按钮点击
    const certificationBtn = page.locator('.btn-purple')
    await certificationBtn.click()
    await page.waitForTimeout(500)
  })

  test('卡片悬停效果验证', async ({ page }) => {
    // 验证投资人卡片悬停效果
    const investorCard = page.locator('.investor-card')
    await investorCard.hover()
    await page.waitForTimeout(300)
    await expect(investorCard).toBeVisible()
    
    // 验证创业者卡片悬停效果
    const entrepreneurCard = page.locator('.entrepreneur-card')
    await entrepreneurCard.hover()
    await page.waitForTimeout(300)
    await expect(entrepreneurCard).toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 桌面端验证
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
    
    const dualGrid = page.locator('.dual-grid')
    await expect(dualGrid).toBeVisible()
    
    // 平板端验证
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    await expect(dualGrid).toBeVisible()
    
    // 手机端验证
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    await expect(dualGrid).toBeVisible()
    
    // 恢复桌面端
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
  })

  test('页面截图对比 - 完整页面', async ({ page }) => {
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    // 滚动到顶部
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('investment-full.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 英雄区域', async ({ page }) => {
    // 等待英雄区域加载
    await page.waitForSelector('.hero-section', { timeout: 5000 })
    await page.waitForTimeout(1000)
    
    // 英雄区域截图
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toHaveScreenshot('investment-hero.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 双列布局区域', async ({ page }) => {
    // 滚动到双列布局区域
    await page.locator('.dual-section').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 双列布局区域截图
    const dualSection = page.locator('.dual-section')
    await expect(dualSection).toHaveScreenshot('investment-dual-layout.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 大赛专区', async ({ page }) => {
    // 滚动到大赛区域
    await page.locator('.competition-section').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 大赛区域截图
    const competitionSection = page.locator('.competition-section')
    await expect(competitionSection).toHaveScreenshot('investment-competition.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 投资机会CTA', async ({ page }) => {
    // 滚动到CTA区域
    await page.locator('.opportunity-cta').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // CTA区域截图
    const opportunityCta = page.locator('.opportunity-cta')
    await expect(opportunityCta).toHaveScreenshot('investment-opportunity-cta.png', {
      animations: 'disabled'
    })
  })
}) 