import { test, expect } from '@playwright/test'

test.describe('项目详情页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/projects/1')
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.project-detail-page', { timeout: 10000 })
    // 等待项目数据加载
    await page.waitForTimeout(1000)
  })

  test('页面整体结构和导航栏', async ({ page }) => {
    // 验证页面根元素
    const pageElement = page.locator('.project-detail-page')
    await expect(pageElement).toBeVisible()
    
    // 验证浮动装饰背景
    const floatingShapes = page.locator('.floating-shapes')
    await expect(floatingShapes).toBeVisible()
    
    const shapes = page.locator('.floating-shapes .shape')
    await expect(shapes).toHaveCount(5)
    
    // 验证导航栏
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    await expect(navbar).toHaveCSS('position', 'fixed')
    await expect(navbar).toHaveCSS('top', '0px')
    await expect(navbar).toHaveCSS('z-index', '1000')
    
    // 验证Logo
    const logo = page.locator('.logo')
    await expect(logo).toBeVisible()
    await expect(logo).toContainText('小概率')
  })

  test('英雄区域验证', async ({ page }) => {
    // 验证英雄区域容器
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toBeVisible()
    
    const heroContainer = page.locator('.hero-container')
    await expect(heroContainer).toBeVisible()
    
    // 验证项目标语
    const heroBadge = page.locator('.hero-badge')
    await expect(heroBadge).toBeVisible()
    await expect(heroBadge).toContainText('🎮 独立游戏项目')
    
    // 验证标题
    const heroTitle = page.locator('.hero-title')
    await expect(heroTitle).toBeVisible()
    await expect(heroTitle).toContainText('时光旅者')
    
    // 验证副标题
    const heroSubtitle = page.locator('.hero-subtitle')
    await expect(heroSubtitle).toBeVisible()
    await expect(heroSubtitle).toContainText('跨越时空的冒险之旅')
    
    // 验证描述
    const heroDescription = page.locator('.hero-description')
    await expect(heroDescription).toBeVisible()
    await expect(heroDescription).toContainText('一款融合了时间旅行元素的2D像素风RPG游戏')
    
    // 验证按钮
    const heroButtons = page.locator('.hero-buttons')
    await expect(heroButtons).toBeVisible()
    
    const primaryBtn = page.locator('.hero-btn-primary')
    await expect(primaryBtn).toBeVisible()
    await expect(primaryBtn).toContainText('加入开发团队')
  })

  test('项目信息卡片验证', async ({ page }) => {
    // 验证信息卡片容器
    const infoCards = page.locator('.project-info-cards')
    await expect(infoCards).toBeVisible()
    
    // 验证信息卡片数量
    const infoCardElements = page.locator('.info-card')
    await expect(infoCardElements).toHaveCount(4)
    
    // 验证团队规模卡片
    const teamSizeCard = infoCardElements.nth(0)
    await expect(teamSizeCard.locator('.info-icon')).toContainText('👥')
    await expect(teamSizeCard.locator('.info-label')).toContainText('团队规模')
    await expect(teamSizeCard.locator('.info-value')).toContainText('人')
    
    // 验证项目周期卡片
    const durationCard = infoCardElements.nth(1)
    await expect(durationCard.locator('.info-icon')).toContainText('📅')
    await expect(durationCard.locator('.info-label')).toContainText('项目周期')
    
    // 验证项目阶段卡片
    const stageCard = infoCardElements.nth(2)
    await expect(stageCard.locator('.info-icon')).toContainText('🎯')
    await expect(stageCard.locator('.info-label')).toContainText('项目阶段')
    
    // 验证资金类型卡片
    const fundingCard = infoCardElements.nth(3)
    await expect(fundingCard.locator('.info-icon')).toContainText('💰')
    await expect(fundingCard.locator('.info-label')).toContainText('资金类型')
  })

  test('游戏预览区域验证', async ({ page }) => {
    // 验证游戏预览容器
    const gamePreview = page.locator('.game-preview')
    await expect(gamePreview).toBeVisible()
    
    // 验证预览容器
    const previewContainer = page.locator('.preview-container')
    await expect(previewContainer).toBeVisible()
    
    // 验证预览幻灯片
    const previewSlides = page.locator('.preview-slide')
    await expect(previewSlides).toHaveCount.greaterThan(0)
    
    // 验证当前激活的幻灯片
    const activeSlide = page.locator('.preview-slide.active')
    await expect(activeSlide).toBeVisible()
    
    // 验证导航按钮
    const prevBtn = page.locator('.nav-prev')
    const nextBtn = page.locator('.nav-next')
    await expect(prevBtn).toBeVisible()
    await expect(nextBtn).toBeVisible()
    
    // 验证缩略图条
    const thumbnailStrip = page.locator('.thumbnail-strip')
    await expect(thumbnailStrip).toBeVisible()
    
    const thumbnails = page.locator('.thumbnail')
    await expect(thumbnails).toHaveCount.greaterThan(0)
    
    // 验证激活的缩略图
    const activeThumbnail = page.locator('.thumbnail.active')
    await expect(activeThumbnail).toBeVisible()
  })

  test('项目进度时间线验证', async ({ page }) => {
    // 验证进度区域
    const progressSection = page.locator('.progress-section')
    await expect(progressSection).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.progress-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('🚀 开发进度')
    
    // 验证时间线
    const progressTimeline = page.locator('.progress-timeline')
    await expect(progressTimeline).toBeVisible()
    
    // 验证里程碑
    const milestones = page.locator('.milestone')
    await expect(milestones).toHaveCount.greaterThan(0)
    
    // 验证第一个里程碑的结构
    const firstMilestone = milestones.nth(0)
    await expect(firstMilestone.locator('.milestone-icon')).toBeVisible()
    await expect(firstMilestone.locator('.milestone-title')).toBeVisible()
    await expect(firstMilestone.locator('.milestone-desc')).toBeVisible()
    await expect(firstMilestone.locator('.milestone-date')).toBeVisible()
    
    // 验证进度线
    const progressLines = page.locator('.progress-line')
    await expect(progressLines).toHaveCount.greaterThan(0)
  })

  test('团队介绍区域验证', async ({ page }) => {
    // 验证团队区域
    const teamSection = page.locator('.team-section')
    await expect(teamSection).toBeVisible()
    
    // 验证标题
    const sectionTitle = page.locator('.team-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toContainText('👨‍💻 核心团队')
    
    // 验证团队网格
    const teamGrid = page.locator('.team-grid')
    await expect(teamGrid).toBeVisible()
    
    // 验证团队成员卡片
    const teamMembers = page.locator('.team-member')
    await expect(teamMembers).toHaveCount.greaterThan(0)
    
    // 验证第一个成员的结构
    const firstMember = teamMembers.nth(0)
    await expect(firstMember.locator('.member-avatar')).toBeVisible()
    await expect(firstMember.locator('.member-name')).toBeVisible()
    await expect(firstMember.locator('.member-role')).toBeVisible()
    await expect(firstMember.locator('.member-story')).toBeVisible()
  })

  test('加入我们区域验证', async ({ page }) => {
    // 验证加入区域
    const joinSection = page.locator('.join-section')
    await expect(joinSection).toBeVisible()
    
    // 验证标题
    const joinTitle = page.locator('.join-title')
    await expect(joinTitle).toBeVisible()
    await expect(joinTitle).toContainText('加入我们的冒险')
    
    // 验证副标题
    const joinSubtitle = page.locator('.join-subtitle')
    await expect(joinSubtitle).toBeVisible()
    await expect(joinSubtitle).toContainText('我们正在寻找志同道合的伙伴')
    
    // 验证招募角色
    const wantedRoles = page.locator('.wanted-roles')
    await expect(wantedRoles).toBeVisible()
    
    const wantedRoleCards = page.locator('.wanted-role')
    await expect(wantedRoleCards).toHaveCount.greaterThan(0)
    
    // 验证第一个角色的结构
    const firstRole = wantedRoleCards.nth(0)
    await expect(firstRole.locator('.role-icon')).toBeVisible()
    await expect(firstRole.locator('.role-title')).toBeVisible()
    await expect(firstRole.locator('.role-desc')).toBeVisible()
    
    // 验证CTA按钮
    const ctaButtons = page.locator('.cta-buttons')
    await expect(ctaButtons).toBeVisible()
    
    const ctaPrimary = page.locator('.cta-primary')
    await expect(ctaPrimary).toBeVisible()
    await expect(ctaPrimary).toContainText('立即申请加入团队')
  })

  test('幻灯片导航功能验证', async ({ page }) => {
    // 等待幻灯片加载
    await page.waitForSelector('.preview-slide.active', { timeout: 5000 })
    
    // 获取当前激活的幻灯片索引
    const initialActiveSlide = page.locator('.preview-slide.active')
    const initialSlideIndex = await initialActiveSlide.getAttribute('data-index') || '0'
    
    // 点击下一个按钮
    const nextBtn = page.locator('.nav-next')
    await nextBtn.click()
    await page.waitForTimeout(600) // 等待动画完成
    
    // 验证幻灯片是否切换
    const newActiveSlide = page.locator('.preview-slide.active')
    await expect(newActiveSlide).toBeVisible()
    
    // 点击上一个按钮
    const prevBtn = page.locator('.nav-prev')
    await prevBtn.click()
    await page.waitForTimeout(600)
    
    // 验证幻灯片是否切换回来
    const backActiveSlide = page.locator('.preview-slide.active')
    await expect(backActiveSlide).toBeVisible()
  })

  test('角色详情弹窗验证', async ({ page }) => {
    // 点击第一个招募角色卡片
    const firstWantedRole = page.locator('.wanted-role').nth(0)
    await firstWantedRole.click()
    
    // 验证弹窗是否显示
    const roleModal = page.locator('.role-modal')
    await expect(roleModal).toBeVisible()
    
    // 验证弹窗内容
    const modalContent = page.locator('.role-modal-content')
    await expect(modalContent).toBeVisible()
    
    // 验证弹窗标题
    const modalTitle = page.locator('.modal-title')
    await expect(modalTitle).toBeVisible()
    
    // 验证关闭按钮
    const closeBtn = page.locator('.close-modal')
    await expect(closeBtn).toBeVisible()
    
    // 验证工作信息网格
    const jobInfoGrid = page.locator('.job-info-grid')
    await expect(jobInfoGrid).toBeVisible()
    
    const infoCards = jobInfoGrid.locator('.info-card')
    await expect(infoCards).toHaveCount.greaterThan(0)
    
    // 关闭弹窗
    await closeBtn.click()
    await page.waitForTimeout(300)
    
    // 验证弹窗是否关闭
    await expect(roleModal).not.toBeVisible()
  })

  test('响应式设计验证', async ({ page }) => {
    // 桌面端验证
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
    
    const heroContainer = page.locator('.hero-container')
    await expect(heroContainer).toBeVisible()
    
    // 平板端验证
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.waitForTimeout(500)
    
    await expect(heroContainer).toBeVisible()
    
    // 手机端验证
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(500)
    
    await expect(heroContainer).toBeVisible()
    
    // 恢复桌面端
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(500)
  })

  test('页面截图对比 - 完整页面', async ({ page }) => {
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    await page.waitForTimeout(2000)
    
    // 滚动到顶部
    await page.evaluate(() => window.scrollTo(0, 0))
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('project-detail-full.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 英雄区域', async ({ page }) => {
    // 等待英雄区域加载
    await page.waitForSelector('.hero-section', { timeout: 5000 })
    await page.waitForTimeout(1000)
    
    // 英雄区域截图
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toHaveScreenshot('project-detail-hero.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 团队区域', async ({ page }) => {
    // 滚动到团队区域
    await page.locator('.team-section').scrollIntoViewIfNeeded()
    await page.waitForTimeout(1000)
    
    // 团队区域截图
    const teamSection = page.locator('.team-section')
    await expect(teamSection).toHaveScreenshot('project-detail-team.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 角色弹窗', async ({ page }) => {
    // 点击第一个角色卡片
    const firstWantedRole = page.locator('.wanted-role').nth(0)
    await firstWantedRole.click()
    await page.waitForTimeout(500)
    
    // 角色弹窗截图
    const roleModal = page.locator('.role-modal')
    await expect(roleModal).toHaveScreenshot('project-detail-role-modal.png', {
      animations: 'disabled'
    })
  })
}) 