import { test, expect } from '@playwright/test'

/**
 * 首页UI还原测试
 * 验证首页实现与原型设计的100%一致性
 */
test.describe('首页UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问首页
    await page.goto('/')
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    // 动画已在测试配置中禁用，无需额外处理
  })

  test('导航栏UI还原 - 精确对比', async ({ page }) => {
    const navbar = page.locator('.navbar')
    
    // 验证导航栏存在
    await expect(navbar).toBeVisible()
    
    // 验证导航栏样式
    await expect(navbar).toHaveCSS('position', 'fixed')
    await expect(navbar).toHaveCSS('backdrop-filter', 'blur(20px)')
    
    // 验证Logo样式
    const logo = page.locator('.logo')
    await expect(logo).toHaveText('小概率')
    await expect(logo).toHaveCSS('font-size', '24px')
    await expect(logo).toHaveCSS('font-weight', '700')
    
    // 验证导航菜单
    const navLinks = page.locator('.nav-menu a')
    await expect(navLinks).toHaveCount(5)
    
    // 验证导航链接文本
    const expectedNavTexts = ['首页', '项目', '学院', '投资', '治理']
    for (let i = 0; i < expectedNavTexts.length; i++) {
      await expect(navLinks.nth(i)).toHaveText(expectedNavTexts[i])
    }
    
    // 验证按钮样式 - 使用更精确的选择器
    const loginBtn = page.locator('.nav-actions .btn-ghost')
    await expect(loginBtn).toHaveText('登录')
    await expect(loginBtn).toHaveCSS('border-radius', '6px')
    
    const primaryBtn = page.locator('.nav-actions .btn-primary')
    await expect(primaryBtn).toBeVisible()
    await expect(primaryBtn).toHaveText('发布项目')
    
    // 截图对比 - 导航栏
    await expect(navbar).toHaveScreenshot('navbar-desktop.png')
  })

  test('英雄区块UI还原 - 完整验证', async ({ page }) => {
    const heroSection = page.locator('.hero-section')
    
    // 验证英雄区块存在
    await expect(heroSection).toBeVisible()
    
    // 验证背景渐变
    await expect(heroSection).toHaveCSS('background-image', /linear-gradient/)
    
    // 验证英雄标题
    const heroTitle = page.locator('.hero-title')
    await expect(heroTitle).toBeVisible()
    await expect(heroTitle).toHaveCSS('font-size', '48px')
    await expect(heroTitle).toHaveCSS('font-weight', '700')
    
    // 验证英雄副标题
    const heroSubtitle = page.locator('.hero-subtitle')
    await expect(heroSubtitle).toBeVisible()
    await expect(heroSubtitle).toHaveCSS('font-size', '18px')
    
    // 验证统计数据
    const heroStats = page.locator('.hero-stats .hero-stat')
    await expect(heroStats).toHaveCount(3)
    
    // 验证每个统计项
    const expectedStats = [
      { number: '328', label: '活跃项目' },
      { number: '156', label: '成功案例' },
      { number: '28', label: '获得投资' }
    ]
    
    for (let i = 0; i < expectedStats.length; i++) {
      const stat = heroStats.nth(i)
      const number = stat.locator('.hero-stat-number')
      const label = stat.locator('.hero-stat-label')
      
      await expect(number).toHaveText(expectedStats[i].number)
      await expect(label).toHaveText(expectedStats[i].label)
      await expect(number).toHaveCSS('font-size', '24px')
      await expect(number).toHaveCSS('font-weight', '700')
    }
    
    // 验证CTA按钮
    const ctaButtons = page.locator('.hero-buttons .hero-btn')
    await expect(ctaButtons).toHaveCount(2)
    
    const primaryCtaBtn = ctaButtons.first()
    const secondaryCtaBtn = ctaButtons.last()
    
    await expect(primaryCtaBtn).toHaveText('探索项目')
    await expect(secondaryCtaBtn).toHaveText('发布想法')
    
    // 截图对比 - 英雄区块
    await expect(heroSection).toHaveScreenshot('hero-section-desktop.png')
  })

  test('项目生命周期可视化UI还原', async ({ page }) => {
    const cycleSection = page.locator('.project-cycle')
    
    // 验证生命周期区块存在
    await expect(cycleSection).toBeVisible()
    
    // 验证周期标题
    const cycleTitle = page.locator('.cycle-title')
    await expect(cycleTitle).toHaveText('项目生命周期')
    
    // 验证周期步骤
    const cycleSteps = page.locator('.cycle-step')
    await expect(cycleSteps).toHaveCount(4)
    
    // 验证每个步骤的内容
    const expectedSteps = [
      { icon: '💡', title: '想法阶段', desc: '需求分析·团队匹配' },
      { icon: '🛠️', title: '开发阶段', desc: '技术指导·进度管理' },
      { icon: '🚀', title: '上线阶段', desc: '产品优化·市场推广' },
      { icon: '💰', title: '成功阶段', desc: '投资对接·规模扩张' }
    ]
    
    for (let i = 0; i < expectedSteps.length; i++) {
      const step = cycleSteps.nth(i)
      const icon = step.locator('.cycle-step-icon')
      const title = step.locator('.cycle-step-title')
      const desc = step.locator('.cycle-step-desc')
      
      await expect(icon).toHaveText(expectedSteps[i].icon)
      await expect(title).toHaveText(expectedSteps[i].title)
      await expect(desc).toHaveText(expectedSteps[i].desc)
    }
    
    // 截图对比 - 生命周期区块
    await expect(cycleSection).toHaveScreenshot('lifecycle-section-desktop.png')
  })

  test('功能区块UI还原验证', async ({ page }) => {
    // 滚动到热门项目区块
    await page.locator('.hot-projects-section').scrollIntoViewIfNeeded()
    
    const hotProjectsSection = page.locator('.hot-projects-section')
    await expect(hotProjectsSection).toBeVisible()
    
    // 验证区块标题
    const sectionTitle = page.locator('.hot-projects-section .section-title')
    await expect(sectionTitle).toBeVisible()
    await expect(sectionTitle).toHaveText('热门项目')
    
    // 验证项目卡片
    const projectCards = page.locator('.project-card')
    await expect(projectCards).toHaveCount(2)
    
    // 验证第一个项目卡片内容
    const firstCard = projectCards.first()
    await expect(firstCard.locator('.project-title')).toHaveText('开源博客系统')
    await expect(firstCard.locator('.project-category')).toHaveText('全栈项目')
    
    // 验证项目标签
    const projectTags = firstCard.locator('.project-tag')
    await expect(projectTags).toHaveCount(4)
    
    // 截图对比 - 热门项目区块
    await expect(hotProjectsSection).toHaveScreenshot('hot-projects-section-desktop.png')
  })

  test('成功案例区块UI还原', async ({ page }) => {
    // 滚动到成功案例区块
    await page.locator('.success-section').scrollIntoViewIfNeeded()
    
    const successSection = page.locator('.success-section')
    await expect(successSection).toBeVisible()
    
    // 验证成功案例卡片
    const successCards = page.locator('.success-card')
    await expect(successCards).toHaveCount(2)
    
    // 验证每个案例卡片的结构
    for (let i = 0; i < 2; i++) {
      const card = successCards.nth(i)
      const image = card.locator('.success-image img')
      const title = card.locator('.success-title')
      const description = card.locator('.success-description')
      const metrics = card.locator('.success-metrics .metric')
      
      await expect(image).toBeVisible()
      await expect(title).toBeVisible()
      await expect(description).toBeVisible()
      await expect(metrics).toHaveCount(3)
    }
    
    // 截图对比 - 成功案例区块
    await expect(successSection).toHaveScreenshot('success-section-desktop.png')
  })

  test('CTA区块和页脚UI还原', async ({ page }) => {
    // 滚动到CTA区块
    await page.locator('.cta-section').scrollIntoViewIfNeeded()
    
    const ctaSection = page.locator('.cta-section')
    await expect(ctaSection).toBeVisible()
    
    // 验证CTA样式
    await expect(ctaSection).toHaveCSS('background-color', /rgb\(17, 24, 39\)/)
    await expect(ctaSection).toHaveCSS('color', /rgb\(255, 255, 255\)/)
    
    // 验证CTA标题和副标题
    const ctaTitle = page.locator('.cta-title')
    await expect(ctaTitle).toHaveText('准备开始你的项目之旅？')
    
    const ctaDescription = page.locator('.cta-description')
    await expect(ctaDescription).toBeVisible()
    
    // 验证CTA按钮
    const ctaButtons = page.locator('.cta-actions .btn')
    await expect(ctaButtons).toHaveCount(2)
    
    const primaryBtn = ctaButtons.first()
    const secondaryBtn = ctaButtons.last()
    
    await expect(primaryBtn).toHaveText('立即开始')
    await expect(secondaryBtn).toHaveText('浏览项目')
    
    // 验证页脚
    const footer = page.locator('.footer')
    await expect(footer).toBeVisible()
    
    const footerLogo = page.locator('.footer-logo')
    await expect(footerLogo).toHaveText('小概率')
    
    // 截图对比 - CTA和页脚区块
    const ctaAndFooter = page.locator('.cta-section, .footer')
    await expect(ctaAndFooter.first()).toHaveScreenshot('cta-section-desktop.png')
  })

  test('整页UI还原 - 完整截图对比', async ({ page }) => {
    // 等待所有图片加载
    await page.waitForFunction(() => {
      const images = Array.from(document.images)
      return images.every(img => img.complete)
    })
    
    // 截图对比 - 整个首页
    await expect(page).toHaveScreenshot('homepage-full-desktop.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })

  test('移动端UI还原验证', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    await page.reload()
    await page.waitForLoadState('networkidle')
    
    // 验证移动端导航
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    
    // 验证移动端英雄区块
    const heroSection = page.locator('.hero-section')
    await expect(heroSection).toBeVisible()
    
    // 截图对比 - 移动端整页
    await expect(page).toHaveScreenshot('homepage-mobile.png', {
      fullPage: true,
      animations: 'disabled'
    })
  })
}) 