import { test, expect } from '@playwright/test'

/**
 * 项目列表页面UI还原测试
 * 验证Projects.vue页面与原型projects-list.html的100%一致性
 */

test.describe('项目列表页面UI还原测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects')
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    // 等待Vue应用挂载完成
    await page.waitForSelector('.projects-page', { timeout: 10000 })
    // 等待项目数据加载
    await page.waitForTimeout(1000)
  })

  test('页面整体结构和导航栏', async ({ page }) => {
    // 验证页面根元素
    const projectsPage = page.locator('.projects-page')
    await expect(projectsPage).toBeVisible()
    
    // 验证导航栏存在
    const navbar = page.locator('.navbar')
    await expect(navbar).toBeVisible()
    
    // 验证主内容区域
    const mainContent = page.locator('.main-content')
    await expect(mainContent).toBeVisible()
  })

  test('智能筛选建议区域验证', async ({ page }) => {
    const filterSuggestions = page.locator('.filter-suggestions')
    await expect(filterSuggestions).toBeVisible()
    
    // 验证智能推荐标题
    const suggestionTitle = page.locator('.suggestion-title')
    await expect(suggestionTitle).toBeVisible()
    await expect(suggestionTitle).toContainText('🤖')
    await expect(suggestionTitle).toContainText('根据您的技能推荐')
    
    // 验证推荐标签
    const suggestionTags = page.locator('.suggestion-tags')
    await expect(suggestionTags).toBeVisible()
    
    const tags = page.locator('.suggestion-tag')
    await expect(tags).toHaveCount(4)
    
    // 验证具体标签内容
    const expectedTags = ['Vue.js', 'TypeScript', '前端工程师', '远程']
    for (let i = 0; i < expectedTags.length; i++) {
      await expect(tags.nth(i)).toHaveText(expectedTags[i])
    }
    
    // 验证筛选建议的样式
    await expect(filterSuggestions).toHaveCSS('border-radius', '12px')
    await expect(filterSuggestions).toHaveCSS('padding', '16px')
  })

  test('精选项目Banner区域验证', async ({ page }) => {
    const featuredBanner = page.locator('.featured-banner')
    await expect(featuredBanner).toBeVisible()
    
    // 验证Banner标题
    const bannerTitle = page.locator('.banner-title')
    await expect(bannerTitle).toBeVisible()
    await expect(bannerTitle).toContainText('⭐')
    await expect(bannerTitle).toContainText('精选项目')
    
    // 验证"每日更新"标识
    const bannerBadge = page.locator('.banner-badge')
    await expect(bannerBadge).toHaveText('每日更新')
    
    // 验证导航按钮
    const navButtons = page.locator('.banner-nav-btn')
    await expect(navButtons).toHaveCount(2)
    await expect(navButtons.nth(0)).toHaveText('‹')
    await expect(navButtons.nth(1)).toHaveText('›')
    
    // 验证精选项目卡片
    const featuredProjects = page.locator('.featured-project-card')
    await expect(featuredProjects).toHaveCount(3)
    
    // 验证第一个精选项目的内容
    const firstProject = featuredProjects.first()
    await expect(firstProject.locator('.featured-project-title')).toBeVisible()
    await expect(firstProject.locator('.featured-project-summary')).toBeVisible()
    await expect(firstProject.locator('.featured-project-tags')).toBeVisible()
  })

  test('区域切换标签验证', async ({ page }) => {
    const sectionTabs = page.locator('.section-tabs')
    await expect(sectionTabs).toBeVisible()
    
    // 验证标签数量
    const tabs = page.locator('.section-tab')
    await expect(tabs).toHaveCount(2)
    
    // 验证免费区标签
    const freeTab = tabs.nth(0)
    await expect(freeTab).toHaveText('💚 免费区 (盈利分红)')
    
    // 验证付费区标签
    const paidTab = tabs.nth(1)
    await expect(paidTab).toHaveText('💰 付费区 (薪资保障)')
    
    // 验证默认激活状态
    await expect(freeTab).toHaveClass(/active/)
    
    // 测试标签切换功能
    await paidTab.click()
    await page.waitForTimeout(300)
    await expect(paidTab).toHaveClass(/active/)
    await expect(freeTab).not.toHaveClass(/active/)
    
    // 切换回免费区
    await freeTab.click()
    await page.waitForTimeout(300)
    await expect(freeTab).toHaveClass(/active/)
  })

  test('项目网格和卡片结构验证', async ({ page }) => {
    const projectsGrid = page.locator('.projects-grid')
    await expect(projectsGrid).toBeVisible()
    
    // 验证项目卡片数量
    const projectCards = page.locator('.project-card')
    await expect(projectCards).toHaveCount(6)
    
    // 验证第一个项目卡片的结构
    const firstCard = projectCards.first()
    await expect(firstCard).toBeVisible()
    
    // 验证卡片头部
    const projectHeader = firstCard.locator('.project-header')
    await expect(projectHeader).toBeVisible()
    await expect(projectHeader.locator('.project-title')).toBeVisible()
    await expect(projectHeader.locator('.project-summary')).toBeVisible()
    
    // 验证项目核心信息
    const coreInfo = firstCard.locator('.project-core-info')
    await expect(coreInfo).toBeVisible()
    
    const infoItems = coreInfo.locator('.info-item')
    await expect(infoItems).toHaveCount(4)
    
    // 验证信息项标签
    const expectedLabels = ['工作性质', '工作时间', '工作地点', '项目阶段']
    for (let i = 0; i < expectedLabels.length; i++) {
      await expect(infoItems.nth(i).locator('.info-label')).toHaveText(expectedLabels[i])
      await expect(infoItems.nth(i).locator('.info-value')).toBeVisible()
    }
  })

  test('项目卡片招募岗位验证', async ({ page }) => {
    const firstCard = page.locator('.project-card').first()
    
    // 验证招募岗位区域
    const projectPositions = firstCard.locator('.project-positions')
    await expect(projectPositions).toBeVisible()
    
    const positionsTitle = projectPositions.locator('.positions-title')
    await expect(positionsTitle).toHaveText('招募岗位')
    
    // 验证岗位标签
    const positionTags = projectPositions.locator('.position-tag')
    await expect(positionTags).toHaveCount(3)
    
    // 验证岗位标签样式
    for (let i = 0; i < await positionTags.count(); i++) {
      const tag = positionTags.nth(i)
      await expect(tag).toBeVisible()
      await expect(tag).toHaveText(/\w+/)
    }
  })

  test('项目卡片技能要求验证', async ({ page }) => {
    const firstCard = page.locator('.project-card').first()
    
    // 验证技能要求区域
    const projectSkills = firstCard.locator('.project-skills')
    await expect(projectSkills).toBeVisible()
    
    const skillsTitle = projectSkills.locator('.skills-title')
    await expect(skillsTitle).toHaveText('技能要求')
    
    // 验证技能标签
    const skillTags = projectSkills.locator('.skill-tag')
    await expect(skillTags).toHaveCount(4)
    
    // 验证匹配的技能标签高亮
    const matchedSkills = skillTags.filter({ hasText: /React|TypeScript/ })
    for (let i = 0; i < await matchedSkills.count(); i++) {
      await expect(matchedSkills.nth(i)).toHaveClass(/match/)
    }
  })

  test('时间线指示器验证', async ({ page }) => {
    const firstCard = page.locator('.project-card').first()
    
    // 验证时间线指示器
    const timelineIndicator = firstCard.locator('.timeline-indicator')
    await expect(timelineIndicator).toBeVisible()
    
    // 验证时间线进度
    const timelineProgress = timelineIndicator.locator('.timeline-progress')
    await expect(timelineProgress).toBeVisible()
    
    // 验证时间线点
    const timelineDots = timelineProgress.locator('.timeline-dot')
    await expect(timelineDots).toHaveCount(3)
    
    // 验证时间线连接线
    const timelineConnectors = timelineProgress.locator('.timeline-connector')
    await expect(timelineConnectors).toHaveCount(2)
    
    // 验证时间线文本
    const timelineText = timelineIndicator.locator('.timeline-text')
    await expect(timelineText).toBeVisible()
    
    const timelinePhase = timelineIndicator.locator('.timeline-phase')
    await expect(timelinePhase).toBeVisible()
    
    // 验证时间线指示器样式
    await expect(timelineIndicator).toHaveCSS('border-radius', '12px')
    await expect(timelineIndicator).toHaveCSS('padding', '12px 16px')
  })

  test('项目卡片底部区域验证', async ({ page }) => {
    const firstCard = page.locator('.project-card').first()
    
    // 验证项目底部
    const projectFooter = firstCard.locator('.project-footer')
    await expect(projectFooter).toBeVisible()
    
    // 验证项目元数据
    const projectMeta = projectFooter.locator('.project-meta')
    await expect(projectMeta).toBeVisible()
    
    const metaItems = projectMeta.locator('.meta-item')
    await expect(metaItems).toHaveCount(2)
    
    // 验证发布日期
    const dateItem = metaItems.nth(0)
    await expect(dateItem.locator('.meta-icon')).toHaveText('📅')
    await expect(dateItem.locator('.meta-text')).toContainText('天前')
    
    // 验证申请人数
    const applicantsItem = metaItems.nth(1)
    await expect(applicantsItem.locator('.meta-icon')).toHaveText('👥')
    await expect(applicantsItem.locator('.meta-text')).toContainText('人申请')
    
    // 验证申请按钮
    const applyBtn = projectFooter.locator('.apply-btn')
    await expect(applyBtn).toBeVisible()
    await expect(applyBtn).toHaveText('立即申请')
  })

  test('AI推荐标识验证', async ({ page }) => {
    // 查找带有AI推荐标识的项目卡片
    const aiRecommendedCards = page.locator('.project-card').filter({ has: page.locator('.ai-recommend') })
    
    if (await aiRecommendedCards.count() > 0) {
      const aiRecommend = aiRecommendedCards.first().locator('.ai-recommend')
      await expect(aiRecommend).toBeVisible()
      await expect(aiRecommend).toHaveText('AI')
      
      // 验证AI推荐标识样式
      await expect(aiRecommend).toHaveCSS('position', 'absolute')
      await expect(aiRecommend).toHaveCSS('border-radius', '50%')
      await expect(aiRecommend).toHaveCSS('color', 'rgb(255, 255, 255)')
    }
  })

  test('薪资类型标识验证', async ({ page }) => {
    const projectCards = page.locator('.project-card')
    
    // 验证第一个卡片的薪资类型标识
    const firstCard = projectCards.first()
    const salaryBadge = firstCard.locator('.salary-type-badge')
    await expect(salaryBadge).toBeVisible()
    await expect(salaryBadge).toHaveText(/免费区|付费区/)
    
    // 验证不同类型的项目卡片
    const freeZoneCards = projectCards.filter({ hasText: '免费区' })
    const paidZoneCards = projectCards.filter({ hasText: '付费区' })
    
    const totalCards = await freeZoneCards.count() + await paidZoneCards.count()
    expect(totalCards).toBeGreaterThan(0)
  })

  test('加载更多功能验证', async ({ page }) => {
    // 滚动到页面底部
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    
    // 验证加载更多按钮
    const loadMore = page.locator('.load-more')
    await expect(loadMore).toBeVisible()
    
    const loadMoreBtn = page.locator('.load-more-btn')
    await expect(loadMoreBtn).toBeVisible()
    await expect(loadMoreBtn).toHaveText('加载更多项目')
    
    // 测试点击加载更多
    await loadMoreBtn.click()
    
    // 验证加载中状态
    const loadingSpinner = page.locator('.loading-spinner')
    await expect(loadingSpinner).toBeVisible()
    await expect(loadingSpinner).toHaveText('加载中...')
  })

  test('响应式设计验证', async ({ page }) => {
    // 测试桌面端
    await page.setViewportSize({ width: 1200, height: 800 })
    const projectsGrid = page.locator('.projects-grid')
    await expect(projectsGrid).toBeVisible()
    
    // 测试平板端
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(projectsGrid).toBeVisible()
    
    // 测试手机端
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(projectsGrid).toBeVisible()
    
    // 恢复默认视口
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('筛选建议标签交互验证', async ({ page }) => {
    const suggestionTags = page.locator('.suggestion-tag')
    
    // 测试点击筛选建议标签
    const firstTag = suggestionTags.first()
    await firstTag.hover()
    
    // 验证悬停效果
    await expect(firstTag).toHaveCSS('cursor', 'pointer')
    
    // 点击标签
    await firstTag.click()
    await page.waitForTimeout(200)
    
    // 验证点击后的状态（这里可能需要根据实际功能调整）
    await expect(firstTag).toBeVisible()
  })

  test('项目卡片悬停效果验证', async ({ page }) => {
    const projectCards = page.locator('.project-card')
    const firstCard = projectCards.first()
    
    // 悬停在项目卡片上
    await firstCard.hover()
    await page.waitForTimeout(200)
    
    // 验证悬停效果
    await expect(firstCard).toBeVisible()
    
    // 验证申请按钮在悬停时的状态
    const applyBtn = firstCard.locator('.apply-btn')
    await applyBtn.hover()
    await expect(applyBtn).toBeVisible()
  })

  test('页面截图对比 - 免费区', async ({ page }) => {
    // 确保在免费区标签
    const freeTab = page.locator('.section-tab').nth(0)
    await freeTab.click()
    await page.waitForTimeout(500)
    
    // 全页面截图
    await expect(page).toHaveScreenshot('projects-page-free-zone.png', {
      fullPage: true,
      animations: 'disabled'
    })
    
    // 筛选建议区域截图
    const filterSuggestions = page.locator('.filter-suggestions')
    await expect(filterSuggestions).toHaveScreenshot('projects-filter-suggestions.png', {
      animations: 'disabled'
    })
    
    // 精选项目Banner截图
    const featuredBanner = page.locator('.featured-banner')
    await expect(featuredBanner).toHaveScreenshot('projects-featured-banner.png', {
      animations: 'disabled'
    })
    
    // 项目网格截图
    const projectsGrid = page.locator('.projects-grid')
    await expect(projectsGrid).toHaveScreenshot('projects-grid-free-zone.png', {
      animations: 'disabled'
    })
  })

  test('页面截图对比 - 付费区', async ({ page }) => {
    // 切换到付费区
    const paidTab = page.locator('.section-tab').nth(1)
    await paidTab.click()
    await page.waitForTimeout(500)
    
    // 付费区页面截图
    await expect(page).toHaveScreenshot('projects-page-paid-zone.png', {
      fullPage: true,
      animations: 'disabled'
    })
    
    // 项目网格截图
    const projectsGrid = page.locator('.projects-grid')
    await expect(projectsGrid).toHaveScreenshot('projects-grid-paid-zone.png', {
      animations: 'disabled'
    })
  })

  test('项目卡片细节截图对比', async ({ page }) => {
    const projectCards = page.locator('.project-card')
    
    // 第一个项目卡片截图
    const firstCard = projectCards.first()
    await expect(firstCard).toHaveScreenshot('project-card-first.png', {
      animations: 'disabled'
    })
    
    // 如果有AI推荐的卡片，单独截图
    const aiRecommendedCard = projectCards.filter({ has: page.locator('.ai-recommend') }).first()
    if (await aiRecommendedCard.count() > 0) {
      await expect(aiRecommendedCard).toHaveScreenshot('project-card-ai-recommended.png', {
        animations: 'disabled'
      })
    }
  })
}) 