{"config": {"configFile": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/ui-test.config.ts", "rootDir": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "ui-restoration-report", "open": "on-failure"}], ["json", {"outputFile": "ui-restoration-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Desktop Chrome", "name": "Desktop Chrome", "testDir": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.2", "workers": 4, "webServer": null}, "suites": [{"title": "academy.ui.spec.ts", "file": "academy.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "技能学院页面UI还原测试", "file": "academy.ui.spec.ts", "line": 3, "column": 6, "specs": [{"title": "页面整体结构和导航栏", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 1303, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:39.455Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-18b2462eb006a60927d7", "file": "academy.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 1290, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:39.550Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-英雄区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-英雄区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-13466272f6dad2727352", "file": "academy.ui.spec.ts", "line": 31, "column": 3}, {"title": "英雄统计数据验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 1560, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:39.477Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-英雄统计数据验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-英雄统计数据验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-1b54a6ca95a9d7ca40ea", "file": "academy.ui.spec.ts", "line": 66, "column": 3}, {"title": "技能树可视化验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 1334, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:39.224Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-技能树可视化验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-技能树可视化验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-48209cc8660cb0f7977c", "file": "academy.ui.spec.ts", "line": 91, "column": 3}, {"title": "学习路径区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "failed", "duration": 1056, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:44.215Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-学习路径区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-学习路径区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-032ec9442038c7d1328b", "file": "academy.ui.spec.ts", "line": 118, "column": 3}, {"title": "学习路径卡片详细验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 999, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:44.110Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-学习路径卡片详细验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-学习路径卡片详细验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-1c3194ab69bc7b087185", "file": "academy.ui.spec.ts", "line": 151, "column": 3}, {"title": "实战项目区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 6, "parallelIndex": 3, "status": "failed", "duration": 787, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:45.881Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-实战项目区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-实战项目区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-f0e4c894a3c805abc99d", "file": "academy.ui.spec.ts", "line": 175, "column": 3}, {"title": "项目卡片详细验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "failed", "duration": 876, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:45.790Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目卡片详细验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目卡片详细验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-f2015d76624d58010d05", "file": "academy.ui.spec.ts", "line": 210, "column": 3}, {"title": "项目标签切换功能验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 656, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:49.304Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目标签切换功能验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目标签切换功能验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-8583007a8ff039b34fd4", "file": "academy.ui.spec.ts", "line": 252, "column": 3}, {"title": "导师团队区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 9, "parallelIndex": 2, "status": "failed", "duration": 598, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:49.816Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-导师团队区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-导师团队区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-c1dd5a1b5be48a05fef4", "file": "academy.ui.spec.ts", "line": 272, "column": 3}, {"title": "导师卡片详细验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "failed", "duration": 709, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:51.097Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-导师卡片详细验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-导师卡片详细验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-29bedd10d341f7b6250e", "file": "academy.ui.spec.ts", "line": 304, "column": 3}, {"title": "CTA区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "failed", "duration": 719, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:51.370Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-CTA区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-CTA区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-8f7349f55f72ae4939e9", "file": "academy.ui.spec.ts", "line": 320, "column": 3}, {"title": "路径卡片悬停效果验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "failed", "duration": 568, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:53.235Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-路径卡片悬停效果验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-路径卡片悬停效果验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-789ec1b0be09d6fbac53", "file": "academy.ui.spec.ts", "line": 347, "column": 3}, {"title": "项目卡片悬停效果验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "failed", "duration": 1057, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:54.859Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目卡片悬停效果验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-项目卡片悬停效果验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-469f0dc70eebc3a473ac", "file": "academy.ui.spec.ts", "line": 363, "column": 3}, {"title": "响应式设计验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 14, "parallelIndex": 1, "status": "failed", "duration": 758, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:55.212Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-响应式设计验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-响应式设计验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-65b8538a707328badf1e", "file": "academy.ui.spec.ts", "line": 379, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 15, "parallelIndex": 0, "status": "failed", "duration": 656, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:51:57.624Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---完整页面-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---完整页面-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-ecfb4d9c8735ac681be7", "file": "academy.ui.spec.ts", "line": 404, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 16, "parallelIndex": 3, "status": "failed", "duration": 928, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:00.038Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---英雄区域-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---英雄区域-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-b9c432f29f606c6502a2", "file": "academy.ui.spec.ts", "line": 420, "column": 3}, {"title": "页面截图对比 - 学习路径区域", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 17, "parallelIndex": 2, "status": "failed", "duration": 942, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:00.172Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---学习路径区域-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---学习路径区域-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-ab579f043fa8e85cb73b", "file": "academy.ui.spec.ts", "line": 432, "column": 3}, {"title": "页面截图对比 - 实战项目区域", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "failed", "duration": 611, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:01.812Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---实战项目区域-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---实战项目区域-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-84aa6c736d0458fdec74", "file": "academy.ui.spec.ts", "line": 444, "column": 3}, {"title": "页面截图对比 - 导师团队区域", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 19, "parallelIndex": 1, "status": "failed", "duration": 583, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/academy\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/academy\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'技能学院页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/academy'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.academy-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:03.480Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---导师团队区域-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/academy.ui-技能学院页面UI还原测试-页面截图对比---导师团队区域-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/academy.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c8f584cc3fb3d3a40ff7-7eeea0d36e54f36dd97c", "file": "academy.ui.spec.ts", "line": 456, "column": 3}, {"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-7c5ec97293152d3d8d78", "file": "academy.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-1ac8b98af7f377c48f56", "file": "academy.ui.spec.ts", "line": 31, "column": 3}, {"title": "英雄统计数据验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-dd034c13810bea79de81", "file": "academy.ui.spec.ts", "line": 66, "column": 3}, {"title": "技能树可视化验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-d436a4fe23ec7ee40b43", "file": "academy.ui.spec.ts", "line": 91, "column": 3}, {"title": "学习路径区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-da4e8012cac44b4ff477", "file": "academy.ui.spec.ts", "line": 118, "column": 3}, {"title": "学习路径卡片详细验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-1277ecc6f3fa3afbdbef", "file": "academy.ui.spec.ts", "line": 151, "column": 3}, {"title": "实战项目区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-d3300e3bd8938b712e6d", "file": "academy.ui.spec.ts", "line": 175, "column": 3}, {"title": "项目卡片详细验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-206f37467d26a3edc467", "file": "academy.ui.spec.ts", "line": 210, "column": 3}, {"title": "项目标签切换功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-4d4df47c26e949e28f53", "file": "academy.ui.spec.ts", "line": 252, "column": 3}, {"title": "导师团队区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-963754065773bf0c6ba7", "file": "academy.ui.spec.ts", "line": 272, "column": 3}, {"title": "导师卡片详细验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-52ce95ae20e4c2ad3eaf", "file": "academy.ui.spec.ts", "line": 304, "column": 3}, {"title": "CTA区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-2a89c4eed0dc4ccc47e8", "file": "academy.ui.spec.ts", "line": 320, "column": 3}, {"title": "路径卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-f255a9ad19b075791f71", "file": "academy.ui.spec.ts", "line": 347, "column": 3}, {"title": "项目卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-cb5979cca6d8c429fae1", "file": "academy.ui.spec.ts", "line": 363, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-5a341d64c9c2b6173288", "file": "academy.ui.spec.ts", "line": 379, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-73a7d546b6cfd41677c4", "file": "academy.ui.spec.ts", "line": 404, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-9ad2f2b3a0960ee659d8", "file": "academy.ui.spec.ts", "line": 420, "column": 3}, {"title": "页面截图对比 - 学习路径区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-8e93f94a4ba151718a2c", "file": "academy.ui.spec.ts", "line": 432, "column": 3}, {"title": "页面截图对比 - 实战项目区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-a6c6bf5ac06d694fbb6e", "file": "academy.ui.spec.ts", "line": 444, "column": 3}, {"title": "页面截图对比 - 导师团队区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c8f584cc3fb3d3a40ff7-0f228e69061b4fa92fb4", "file": "academy.ui.spec.ts", "line": 456, "column": 3}]}]}, {"title": "advice.ui.spec.ts", "file": "advice.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "咨询建议页面UI还原测试", "file": "advice.ui.spec.ts", "line": 3, "column": 6, "specs": [{"title": "页面整体结构和导航栏", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 20, "parallelIndex": 2, "status": "failed", "duration": 666, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:04.873Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-ebba5afee58f3703f3de", "file": "advice.ui.spec.ts", "line": 13, "column": 3}, {"title": "DAO治理中心主体验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 21, "parallelIndex": 0, "status": "failed", "duration": 816, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:06.140Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-DAO治理中心主体验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-DAO治理中心主体验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-2ee25ba92885059587f2", "file": "advice.ui.spec.ts", "line": 30, "column": 3}, {"title": "统计数据行验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 22, "parallelIndex": 1, "status": "failed", "duration": 671, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:06.602Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-统计数据行验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-统计数据行验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-0a483a57ece1b6713c53", "file": "advice.ui.spec.ts", "line": 50, "column": 3}, {"title": "过滤和排序区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 23, "parallelIndex": 3, "status": "failed", "duration": 647, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:08.256Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-过滤和排序区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-过滤和排序区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-eb0feaa7681352403f44", "file": "advice.ui.spec.ts", "line": 80, "column": 3}, {"title": "治理提案列表验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 24, "parallelIndex": 2, "status": "failed", "duration": 663, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:10.055Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-治理提案列表验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-治理提案列表验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-92224cc90791fc4aae59", "file": "advice.ui.spec.ts", "line": 109, "column": 3}, {"title": "提案卡片进度条验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "failed", "duration": 653, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:11.177Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案卡片进度条验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案卡片进度条验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-4dcafd3e56b3ef2156cc", "file": "advice.ui.spec.ts", "line": 143, "column": 3}, {"title": "提案卡片底部验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 26, "parallelIndex": 0, "status": "failed", "duration": 745, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:12.995Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案卡片底部验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案卡片底部验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-56c3d74e60bee18558cc", "file": "advice.ui.spec.ts", "line": 166, "column": 3}, {"title": "建议提交面板验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 27, "parallelIndex": 1, "status": "failed", "duration": 677, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:13.217Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-建议提交面板验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-建议提交面板验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-d08f330735acbe884ddb", "file": "advice.ui.spec.ts", "line": 195, "column": 3}, {"title": "提案表单验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "failed", "duration": 766, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:15.355Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案表单验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案表单验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-800101fd1859327e00b5", "file": "advice.ui.spec.ts", "line": 218, "column": 3}, {"title": "影响评估字段验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 29, "parallelIndex": 3, "status": "failed", "duration": 692, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:15.569Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-影响评估字段验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-影响评估字段验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-a952d7545e56061e8df1", "file": "advice.ui.spec.ts", "line": 252, "column": 3}, {"title": "分类指南验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 30, "parallelIndex": 0, "status": "failed", "duration": 656, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:17.461Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-分类指南验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-分类指南验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-d2a1addf7461738f199d", "file": "advice.ui.spec.ts", "line": 276, "column": 3}, {"title": "过滤选项交互验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 31, "parallelIndex": 1, "status": "failed", "duration": 586, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:18.442Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-过滤选项交互验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-过滤选项交互验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-08cda771f055d5e42148", "file": "advice.ui.spec.ts", "line": 287, "column": 3}, {"title": "排序功能验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 32, "parallelIndex": 2, "status": "failed", "duration": 644, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:19.410Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-排序功能验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-排序功能验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-d2515b04ad3345810b63", "file": "advice.ui.spec.ts", "line": 307, "column": 3}, {"title": "表单输入验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 33, "parallelIndex": 3, "status": "failed", "duration": 596, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:20.861Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-表单输入验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-表单输入验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-09027012a3b3ff23bb46", "file": "advice.ui.spec.ts", "line": 325, "column": 3}, {"title": "影响评估选择验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 34, "parallelIndex": 0, "status": "failed", "duration": 603, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:22.075Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-影响评估选择验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-影响评估选择验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-53f590266f253e16d264", "file": "advice.ui.spec.ts", "line": 346, "column": 3}, {"title": "提案参与按钮验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 35, "parallelIndex": 2, "status": "failed", "duration": 674, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:22.732Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案参与按钮验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-提案参与按钮验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-822589ec6ac90542c886", "file": "advice.ui.spec.ts", "line": 362, "column": 3}, {"title": "响应式设计验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 36, "parallelIndex": 1, "status": "failed", "duration": 617, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:24.319Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-响应式设计验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-响应式设计验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-d6fad54c1dfb1fb14f37", "file": "advice.ui.spec.ts", "line": 372, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 37, "parallelIndex": 3, "status": "failed", "duration": 857, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:25.663Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---完整页面-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---完整页面-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-6ad45678e990e82dd79e", "file": "advice.ui.spec.ts", "line": 397, "column": 3}, {"title": "页面截图对比 - DAO治理中心头部", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 38, "parallelIndex": 2, "status": "failed", "duration": 791, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:26.223Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---DAO治理中心头部-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---DAO治理中心头部-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-a5b83c92f26efdea4587", "file": "advice.ui.spec.ts", "line": 413, "column": 3}, {"title": "页面截图对比 - 提案列表", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 39, "parallelIndex": 1, "status": "failed", "duration": 645, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:27.886Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---提案列表-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---提案列表-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-3d259253677c0b0c891b", "file": "advice.ui.spec.ts", "line": 425, "column": 3}, {"title": "页面截图对比 - 建议提交面板", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 40, "parallelIndex": 0, "status": "failed", "duration": 786, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:29.846Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---建议提交面板-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---建议提交面板-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-f556e886d736b3c75ebf", "file": "advice.ui.spec.ts", "line": 437, "column": 3}, {"title": "页面截图对比 - 提案卡片详情", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 41, "parallelIndex": 3, "status": "failed", "duration": 640, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/advice\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/advice\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'咨询建议页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/advice'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.advice-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:30.187Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---提案卡片详情-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/advice.ui-咨询建议页面UI还原测试-页面截图对比---提案卡片详情-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/advice.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "c6b5c00975318d3a3965-1dc30d0bfefb7f7e0abc", "file": "advice.ui.spec.ts", "line": 449, "column": 3}, {"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-e7bb012460f8991a62bb", "file": "advice.ui.spec.ts", "line": 13, "column": 3}, {"title": "DAO治理中心主体验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-7955047aa08368bd78d6", "file": "advice.ui.spec.ts", "line": 30, "column": 3}, {"title": "统计数据行验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-7cfb543eec233b06014d", "file": "advice.ui.spec.ts", "line": 50, "column": 3}, {"title": "过滤和排序区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-71537c73a7818408c438", "file": "advice.ui.spec.ts", "line": 80, "column": 3}, {"title": "治理提案列表验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-f9c8e8793df4cfc060d0", "file": "advice.ui.spec.ts", "line": 109, "column": 3}, {"title": "提案卡片进度条验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-f922928f84dd9f1c90fa", "file": "advice.ui.spec.ts", "line": 143, "column": 3}, {"title": "提案卡片底部验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-dbb752cdd1f80ab86f3d", "file": "advice.ui.spec.ts", "line": 166, "column": 3}, {"title": "建议提交面板验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-260228bdd714b5f66c0d", "file": "advice.ui.spec.ts", "line": 195, "column": 3}, {"title": "提案表单验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-6c46ff5d62db3ebbcca3", "file": "advice.ui.spec.ts", "line": 218, "column": 3}, {"title": "影响评估字段验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-bf089c4f8f2170095912", "file": "advice.ui.spec.ts", "line": 252, "column": 3}, {"title": "分类指南验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-85210b66765f9e00557b", "file": "advice.ui.spec.ts", "line": 276, "column": 3}, {"title": "过滤选项交互验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-3ea0237656bbb131b8c3", "file": "advice.ui.spec.ts", "line": 287, "column": 3}, {"title": "排序功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-f367bdd49799bba2e95a", "file": "advice.ui.spec.ts", "line": 307, "column": 3}, {"title": "表单输入验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-7cb4faa0e4f66274ebd8", "file": "advice.ui.spec.ts", "line": 325, "column": 3}, {"title": "影响评估选择验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-74228efa196876c5429c", "file": "advice.ui.spec.ts", "line": 346, "column": 3}, {"title": "提案参与按钮验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-4ff0c7ebba7e58f0d8ab", "file": "advice.ui.spec.ts", "line": 362, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-7b4013c2d60557163f73", "file": "advice.ui.spec.ts", "line": 372, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-dca3a1ee831077e4a3a0", "file": "advice.ui.spec.ts", "line": 397, "column": 3}, {"title": "页面截图对比 - DAO治理中心头部", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-8cd618faaea92228461b", "file": "advice.ui.spec.ts", "line": 413, "column": 3}, {"title": "页面截图对比 - 提案列表", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-5631025b0fcbfc059971", "file": "advice.ui.spec.ts", "line": 425, "column": 3}, {"title": "页面截图对比 - 建议提交面板", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-321b9d477af3429e4951", "file": "advice.ui.spec.ts", "line": 437, "column": 3}, {"title": "页面截图对比 - 提案卡片详情", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c6b5c00975318d3a3965-331c56983c22d471bf61", "file": "advice.ui.spec.ts", "line": 449, "column": 3}]}]}, {"title": "auth.ui.spec.ts", "file": "auth.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "认证页面UI还原测试", "file": "auth.ui.spec.ts", "line": 8, "column": 6, "specs": [{"title": "页面整体布局和背景装饰", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 42, "parallelIndex": 2, "status": "failed", "duration": 551, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:32.273Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面整体布局和背景装饰-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面整体布局和背景装饰-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-a0a7d7e2bfacfa3131cb", "file": "auth.ui.spec.ts", "line": 20, "column": 3}, {"title": "主容器布局验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 43, "parallelIndex": 0, "status": "failed", "duration": 568, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:33.157Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-主容器布局验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-主容器布局验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-f1e829af46518838e4c6", "file": "auth.ui.spec.ts", "line": 42, "column": 3}, {"title": "左侧品牌区域完整验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 44, "parallelIndex": 1, "status": "failed", "duration": 580, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:34.412Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-左侧品牌区域完整验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-左侧品牌区域完整验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-a2b57dfb2b1469033ba8", "file": "auth.ui.spec.ts", "line": 59, "column": 3}, {"title": "故事章节内容验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 45, "parallelIndex": 2, "status": "failed", "duration": 594, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:35.820Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-故事章节内容验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-故事章节内容验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-c1b1b040f1050b9f6bb3", "file": "auth.ui.spec.ts", "line": 82, "column": 3}, {"title": "创意元素区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 46, "parallelIndex": 3, "status": "failed", "duration": 727, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:37.265Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-创意元素区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-创意元素区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-d0ec4169d7e1db70d184", "file": "auth.ui.spec.ts", "line": 110, "column": 3}, {"title": "激励引言区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 47, "parallelIndex": 0, "status": "failed", "duration": 808, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:37.003Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-激励引言区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-激励引言区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-8e9b37ec47cf80a732ce", "file": "auth.ui.spec.ts", "line": 140, "column": 3}, {"title": "右侧表单区域布局验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 48, "parallelIndex": 1, "status": "failed", "duration": 860, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:38.792Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-右侧表单区域布局验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-右侧表单区域布局验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-2c870af77f9a421ccd04", "file": "auth.ui.spec.ts", "line": 153, "column": 3}, {"title": "登录表单字段验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 49, "parallelIndex": 2, "status": "failed", "duration": 644, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:40.828Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-登录表单字段验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-登录表单字段验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-b2650897c75b6804871c", "file": "auth.ui.spec.ts", "line": 170, "column": 3}, {"title": "表单选项和按钮验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 50, "parallelIndex": 0, "status": "failed", "duration": 784, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:41.731Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-表单选项和按钮验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-表单选项和按钮验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-91a1f0c7ec6dba37c888", "file": "auth.ui.spec.ts", "line": 194, "column": 3}, {"title": "登录/注册切换功能验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 51, "parallelIndex": 1, "status": "failed", "duration": 985, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:44.145Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-登录-注册切换功能验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-登录-注册切换功能验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-efca908006e560f526da", "file": "auth.ui.spec.ts", "line": 215, "column": 3}, {"title": "社交登录区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 52, "parallelIndex": 3, "status": "failed", "duration": 1014, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:44.137Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-社交登录区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-社交登录区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-ce5e0478be12e5fd9078", "file": "auth.ui.spec.ts", "line": 267, "column": 3}, {"title": "表单样式和交互验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 53, "parallelIndex": 2, "status": "failed", "duration": 733, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:45.959Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-表单样式和交互验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-表单样式和交互验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-a50e90882b728e3cb631", "file": "auth.ui.spec.ts", "line": 292, "column": 3}, {"title": "响应式设计验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 54, "parallelIndex": 1, "status": "failed", "duration": 619, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:47.794Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-响应式设计验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-响应式设计验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-e5c87104bd5022951cad", "file": "auth.ui.spec.ts", "line": 317, "column": 3}, {"title": "页面截图对比 - 登录状态", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 55, "parallelIndex": 3, "status": "failed", "duration": 734, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:48.827Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面截图对比---登录状态-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面截图对比---登录状态-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-f7e57d191d0a8987005d", "file": "auth.ui.spec.ts", "line": 335, "column": 3}, {"title": "页面截图对比 - 注册状态", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 56, "parallelIndex": 0, "status": "failed", "duration": 689, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "snippet": "\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/auth\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/auth\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  9 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 10 |\u001b[39m     \u001b[90m// 访问认证页面\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/auth'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 14 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts:11:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:49.290Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面截图对比---注册状态-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/auth.ui-认证页面UI还原测试-页面截图对比---注册状态-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/auth.ui.spec.ts", "column": 16, "line": 11}}], "status": "unexpected"}], "id": "6d6d9ab81445391c32a9-d72288999412657de872", "file": "auth.ui.spec.ts", "line": 358, "column": 3}, {"title": "页面整体布局和背景装饰", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-1235dc82bec90417b7a1", "file": "auth.ui.spec.ts", "line": 20, "column": 3}, {"title": "主容器布局验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-f162d2563c9730af029d", "file": "auth.ui.spec.ts", "line": 42, "column": 3}, {"title": "左侧品牌区域完整验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-22ab77318325162ee8ff", "file": "auth.ui.spec.ts", "line": 59, "column": 3}, {"title": "故事章节内容验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-84900c2dd626cb6b1b93", "file": "auth.ui.spec.ts", "line": 82, "column": 3}, {"title": "创意元素区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-44005d2ab199f8a237ac", "file": "auth.ui.spec.ts", "line": 110, "column": 3}, {"title": "激励引言区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-a59fcb99408e831fbcbe", "file": "auth.ui.spec.ts", "line": 140, "column": 3}, {"title": "右侧表单区域布局验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-74d44256a8a5e3717b9c", "file": "auth.ui.spec.ts", "line": 153, "column": 3}, {"title": "登录表单字段验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-e366fdb399a72f2bb7db", "file": "auth.ui.spec.ts", "line": 170, "column": 3}, {"title": "表单选项和按钮验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-f1b71d9ee596064ab4fc", "file": "auth.ui.spec.ts", "line": 194, "column": 3}, {"title": "登录/注册切换功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-21766005ad1e7ce7ffa4", "file": "auth.ui.spec.ts", "line": 215, "column": 3}, {"title": "社交登录区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-0d2ae69c27728dd3e269", "file": "auth.ui.spec.ts", "line": 267, "column": 3}, {"title": "表单样式和交互验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-f40175821fda08b4cf61", "file": "auth.ui.spec.ts", "line": 292, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-35fecc2de891a728a0cf", "file": "auth.ui.spec.ts", "line": 317, "column": 3}, {"title": "页面截图对比 - 登录状态", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-e4609f531573b500cf9d", "file": "auth.ui.spec.ts", "line": 335, "column": 3}, {"title": "页面截图对比 - 注册状态", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "6d6d9ab81445391c32a9-9f1cc39e6b71d8db9502", "file": "auth.ui.spec.ts", "line": 358, "column": 3}]}]}, {"title": "homepage.ui.spec.ts", "file": "homepage.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "首页UI还原测试", "file": "homepage.ui.spec.ts", "line": 7, "column": 6, "specs": [{"title": "导航栏UI还原 - 精确对比", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 57, "parallelIndex": 2, "status": "failed", "duration": 623, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:50.327Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-导航栏UI还原---精确对比-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-导航栏UI还原---精确对比-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-35de577c11bee64e4d99", "file": "homepage.ui.spec.ts", "line": 16, "column": 3}, {"title": "英雄区块UI还原 - 完整验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 58, "parallelIndex": 3, "status": "failed", "duration": 657, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:52.108Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-英雄区块UI还原---完整验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-英雄区块UI还原---完整验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-1ee49b9fc076978ce09b", "file": "homepage.ui.spec.ts", "line": 55, "column": 3}, {"title": "项目生命周期可视化UI还原", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 59, "parallelIndex": 2, "status": "failed", "duration": 760, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:53.879Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-项目生命周期可视化UI还原-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-项目生命周期可视化UI还原-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-d468bedfc3abd038224b", "file": "homepage.ui.spec.ts", "line": 111, "column": 3}, {"title": "功能区块UI还原验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 60, "parallelIndex": 0, "status": "failed", "duration": 896, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:54.923Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-功能区块UI还原验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-功能区块UI还原验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-8cccf2ed15d896c33f35", "file": "homepage.ui.spec.ts", "line": 148, "column": 3}, {"title": "成功案例区块UI还原", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 61, "parallelIndex": 1, "status": "failed", "duration": 831, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:55.210Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-成功案例区块UI还原-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-成功案例区块UI还原-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-f507087c62db6e284952", "file": "homepage.ui.spec.ts", "line": 177, "column": 3}, {"title": "CTA区块和页脚UI还原", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 62, "parallelIndex": 2, "status": "failed", "duration": 1261, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:59.301Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-CTA区块和页脚UI还原-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-CTA区块和页脚UI还原-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-7e30036a41cef924f38b", "file": "homepage.ui.spec.ts", "line": 206, "column": 3}, {"title": "整页UI还原 - 完整截图对比", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 63, "parallelIndex": 0, "status": "failed", "duration": 1222, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:59.302Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-整页UI还原---完整截图对比-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-整页UI还原---完整截图对比-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-0f9bfe0921e32f033021", "file": "homepage.ui.spec.ts", "line": 246, "column": 3}, {"title": "移动端UI还原验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 64, "parallelIndex": 1, "status": "failed", "duration": 1273, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "snippet": "\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  8 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m  9 |\u001b[39m     \u001b[90m// 访问首页\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[90m// 等待页面完全加载\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 13 |\u001b[39m     \u001b[90m// 动画已在测试配置中禁用，无需额外处理\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts:10:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:52:59.406Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-移动端UI还原验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/homepage.ui-首页UI还原测试-移动端UI还原验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/homepage.ui.spec.ts", "column": 16, "line": 10}}], "status": "unexpected"}], "id": "56b370908e98928cda0a-f0bcbeb3ce52131a1118", "file": "homepage.ui.spec.ts", "line": 260, "column": 3}, {"title": "导航栏UI还原 - 精确对比", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-4f5288198b7480c62877", "file": "homepage.ui.spec.ts", "line": 16, "column": 3}, {"title": "英雄区块UI还原 - 完整验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-c8018de1901cea5b4711", "file": "homepage.ui.spec.ts", "line": 55, "column": 3}, {"title": "项目生命周期可视化UI还原", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-94340ea45e08a7399e3f", "file": "homepage.ui.spec.ts", "line": 111, "column": 3}, {"title": "功能区块UI还原验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-4c824b35ebcbc9a91c05", "file": "homepage.ui.spec.ts", "line": 148, "column": 3}, {"title": "成功案例区块UI还原", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-3c935378c523dc63ef06", "file": "homepage.ui.spec.ts", "line": 177, "column": 3}, {"title": "CTA区块和页脚UI还原", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-52a9ab4e147e90160d24", "file": "homepage.ui.spec.ts", "line": 206, "column": 3}, {"title": "整页UI还原 - 完整截图对比", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-451fd46c7705774ec9e2", "file": "homepage.ui.spec.ts", "line": 246, "column": 3}, {"title": "移动端UI还原验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "56b370908e98928cda0a-cc737537edc0744d70a0", "file": "homepage.ui.spec.ts", "line": 260, "column": 3}]}]}, {"title": "investment.ui.spec.ts", "file": "investment.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "投资生态页面UI还原测试", "file": "investment.ui.spec.ts", "line": 3, "column": 6, "specs": [{"title": "页面整体结构和导航栏", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 65, "parallelIndex": 3, "status": "failed", "duration": 684, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'投资生态页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/investment'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.investment-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'投资生态页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/investment'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.investment-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:53:01.593Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/investment.ui-投资生态页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/investment.ui-投资生态页面UI还原测试-页面整体结构和导航栏-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "fb4982cbd02497643ce8-5fcf89de46221dcef26a", "file": "investment.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 66, "parallelIndex": 0, "status": "failed", "duration": 1064, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n\n    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts:5:16", "location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}, "snippet": "\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'投资生态页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/investment'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.investment-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:3001/investment\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3001/investment\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'投资生态页面UI还原测试'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/investment'\u001b[39m)\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\n \u001b[90m 7 |\u001b[39m     \u001b[90m// 等待Vue应用挂载完成\u001b[39m\n \u001b[90m 8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'.investment-page'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[0m\n\u001b[2m    at /Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts:5:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:53:04.541Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/investment.ui-投资生态页面UI还原测试-英雄区域验证-Desktop-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/test-results/investment.ui-投资生态页面UI还原测试-英雄区域验证-Desktop-Chrome/video.webm"}], "errorLocation": {"file": "/Users/<USER>/Downloads/match/frontend/tests/ui-restoration/restoration/investment.ui.spec.ts", "column": 16, "line": 5}}], "status": "unexpected"}], "id": "fb4982cbd02497643ce8-30a6a0ceb369222342e7", "file": "investment.ui.spec.ts", "line": 31, "column": 3}, {"title": "投资统计数据验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 67, "parallelIndex": 2, "status": "interrupted", "duration": 169, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:53:04.962Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "fb4982cbd02497643ce8-95dc5787e94666f88cb3", "file": "investment.ui.spec.ts", "line": 55, "column": 3}, {"title": "双列布局区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [{"workerIndex": 68, "parallelIndex": 1, "status": "interrupted", "duration": 9, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-08T10:53:05.258Z", "annotations": [], "attachments": []}], "status": "skipped"}], "id": "fb4982cbd02497643ce8-ca90fe178815d6695858", "file": "investment.ui.spec.ts", "line": 85, "column": 3}, {"title": "投资人生态卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-7a96b7c898b14009e32d", "file": "investment.ui.spec.ts", "line": 109, "column": 3}, {"title": "创业者生态卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-107695a27fe6fd9c01b4", "file": "investment.ui.spec.ts", "line": 142, "column": 3}, {"title": "独立开发大赛区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-b2ffeb4a3e9291ed8ffa", "file": "investment.ui.spec.ts", "line": 175, "column": 3}, {"title": "大赛奖项网格验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-0448adcdb9e07d705e00", "file": "investment.ui.spec.ts", "line": 207, "column": 3}, {"title": "大赛CTA按钮验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-1c569a890cd90c9018c0", "file": "investment.ui.spec.ts", "line": 235, "column": 3}, {"title": "投资机会CTA区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-26d89d4e0013bd62349a", "file": "investment.ui.spec.ts", "line": 256, "column": 3}, {"title": "权益列表详细验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-f09d8bee811b4cf2307e", "file": "investment.ui.spec.ts", "line": 291, "column": 3}, {"title": "按钮交互功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-fa2500b968cc8638064e", "file": "investment.ui.spec.ts", "line": 333, "column": 3}, {"title": "卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-81addb4a3a2b88afcdbd", "file": "investment.ui.spec.ts", "line": 355, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-fdc95b466564ca238339", "file": "investment.ui.spec.ts", "line": 369, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-1b40c2848a9f8cfe6220", "file": "investment.ui.spec.ts", "line": 394, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-3c285966121e30873163", "file": "investment.ui.spec.ts", "line": 410, "column": 3}, {"title": "页面截图对比 - 双列布局区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-2147135f61d0507208bf", "file": "investment.ui.spec.ts", "line": 422, "column": 3}, {"title": "页面截图对比 - 大赛专区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-2ca618cdef5f70c5f724", "file": "investment.ui.spec.ts", "line": 434, "column": 3}, {"title": "页面截图对比 - 投资机会CTA", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-f0e89f1bbd10fda4aa3f", "file": "investment.ui.spec.ts", "line": 446, "column": 3}, {"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-d35f0a3054faf1d0d21e", "file": "investment.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-fa1a7bea3611b1e43aa3", "file": "investment.ui.spec.ts", "line": 31, "column": 3}, {"title": "投资统计数据验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-2bf02563ce9ff09571ab", "file": "investment.ui.spec.ts", "line": 55, "column": 3}, {"title": "双列布局区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-5fadf1bbff66affbf250", "file": "investment.ui.spec.ts", "line": 85, "column": 3}, {"title": "投资人生态卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-7db155fa229192e7af7d", "file": "investment.ui.spec.ts", "line": 109, "column": 3}, {"title": "创业者生态卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-dff0aaaee35e84db35d3", "file": "investment.ui.spec.ts", "line": 142, "column": 3}, {"title": "独立开发大赛区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-d6dcd724be42180e5fe4", "file": "investment.ui.spec.ts", "line": 175, "column": 3}, {"title": "大赛奖项网格验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-677b266f4613d18ce518", "file": "investment.ui.spec.ts", "line": 207, "column": 3}, {"title": "大赛CTA按钮验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-6de49aa951b1b0f1a11d", "file": "investment.ui.spec.ts", "line": 235, "column": 3}, {"title": "投资机会CTA区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-7c532e1cd132a6e689b1", "file": "investment.ui.spec.ts", "line": 256, "column": 3}, {"title": "权益列表详细验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-acd9684603b8e8757af6", "file": "investment.ui.spec.ts", "line": 291, "column": 3}, {"title": "按钮交互功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-a1b3f7122f3ce0f1ece3", "file": "investment.ui.spec.ts", "line": 333, "column": 3}, {"title": "卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-b98da9b78b8e88feed02", "file": "investment.ui.spec.ts", "line": 355, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-ea6a980c10c5d37c158d", "file": "investment.ui.spec.ts", "line": 369, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-0646c8b55115ad3579f0", "file": "investment.ui.spec.ts", "line": 394, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-22349f22924eadbbe793", "file": "investment.ui.spec.ts", "line": 410, "column": 3}, {"title": "页面截图对比 - 双列布局区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-f21460e24c2cab9530f2", "file": "investment.ui.spec.ts", "line": 422, "column": 3}, {"title": "页面截图对比 - 大赛专区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-15b0caf978729a1e21ce", "file": "investment.ui.spec.ts", "line": 434, "column": 3}, {"title": "页面截图对比 - 投资机会CTA", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "fb4982cbd02497643ce8-184e667f070bd3cf0bf6", "file": "investment.ui.spec.ts", "line": 446, "column": 3}]}]}, {"title": "project-detail.ui.spec.ts", "file": "project-detail.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "项目详情页面UI还原测试", "file": "project-detail.ui.spec.ts", "line": 3, "column": 6, "specs": [{"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-a5670416d4756e81f426", "file": "project-detail.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-8fe912b896307a52ec14", "file": "project-detail.ui.spec.ts", "line": 38, "column": 3}, {"title": "项目信息卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-257f172738d16e346d3f", "file": "project-detail.ui.spec.ts", "line": 75, "column": 3}, {"title": "游戏预览区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-fca6b46d07094f162b15", "file": "project-detail.ui.spec.ts", "line": 106, "column": 3}, {"title": "项目进度时间线验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-a06ff0416991078893cb", "file": "project-detail.ui.spec.ts", "line": 141, "column": 3}, {"title": "团队介绍区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-dbba67e9e5f51fca49c5", "file": "project-detail.ui.spec.ts", "line": 171, "column": 3}, {"title": "加入我们区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-79ed0a41933742913db7", "file": "project-detail.ui.spec.ts", "line": 197, "column": 3}, {"title": "幻灯片导航功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-649f5e5ae61c88d26577", "file": "project-detail.ui.spec.ts", "line": 234, "column": 3}, {"title": "角色详情弹窗验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-e4862ac0b33a43e96bbf", "file": "project-detail.ui.spec.ts", "line": 261, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-52758b461cf602f7f8ca", "file": "project-detail.ui.spec.ts", "line": 297, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-0c4a7fcbba8a127bfd5a", "file": "project-detail.ui.spec.ts", "line": 322, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-02b2554a547f6ebce83f", "file": "project-detail.ui.spec.ts", "line": 338, "column": 3}, {"title": "页面截图对比 - 团队区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-e2cbbb0a0a642f499553", "file": "project-detail.ui.spec.ts", "line": 350, "column": 3}, {"title": "页面截图对比 - 角色弹窗", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-d01356cbef24e69df301", "file": "project-detail.ui.spec.ts", "line": 362, "column": 3}, {"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-fc40b894bf6f7ec8bf7b", "file": "project-detail.ui.spec.ts", "line": 13, "column": 3}, {"title": "英雄区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-e4f26acb7c014568a547", "file": "project-detail.ui.spec.ts", "line": 38, "column": 3}, {"title": "项目信息卡片验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-b942c0c44bcd461e4625", "file": "project-detail.ui.spec.ts", "line": 75, "column": 3}, {"title": "游戏预览区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-b9fbd9b07994c6a36b0a", "file": "project-detail.ui.spec.ts", "line": 106, "column": 3}, {"title": "项目进度时间线验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-2edc9575ca8f09224996", "file": "project-detail.ui.spec.ts", "line": 141, "column": 3}, {"title": "团队介绍区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-1a1dea7b6b5506e38c60", "file": "project-detail.ui.spec.ts", "line": 171, "column": 3}, {"title": "加入我们区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-075d71a81b47207d0634", "file": "project-detail.ui.spec.ts", "line": 197, "column": 3}, {"title": "幻灯片导航功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-d95c5d393a2edb2ff278", "file": "project-detail.ui.spec.ts", "line": 234, "column": 3}, {"title": "角色详情弹窗验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-02080cc249e7b00f813a", "file": "project-detail.ui.spec.ts", "line": 261, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-d6c0efb4ef0538c80531", "file": "project-detail.ui.spec.ts", "line": 297, "column": 3}, {"title": "页面截图对比 - 完整页面", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-921bdcfb789050decde2", "file": "project-detail.ui.spec.ts", "line": 322, "column": 3}, {"title": "页面截图对比 - 英雄区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-4ab0dfab1a8338140248", "file": "project-detail.ui.spec.ts", "line": 338, "column": 3}, {"title": "页面截图对比 - 团队区域", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-22b88c4930ed6af1f2ab", "file": "project-detail.ui.spec.ts", "line": 350, "column": 3}, {"title": "页面截图对比 - 角色弹窗", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "b469bf403f306870a66f-a3da5d83fb0812f83bc7", "file": "project-detail.ui.spec.ts", "line": 362, "column": 3}]}]}, {"title": "projects.ui.spec.ts", "file": "projects.ui.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "项目列表页面UI还原测试", "file": "projects.ui.spec.ts", "line": 8, "column": 6, "specs": [{"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-20ddae8fb3b5a52c72eb", "file": "projects.ui.spec.ts", "line": 20, "column": 3}, {"title": "智能筛选建议区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-8c3622974abb572e631a", "file": "projects.ui.spec.ts", "line": 34, "column": 3}, {"title": "精选项目Banner区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-4d72806600e800a9b02f", "file": "projects.ui.spec.ts", "line": 62, "column": 3}, {"title": "区域切换标签验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-9311b52bff910656f46b", "file": "projects.ui.spec.ts", "line": 93, "column": 3}, {"title": "项目网格和卡片结构验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-a4c96db7382a09234314", "file": "projects.ui.spec.ts", "line": 124, "column": 3}, {"title": "项目卡片招募岗位验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-ced71caed250de19d3eb", "file": "projects.ui.spec.ts", "line": 157, "column": 3}, {"title": "项目卡片技能要求验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-99f196030712bc8adae3", "file": "projects.ui.spec.ts", "line": 179, "column": 3}, {"title": "时间线指示器验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-9f39de5fa7ffe419ea7c", "file": "projects.ui.spec.ts", "line": 200, "column": 3}, {"title": "项目卡片底部区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-d2334fb26b0f0e40b588", "file": "projects.ui.spec.ts", "line": 231, "column": 3}, {"title": "AI推荐标识验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-1d86dc06d1b8117f3a2f", "file": "projects.ui.spec.ts", "line": 261, "column": 3}, {"title": "薪资类型标识验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-aaed25ddcad421899395", "file": "projects.ui.spec.ts", "line": 277, "column": 3}, {"title": "加载更多功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-5e3a3a9ea3daf3696958", "file": "projects.ui.spec.ts", "line": 294, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-4e247eab5d18f059f2bb", "file": "projects.ui.spec.ts", "line": 315, "column": 3}, {"title": "筛选建议标签交互验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-2f89aef49f9c6efcf6c1", "file": "projects.ui.spec.ts", "line": 333, "column": 3}, {"title": "项目卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-3ecbfbc73e2a0786fa75", "file": "projects.ui.spec.ts", "line": 351, "column": 3}, {"title": "页面截图对比 - 免费区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-bbf1b374195d862dba91", "file": "projects.ui.spec.ts", "line": 368, "column": 3}, {"title": "页面截图对比 - 付费区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-ef4d704c4317d9e24763", "file": "projects.ui.spec.ts", "line": 399, "column": 3}, {"title": "项目卡片细节截图对比", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Desktop Chrome", "projectName": "Desktop Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-9cd3543d34e0ef744e66", "file": "projects.ui.spec.ts", "line": 418, "column": 3}, {"title": "页面整体结构和导航栏", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-9e9061a575aca9bd41a5", "file": "projects.ui.spec.ts", "line": 20, "column": 3}, {"title": "智能筛选建议区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-5c788099c49126d105ee", "file": "projects.ui.spec.ts", "line": 34, "column": 3}, {"title": "精选项目Banner区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-a4e28c2e33573ea3eaaf", "file": "projects.ui.spec.ts", "line": 62, "column": 3}, {"title": "区域切换标签验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-493931d1f666c88694dc", "file": "projects.ui.spec.ts", "line": 93, "column": 3}, {"title": "项目网格和卡片结构验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-58e21189de11e797307c", "file": "projects.ui.spec.ts", "line": 124, "column": 3}, {"title": "项目卡片招募岗位验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-4bc64d859d8eb0123af3", "file": "projects.ui.spec.ts", "line": 157, "column": 3}, {"title": "项目卡片技能要求验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-fb60404c747b3dce1851", "file": "projects.ui.spec.ts", "line": 179, "column": 3}, {"title": "时间线指示器验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-faa58ab6b8b982e755ab", "file": "projects.ui.spec.ts", "line": 200, "column": 3}, {"title": "项目卡片底部区域验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-a912c4679ebf0eb25be1", "file": "projects.ui.spec.ts", "line": 231, "column": 3}, {"title": "AI推荐标识验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-8f81c937c8c30ad0c33b", "file": "projects.ui.spec.ts", "line": 261, "column": 3}, {"title": "薪资类型标识验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-907bd2da64addc350de7", "file": "projects.ui.spec.ts", "line": 277, "column": 3}, {"title": "加载更多功能验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-46405a2726b555b2d7f7", "file": "projects.ui.spec.ts", "line": 294, "column": 3}, {"title": "响应式设计验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-fdd02ff76e4e636ea64e", "file": "projects.ui.spec.ts", "line": 315, "column": 3}, {"title": "筛选建议标签交互验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-8db1e3bab152bd7d58d9", "file": "projects.ui.spec.ts", "line": 333, "column": 3}, {"title": "项目卡片悬停效果验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-c5350ebeb3612cd19c5b", "file": "projects.ui.spec.ts", "line": 351, "column": 3}, {"title": "页面截图对比 - 免费区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-af0021162f508c6b77d0", "file": "projects.ui.spec.ts", "line": 368, "column": 3}, {"title": "页面截图对比 - 付费区", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-5668a4eb281da8cb8c78", "file": "projects.ui.spec.ts", "line": 399, "column": 3}, {"title": "项目卡片细节截图对比", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "3a28478daf3e277f9ff5-88df4c29423ad74a8b3a", "file": "projects.ui.spec.ts", "line": 418, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-08T10:51:37.187Z", "duration": 89175.141, "expected": 0, "skipped": 165, "unexpected": 67, "flaky": 0}}