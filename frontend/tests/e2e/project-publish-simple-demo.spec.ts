import { test, expect } from '@playwright/test'

test.describe('项目发布功能演示', () => {
  test.beforeEach(async ({ page }) => {
    // 设置较短的超时时间
    page.setDefaultTimeout(8000)
    
    // 1. 首先访问首页
    await page.goto('/')
    await page.waitForLoadState('domcontentloaded')
    
    // 2. 检查是否需要登录
    try {
      // 直接尝试访问项目发布页面
      await page.goto('/project-publish')
      await page.waitForLoadState('domcontentloaded')
      
      // 如果被重定向到登录页面，进行快速注册/登录
      const currentUrl = page.url()
      if (currentUrl.includes('/auth') || !currentUrl.includes('/project-publish')) {
        console.log('🔑 需要登录，正在创建测试账号...')
        
        // 生成随机测试用户
        const timestamp = Date.now()
        const testUser = {
          username: `testuser${timestamp}`,
          email: `test${timestamp}@example.com`,
          password: 'password123'
        }
        
        // 等待登录页面加载
        await page.waitForSelector('form', { timeout: 5000 })
        
        // 切换到注册模式（如果有的话）
        const registerBtn = page.locator('button:has-text("注册")')
        if (await registerBtn.isVisible()) {
          await registerBtn.click()
        }
        
        // 填写注册表单
        await page.fill('input[type="text"], input[placeholder*="用户名"]', testUser.username)
        await page.fill('input[type="email"], input[placeholder*="邮箱"]', testUser.email)
        await page.fill('input[type="password"]:nth-of-type(1)', testUser.password)
        const confirmPwdInput = page.locator('input[type="password"]:nth-of-type(2)')
        if (await confirmPwdInput.isVisible()) {
          await confirmPwdInput.fill(testUser.password)
        }
        
        // 提交表单
        await page.click('button[type="submit"]')
        await page.waitForTimeout(2000)
        
        // 再次尝试访问项目发布页面
        await page.goto('/project-publish')
        await page.waitForLoadState('domcontentloaded')
      }
      
      // 等待项目发布页面的关键元素
      await page.waitForSelector('.wizard-container', { timeout: 10000 })
      console.log('✅ 成功访问项目发布页面')
      
    } catch (error) {
      console.log('❌ 页面访问失败:', error.message)
      // 如果仍然失败，尝试直接访问，可能认证已绕过
      await page.goto('/project-publish')
      await page.waitForLoadState('domcontentloaded')
      await page.waitForSelector('.wizard-container', { timeout: 5000 })
    }
  })

  test('🎬 项目发布表单验证和按钮状态演示', async ({ page }) => {
    console.log('🚀 开始项目发布功能演示...')
    
    // 验证页面已正确加载
    await expect(page.locator('.wizard-container')).toBeVisible()
    
    // 定位下一步按钮
    const nextButton = page.locator('.form-actions .btn-primary').first()
    
    // 验证页面加载
    console.log('✅ 页面加载完成')
    await expect(page.locator('.progress-title')).toHaveText('项目基本信息', { timeout: 3000 })
    
    // 1. 验证初始状态 - 按钮应该被禁用
    console.log('🔍 验证初始状态 - 按钮禁用')
    await expect(nextButton).toBeDisabled()
    
    // 2. 逐步填写表单，观察按钮状态变化
    console.log('📝 开始填写表单...')
    
    // 填写项目名称
    console.log('  📌 填写项目名称')
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', 'AI智能助手平台')
    await page.waitForTimeout(800)
    await expect(nextButton).toBeDisabled() // 仍然禁用
    
    // 填写项目概括
    console.log('  📌 填写项目概括')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '智能AI助手服务')
    await page.waitForTimeout(800)
    await expect(nextButton).toBeDisabled() // 仍然禁用
    
    // 选择项目分类
    console.log('  📌 选择项目分类: AI/机器学习')
    await page.selectOption('select >> nth=0', 'ai')
    await page.waitForTimeout(800)
    await expect(nextButton).toBeDisabled() // 仍然禁用
    
    // 选择预算范围
    console.log('  📌 选择预算范围: 10万-30万')
    await page.selectOption('select >> nth=1', '100000-300000')
    await page.waitForTimeout(800)
    await expect(nextButton).toBeDisabled() // 仍然禁用，因为描述未填写
    
    // 填写项目描述
    console.log('  📌 填写项目描述（至少50字符）')
    const description = '这是一个基于最新AI技术的智能助手平台，主要功能包括自然语言处理、智能对话、任务自动化、数据分析等。目标用户群体为企业和个人用户，旨在通过AI技术大幅提升工作效率和用户体验，解决日常工作中的重复性任务问题。'
    await page.fill('textarea[placeholder*="详细描述你的项目"]', description)
    
    // 模拟用户点击其他地方触发验证
    await page.locator('textarea[placeholder*="详细描述你的项目"]').blur()
    await page.waitForTimeout(1000)
    
    // 3. 验证所有字段填写完成后，按钮变为可用
    console.log('✨ 所有必填字段完成，按钮应该可用')
    await expect(nextButton).toBeEnabled()
    
    // 4. 点击下一步，进入第二步
    console.log('🎯 点击下一步按钮')
    await nextButton.click()
    await page.waitForTimeout(1500)
    
    // 5. 验证成功进入第二步
    console.log('✅ 验证进入第二步')
    await expect(page.locator('.step.active .step-circle')).toHaveText('2', { timeout: 3000 })
    await expect(page.locator('.progress-title')).toHaveText('团队和需求信息')
    
    console.log('🎉 项目发布第一步演示完成！')
  })

  test('🚨 表单验证错误演示', async ({ page }) => {
    console.log('🚀 开始表单验证错误演示...')
    
    const nextButton = page.locator('.form-actions .btn-primary').first()
    
    // 1. 测试项目名称过短
    console.log('❌ 测试项目名称过短')
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '短')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    await page.waitForTimeout(500)
    
    // 验证错误提示出现
    await expect(page.locator('.error-message')).toBeVisible()
    await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).toHaveClass(/error/)
    
    // 2. 修正项目名称
    console.log('✅ 修正项目名称')
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '正确长度的项目名称')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    await page.waitForTimeout(500)
    
    // 验证错误提示消失
    await expect(page.locator('.error-message')).not.toBeVisible()
    
    // 3. 测试概括过长
    console.log('❌ 测试概括文本过长')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '这是一个非常详细和冗长的项目概括描述文本，明显超过了二十个字符的限制要求')
    await page.locator('input[placeholder="用一句话概括项目的核心价值"]').blur()
    await page.waitForTimeout(500)
    
    // 验证错误提示
    await expect(page.locator('.error-message')).toBeVisible()
    
    // 4. 修正概括
    console.log('✅ 修正概括长度')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '合适长度的概括')
    await page.locator('input[placeholder="用一句话概括项目的核心价值"]').blur()
    await page.waitForTimeout(500)
    
    await expect(page.locator('.error-message')).not.toBeVisible()
    
    console.log('🎉 表单验证演示完成！')
  })

  test('📱 响应式设计演示', async ({ page }) => {
    console.log('🚀 开始响应式设计演示...')
    
    // 验证桌面版布局
    console.log('🖥️  验证桌面版布局')
    await expect(page.locator('.wizard-container')).toBeVisible()
    await expect(page.locator('.progress-steps')).toBeVisible()
    
    // 切换到移动端视图
    console.log('📱 切换到移动端视图')
    await page.setViewportSize({ width: 375, height: 667 })
    await page.waitForTimeout(1000)
    
    // 验证移动端布局
    await expect(page.locator('.wizard-container')).toBeVisible()
    await expect(page.locator('.form-container')).toBeVisible()
    
    // 恢复桌面视图
    console.log('🖥️  恢复桌面视图')
    await page.setViewportSize({ width: 1280, height: 720 })
    await page.waitForTimeout(1000)
    
    console.log('🎉 响应式设计演示完成！')
  })
}) 