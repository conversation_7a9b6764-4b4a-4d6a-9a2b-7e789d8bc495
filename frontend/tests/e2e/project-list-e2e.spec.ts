import { test, expect } from '@playwright/test'

test.describe('项目列表端到端测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects')
  })

  test('应该能够正确加载和显示项目列表', async ({ page }) => {
    // 等待页面加载完成
    await page.waitForLoadState('networkidle')
    
    // 验证页面标题
    await expect(page).toHaveTitle(/项目广场/)
    
    // 验证区域切换标签存在
    await expect(page.locator('.section-tabs')).toBeVisible()
    await expect(page.locator('.section-tab').first()).toContainText('免费区')
    await expect(page.locator('.section-tab').last()).toContainText('付费区')
    
    // 验证默认显示免费区
    await expect(page.locator('.section-tab.active')).toContainText('免费区')
  })

  test('应该能够切换免费区和付费区', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 点击付费区标签
    await page.locator('.section-tab').filter({ hasText: '付费区' }).click()
    
    // 验证付费区标签激活
    await expect(page.locator('.section-tab.active')).toContainText('付费区')
    
    // 点击免费区标签
    await page.locator('.section-tab').filter({ hasText: '免费区' }).click()
    
    // 验证免费区标签激活
    await expect(page.locator('.section-tab.active')).toContainText('免费区')
  })

  test('应该显示项目卡片内容', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待项目卡片加载
    await page.waitForSelector('.project-card', { timeout: 10000 })
    
    // 验证第一个项目卡片的基本信息
    const firstCard = page.locator('.project-card').first()
    
    // 验证项目标题存在
    await expect(firstCard.locator('.project-title')).not.toBeEmpty()
    
    // 验证项目描述存在
    await expect(firstCard.locator('.project-summary')).not.toBeEmpty()
    
    // 验证工作信息存在
    await expect(firstCard.locator('.project-core-info')).toBeVisible()
    
    // 验证申请按钮存在
    await expect(firstCard.locator('.apply-btn')).toBeVisible()
  })

  test('应该能够点击项目卡片跳转到详情页', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待项目卡片加载
    await page.waitForSelector('.project-card', { timeout: 10000 })
    
    // 点击第一个项目卡片（避免点击申请按钮）
    await page.locator('.project-card .project-title').first().click()
    
    // 验证跳转到项目详情页
    await expect(page).toHaveURL(/\/project\/\d+/)
  })

  test('应该能够申请项目', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待项目卡片加载
    await page.waitForSelector('.project-card', { timeout: 10000 })
    
    // 处理可能出现的提示框
    page.on('dialog', async dialog => {
      if (dialog.type() === 'prompt') {
        await dialog.accept('我对这个项目很感兴趣，希望能够加入团队。')
      } else if (dialog.type() === 'alert') {
        await dialog.accept()
      }
    })
    
    // 点击申请按钮
    await page.locator('.apply-btn').first().click()
    
    // 等待一下，确保处理完成
    await page.waitForTimeout(1000)
  })

  test('应该处理加载状态', async ({ page }) => {
    // 阻止网络请求以测试加载状态
    await page.route('**/api/projects*', route => {
      // 延迟响应
      setTimeout(() => route.continue(), 2000)
    })
    
    // 访问页面
    await page.goto('/projects')
    
    // 验证加载状态显示
    await expect(page.locator('.loading-container')).toBeVisible()
    await expect(page.locator('.loading-spinner')).toContainText('正在加载项目')
  })

  test('应该处理错误状态', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/projects*', route => {
      route.abort()
    })
    
    // 访问页面
    await page.goto('/projects')
    
    // 等待错误状态显示
    await expect(page.locator('.error-container')).toBeVisible()
    await expect(page.locator('.error-message')).toContainText('加载项目失败')
    
    // 验证重试按钮存在
    await expect(page.locator('.retry-btn')).toBeVisible()
  })

  test('应该显示精选项目轮播', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待精选项目区域加载
    await page.waitForSelector('.featured-banner', { timeout: 10000 })
    
    // 验证精选项目标题
    await expect(page.locator('.banner-title')).toContainText('精选项目')
    
    // 验证轮播导航按钮
    await expect(page.locator('.banner-nav .banner-nav-btn')).toHaveCount(2)
  })

  test('应该显示智能筛选建议', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 验证智能筛选建议区域
    await expect(page.locator('.filter-suggestions')).toBeVisible()
    await expect(page.locator('.suggestion-title')).toContainText('根据您的技能推荐')
    
    // 验证建议标签
    await expect(page.locator('.suggestion-tags .suggestion-tag')).toHaveCount(4)
  })

  test('应该能够加载更多项目', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 滚动到页面底部
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight))
    
    // 查找加载更多按钮
    const loadMoreBtn = page.locator('.load-more-btn')
    
    if (await loadMoreBtn.isVisible()) {
      // 点击加载更多
      await loadMoreBtn.click()
      
      // 验证加载状态
      await expect(page.locator('.loading-spinner')).toBeVisible()
    }
  })

  test('应该正确显示项目元数据', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待项目卡片加载
    await page.waitForSelector('.project-card', { timeout: 10000 })
    
    const firstCard = page.locator('.project-card').first()
    
    // 验证项目元数据
    await expect(firstCard.locator('.project-meta')).toBeVisible()
    await expect(firstCard.locator('.meta-item')).toHaveCount(2) // 发布日期和申请人数
    
    // 验证发布日期格式
    const dateElement = firstCard.locator('.meta-item').first()
    await expect(dateElement).toContainText(/\d{4}\/\d{1,2}\/\d{1,2}/)
    
    // 验证申请人数格式
    const applicantsElement = firstCard.locator('.meta-item').last()
    await expect(applicantsElement).toContainText(/\d+人申请/)
  })

  test('应该正确显示技能标签匹配', async ({ page }) => {
    // 等待页面加载
    await page.waitForLoadState('networkidle')
    
    // 等待项目卡片加载
    await page.waitForSelector('.project-card', { timeout: 10000 })
    
    const firstCard = page.locator('.project-card').first()
    
    // 验证技能标签区域
    await expect(firstCard.locator('.project-skills')).toBeVisible()
    await expect(firstCard.locator('.skills-title')).toContainText('技能要求')
    
    // 验证至少有一个技能标签
    const skillTags = firstCard.locator('.skill-tag')
    await expect(skillTags.first()).toBeVisible()
  })
}) 