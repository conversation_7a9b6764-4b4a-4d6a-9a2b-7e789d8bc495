import { test, expect, Page } from '@playwright/test'

// 测试数据
const testUser = {
  username: 'e2euser',
  email: '<EMAIL>',
  password: 'password123'
}

const testAdmin = {
  username: 'e2eadmin',
  email: '<EMAIL>',
  password: 'password123'
}

const testProject = {
  title: 'E2E测试项目',
  summary: '这是一个端到端测试项目',
  category: 'web',
  budgetRange: '10000-50000',
  description: '这是一个用于端到端测试的项目描述。需要足够长以满足验证要求。我们将测试完整的项目发布和审核流程。',
  demoUrl: 'https://example.com/demo',
  teamMember: {
    name: '张三',
    role: '项目经理',
    background: '5年项目管理经验',
    introduction: '负责项目整体协调'
  },
  teamInvestment: '团队全职投入，预计工作时间每周40小时',
  recruitmentPosition: '前端开发工程师',
  cooperation: 'salary',
  workType: 'remote',
  workArrangement: 'fullTime',
  workLocation: '北京',
  recentProgress: '项目处于概念验证阶段，已完成基础架构设计',
  userAnalysis: '目标用户为中小企业，主要解决项目管理效率问题',
  projectOrigin: '基于团队在项目管理中遇到的实际痛点',
  competitiveAdvantage: '相比现有产品，我们的解决方案更加轻量级和易用',
  businessModel: 'subscription',
  businessDescription: '采用订阅制收费模式，按月收费，提供不同级别的服务',
  durationMonths: '6'
}

test.describe('项目发布和审核流程', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test.describe('用户注册和登录', () => {
    test('用户应该能够注册和登录', async ({ page }) => {
      // 点击注册按钮
      await page.click('a[href="/register"]')
      await expect(page).toHaveURL('/register')

      // 填写注册表单
      await page.fill('input[name="username"]', testUser.username)
      await page.fill('input[name="email"]', testUser.email)
      await page.fill('input[name="password"]', testUser.password)
      await page.fill('input[name="confirmPassword"]', testUser.password)

      // 提交注册
      await page.click('button[type="submit"]')
      
      // 等待跳转到首页
      await expect(page).toHaveURL('/')
      
      // 验证用户已登录
      await expect(page.locator('text=' + testUser.username)).toBeVisible()
    })

    test('管理员应该能够注册和登录', async ({ page }) => {
      // 注册管理员账户
      await page.click('a[href="/register"]')
      await page.fill('input[name="username"]', testAdmin.username)
      await page.fill('input[name="email"]', testAdmin.email)
      await page.fill('input[name="password"]', testAdmin.password)
      await page.fill('input[name="confirmPassword"]', testAdmin.password)
      await page.click('button[type="submit"]')
      
      // 这里需要手动将用户设为管理员（在实际环境中可能需要后台操作）
      // 为了测试，我们假设有一个特殊的管理员注册路径
    })
  })

  test.describe('项目发布流程', () => {
    test.beforeEach(async ({ page }) => {
      // 先登录用户
      await page.goto('/login')
      await page.fill('input[name="email"]', testUser.email)
      await page.fill('input[name="password"]', testUser.password)
      await page.click('button[type="submit"]')
      await expect(page).toHaveURL('/')
    })

    test('用户应该能够完成项目发布流程', async ({ page }) => {
      // 导航到项目发布页面
      await page.click('a[href="/projects/publish"]')
      await expect(page).toHaveURL('/projects/publish')
      
      // 验证页面标题和步骤指示器
      await expect(page.locator('h2:has-text("项目基本信息")')).toBeVisible()
      await expect(page.locator('.step.active')).toContainText('基本信息')

      // 第一步：填写基本信息
      await fillBasicInfo(page)
      
      // 验证可以进入下一步
      const nextButton = page.locator('button:has-text("下一步")')
      await expect(nextButton).toBeEnabled()
      await nextButton.click()

      // 验证进入第二步
      await expect(page.locator('h3:has-text("团队和需求信息")')).toBeVisible()
      await expect(page.locator('.step.active')).toContainText('团队需求')

      // 第二步：填写团队需求
      await fillTeamRequirements(page)
      
      // 进入下一步
      await page.click('button:has-text("下一步")')

      // 验证进入第三步
      await expect(page.locator('h3:has-text("项目分析")')).toBeVisible()
      await expect(page.locator('.step.active')).toContainText('项目分析')

      // 第三步：填写项目分析
      await fillProjectAnalysis(page)
      
      // 提交项目
      const submitButton = page.locator('button:has-text("提交审核")')
      await expect(submitButton).toBeEnabled()
      
      // Mock 成功提交的 API 响应
      await page.route('**/api/projects', route => {
        route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '项目提交成功，已进入审核流程',
            data: { projectId: 1, status: 'pending_review', reviewStatus: 'pending' }
          })
        })
      })

      await submitButton.click()
      
      // 验证提交成功提示
      await expect(page.locator('text=项目提交成功')).toBeVisible()
      
      // 验证跳转到项目列表
      await expect(page).toHaveURL('/projects')
    })

    test('表单验证应该正常工作', async ({ page }) => {
      await page.goto('/projects/publish')
      
      // 尝试在未填写必填字段时点击下一步
      const nextButton = page.locator('button:has-text("下一步")')
      await expect(nextButton).toBeDisabled()
      
      // 填写项目名称
      await page.fill('input[placeholder*="项目名字"]', testProject.title)
      await expect(nextButton).toBeDisabled() // 仍然应该被禁用
      
      // 填写概括
      await page.fill('input[placeholder*="一句话概括"]', testProject.summary)
      await expect(nextButton).toBeDisabled()
      
      // 选择分类
      await page.selectOption('select', testProject.category)
      await expect(nextButton).toBeDisabled()
      
      // 选择预算
      await page.selectOption('select', testProject.budgetRange)
      await expect(nextButton).toBeDisabled()
      
      // 填写描述（太短）
      await page.fill('textarea[placeholder*="详细描述"]', '太短了')
      await expect(nextButton).toBeDisabled()
      
      // 填写足够长的描述
      await page.fill('textarea[placeholder*="详细描述"]', testProject.description)
      await expect(nextButton).toBeEnabled()
    })

    test('应该能够在步骤间导航', async ({ page }) => {
      await page.goto('/projects/publish')
      
      // 填写第一步
      await fillBasicInfo(page)
      await page.click('button:has-text("下一步")')
      
      // 验证在第二步
      await expect(page.locator('.step.active')).toContainText('团队需求')
      
      // 点击上一步
      await page.click('button:has-text("上一步")')
      
      // 验证回到第一步
      await expect(page.locator('.step.active')).toContainText('基本信息')
      await expect(page.locator('h3:has-text("项目基本信息")')).toBeVisible()
    })

    test('动态表单功能应该正常工作', async ({ page }) => {
      await page.goto('/projects/publish')
      await fillBasicInfo(page)
      await page.click('button:has-text("下一步")')
      
      // 验证初始只有一个团队成员
      const teamMembers = page.locator('.dynamic-item')
      await expect(teamMembers).toHaveCount(1)
      
      // 添加团队成员
      await page.click('button:has-text("添加团队成员")')
      await expect(teamMembers).toHaveCount(2)
      
      // 删除团队成员
      await page.click('.remove-btn:nth-child(1)')
      await expect(teamMembers).toHaveCount(1)
    })
  })

  test.describe('管理员审核流程', () => {
    test.beforeEach(async ({ page }) => {
      // 登录管理员账户
      await page.goto('/login')
      await page.fill('input[name="email"]', testAdmin.email)
      await page.fill('input[name="password"]', testAdmin.password)
      await page.click('button[type="submit"]')
    })

    test('管理员应该能够查看待审核项目', async ({ page }) => {
      // Mock API 响应
      await page.route('**/api/projects/pending', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              projects: [{
                id: 1,
                title: testProject.title,
                summary: testProject.summary,
                category: testProject.category,
                description: testProject.description,
                budgetRange: testProject.budgetRange,
                durationMonths: 6,
                reviewStatus: 'pending',
                submittedAt: new Date().toISOString(),
                creator: {
                  id: 1,
                  username: testUser.username,
                  email: testUser.email,
                  avatarUrl: null
                }
              }]
            }
          })
        })
      })

      // 导航到审核页面
      await page.goto('/admin/projects/review')
      
      // 验证页面标题
      await expect(page.locator('h1:has-text("项目审核管理")')).toBeVisible()
      
      // 验证项目卡片
      await expect(page.locator('.project-card')).toBeVisible()
      await expect(page.locator(`text=${testProject.title}`)).toBeVisible()
      await expect(page.locator('.status-badge:has-text("待审核")')).toBeVisible()
    })

    test('管理员应该能够审核通过项目', async ({ page }) => {
      // Mock 获取待审核项目的 API
      await page.route('**/api/projects/pending', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              projects: [{
                id: 1,
                title: testProject.title,
                summary: testProject.summary,
                reviewStatus: 'pending',
                creator: { username: testUser.username, email: testUser.email }
              }]
            }
          })
        })
      })

      // Mock 审核 API
      await page.route('**/api/projects/1/review', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '项目审核通过并已发布',
            data: { project: { reviewStatus: 'approved', status: 'recruiting' } }
          })
        })
      })

      await page.goto('/admin/projects/review')
      
      // 点击通过按钮
      await page.click('button:has-text("通过")')
      
      // 验证弹窗
      await expect(page.locator('.modal-content')).toBeVisible()
      await expect(page.locator('h3:has-text("审核通过")')).toBeVisible()
      
      // 填写审核意见
      await page.fill('textarea[placeholder*="审核意见"]', '项目质量很好，符合发布要求')
      
      // 确认审核
      await page.click('button:has-text("确认")')
      
      // 验证成功提示
      await expect(page.locator('text=审核通过')).toBeVisible()
    })

    test('管理员应该能够审核拒绝项目', async ({ page }) => {
      // Mock APIs
      await page.route('**/api/projects/pending', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              projects: [{
                id: 1,
                title: testProject.title,
                reviewStatus: 'pending',
                creator: { username: testUser.username }
              }]
            }
          })
        })
      })

      await page.route('**/api/projects/1/review', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '项目已拒绝',
            data: { project: { reviewStatus: 'rejected' } }
          })
        })
      })

      await page.goto('/admin/projects/review')
      
      // 点击拒绝按钮
      await page.click('button:has-text("拒绝")')
      
      // 验证弹窗
      await expect(page.locator('h3:has-text("审核拒绝")')).toBeVisible()
      
      // 拒绝需要填写理由
      const confirmButton = page.locator('button:has-text("确认")')
      await expect(confirmButton).toBeDisabled()
      
      // 填写拒绝理由
      await page.fill('textarea[placeholder*="审核意见"]', '项目内容不符合平台规范')
      await expect(confirmButton).toBeEnabled()
      
      // 确认拒绝
      await confirmButton.click()
      
      // 验证成功提示
      await expect(page.locator('text=已拒绝')).toBeVisible()
    })
  })

  test.describe('完整的端到端流程', () => {
    test('完整的项目发布到审核流程', async ({ page, context }) => {
      // 第一步：用户注册和发布项目
      await page.goto('/register')
      await page.fill('input[name="username"]', testUser.username)
      await page.fill('input[name="email"]', testUser.email)
      await page.fill('input[name="password"]', testUser.password)
      await page.fill('input[name="confirmPassword"]', testUser.password)
      await page.click('button[type="submit"]')
      
      // 发布项目
      await page.goto('/projects/publish')
      await fillCompleteProjectForm(page)
      
      // Mock 项目创建 API
      await page.route('**/api/projects', route => {
        route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { projectId: 1, status: 'pending_review' }
          })
        })
      })
      
      await page.click('button:has-text("提交审核")')
      await expect(page.locator('text=项目提交成功')).toBeVisible()
      
      // 第二步：管理员审核
      // 创建新的页面上下文用于管理员
      const adminPage = await context.newPage()
      
      // 管理员登录
      await adminPage.goto('/login')
      await adminPage.fill('input[name="email"]', testAdmin.email)
      await adminPage.fill('input[name="password"]', testAdmin.password)
      await adminPage.click('button[type="submit"]')
      
      // Mock 管理员 API
      await adminPage.route('**/api/projects/pending', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              projects: [{
                id: 1,
                title: testProject.title,
                reviewStatus: 'pending',
                creator: { username: testUser.username, email: testUser.email }
              }]
            }
          })
        })
      })
      
      await adminPage.route('**/api/projects/1/review', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            message: '项目审核通过',
            data: { project: { reviewStatus: 'approved' } }
          })
        })
      })
      
      // 审核项目
      await adminPage.goto('/admin/projects/review')
      await adminPage.click('button:has-text("通过")')
      await adminPage.fill('textarea[placeholder*="审核意见"]', '审核通过')
      await adminPage.click('button:has-text("确认")')
      
      // 验证审核成功
      await expect(adminPage.locator('text=审核通过')).toBeVisible()
      
      // 第三步：用户查看项目状态
      // Mock 我的项目 API
      await page.route('**/api/projects/my', route => {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: {
              projects: [{
                id: 1,
                title: testProject.title,
                status: 'recruiting',
                reviewStatus: 'approved',
                publishedAt: new Date().toISOString()
              }]
            }
          })
        })
      })
      
      await page.goto('/projects/my')
      await expect(page.locator('text=已通过')).toBeVisible()
      await expect(page.locator('text=招募中')).toBeVisible()
    })
  })

  // 辅助函数
  async function fillBasicInfo(page: Page) {
    await page.fill('input[placeholder*="项目名字"]', testProject.title)
    await page.fill('input[placeholder*="一句话概括"]', testProject.summary)
    await page.selectOption('select', testProject.category)
    await page.selectOption('select', testProject.budgetRange)
    await page.fill('textarea[placeholder*="详细描述"]', testProject.description)
    await page.fill('input[placeholder*="demo.example.com"]', testProject.demoUrl)
  }

  async function fillTeamRequirements(page: Page) {
    await page.fill('input[placeholder="成员姓名"]', testProject.teamMember.name)
    await page.fill('input[placeholder*="产品经理"]', testProject.teamMember.role)
    await page.fill('textarea[placeholder*="专业背景"]', testProject.teamMember.background)
    await page.fill('textarea[placeholder*="个人特点"]', testProject.teamMember.introduction)
    await page.fill('textarea[placeholder*="团队投入"]', testProject.teamInvestment)
    
    await page.fill('input[placeholder*="前端开发工程师"]', testProject.recruitmentPosition)
    await page.selectOption('select', testProject.cooperation)
    
    await page.selectOption('select', testProject.workType)
    await page.selectOption('select', testProject.workArrangement)
    await page.fill('input[placeholder*="工作地址"]', testProject.workLocation)
  }

  async function fillProjectAnalysis(page: Page) {
    await page.fill('textarea[placeholder*="最近的重要进展"]', testProject.recentProgress)
    await page.fill('textarea[placeholder*="目标用户群体"]', testProject.userAnalysis)
    await page.fill('textarea[placeholder*="初始灵感"]', testProject.projectOrigin)
    await page.fill('textarea[placeholder*="独特之处"]', testProject.competitiveAdvantage)
    await page.selectOption('select', testProject.businessModel)
    await page.fill('textarea[placeholder*="商业模式"]', testProject.businessDescription)
    await page.selectOption('select', testProject.durationMonths)
  }

  async function fillCompleteProjectForm(page: Page) {
    await fillBasicInfo(page)
    await page.click('button:has-text("下一步")')
    
    await fillTeamRequirements(page)
    await page.click('button:has-text("下一步")')
    
    await fillProjectAnalysis(page)
  }
}) 