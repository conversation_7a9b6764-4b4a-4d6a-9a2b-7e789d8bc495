import { test, expect } from '@playwright/test'

test.describe('项目发布完整流程测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到项目发布页面
    await page.goto('/project-publish')
    
    // 等待页面加载
    await page.waitForSelector('.wizard-container')
  })

  test('完整的项目发布流程', async ({ page }) => {
    // 步骤1：项目基本信息
    await test.step('填写项目基本信息', async () => {
      // 验证当前在第一步
      await expect(page.locator('.step.active .step-circle')).toHaveText('1')
      await expect(page.locator('.progress-title')).toHaveText('项目基本信息')
      
      // 填写项目名称
      await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '创新AI助手项目')
      
      // 填写一句话概括
      await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '智能AI助手平台')
      
      // 选择项目分类
      await page.selectOption('select:first-of-type', 'ai')
      
      // 选择项目预算
      await page.selectOption('select:nth-of-type(2)', '100000-300000')
      
      // 填写项目描述
      await page.fill('textarea[placeholder*="详细描述你的项目"]', 
        '这是一个基于AI技术的智能助手项目，主要功能包括自然语言处理、智能对话、任务自动化等。目标用户是企业和个人用户，希望通过AI技术提升工作效率和用户体验。')
      
      // 填写Demo链接（可选）
      await page.fill('input[placeholder="https://demo.example.com"]', 'https://ai-assistant-demo.com')
      
      // 验证下一步按钮可用
      await expect(page.locator('.btn-primary')).not.toBeDisabled()
      
      // 点击下一步
      await page.click('.btn-primary')
    })

    // 步骤2：团队和需求信息
    await test.step('填写团队和需求信息', async () => {
      // 验证切换到第二步
      await expect(page.locator('.step.active .step-circle')).toHaveText('2')
      await expect(page.locator('.progress-title')).toHaveText('团队和需求信息')
      
      // 填写默认团队成员信息
      await page.fill('input[placeholder="成员姓名"]:first-of-type', '张三')
      await page.fill('input[placeholder="如：产品经理"]:first-of-type', '项目负责人')
      await page.fill('textarea[placeholder="简要介绍成员的专业背景和经验"]:first-of-type', 
        '5年产品管理经验，曾负责多个AI项目的产品规划和管理。')
      
      // 添加新的团队成员
      await page.click('button:has-text("+ 添加团队成员")')
      
      // 填写第二个团队成员
      await page.fill('input[placeholder="成员姓名"]:nth-of-type(2)', '李四')
      await page.fill('input[placeholder="如：产品经理"]:nth-of-type(2)', '技术负责人')
      
      // 填写团队投入情况
      await page.fill('textarea[placeholder="描述团队的投入程度、工作时间安排、已有进展等"]', 
        '团队成员全职投入，已完成前期调研和技术预研，具备项目启动条件。')
      
      // 填写招募需求
      await page.fill('input[placeholder="如：前端开发工程师"]:first-of-type', '前端开发工程师')
      await page.selectOption('select:has-text("选择合作方式")', 'salary')
      await page.fill('input[placeholder="如：Vue.js, React, TypeScript（用逗号分隔）"]', 'Vue.js, TypeScript, AI技术')
      await page.fill('input[placeholder="如：8000-12000"]', '12000-18000')
      
      // 选择工作地点和安排
      await page.selectOption('select:has-text("选择工作地点")', 'hybrid')
      await page.selectOption('select:has-text("选择工作安排")', 'fullTime')
      
      // 填写具体地址
      await page.fill('input[placeholder="如果不是完全远程，请填写具体工作地址"]', '北京市海淀区中关村科技园')
      
      // 验证下一步按钮可用并点击
      await expect(page.locator('.btn-primary')).not.toBeDisabled()
      await page.click('.btn-primary')
    })

    // 步骤3：项目进展
    await test.step('填写项目进展信息', async () => {
      // 验证切换到第三步
      await expect(page.locator('.step.active .step-circle')).toHaveText('3')
      await expect(page.locator('.progress-title')).toHaveText('项目分析')
      
      // 填写最近进展
      await page.fill('textarea[placeholder="描述项目最近的重要进展，如产品开发、用户验证、市场反馈等"]',
        '已完成核心AI算法开发，正在进行用户界面设计和后端架构搭建。初步用户调研显示需求强烈。')
      
      // 填写用户分析
      await page.fill('textarea[placeholder="详细描述目标用户群体、用户需求和市场痛点"]',
        '目标用户主要是中小企业和知识工作者，需要提升工作效率。现有解决方案复杂度高，用户学习成本大。')
      
      // 填写项目起源
      await page.fill('textarea[placeholder="描述项目的初始灵感、创立背景和要解决的问题"]',
        '团队在工作中发现重复性任务较多，希望通过AI技术简化工作流程，提升整体效率。')
      
      // 填写竞争优势
      await page.fill('textarea[placeholder="相比竞争对手，你的项目有什么独特之处？技术、产品、团队或商业模式上的优势"]',
        '采用最新的大语言模型技术，结合深度学习算法，提供更准确的智能识别和处理能力。')
      
      // 选择盈利模式
      await page.selectOption('select:has-text("选择主要盈利模式")', 'subscription')
      
      // 填写商业模式详述
      await page.fill('textarea[placeholder="详细描述商业模式、盈利方式、定价策略和市场预期"]',
        '采用SaaS订阅模式，提供基础版和专业版。基础版免费，专业版月费99元，预计3年内用户达到10万。')
      
      // 选择项目周期
      await page.selectOption('select:has-text("选择项目周期")', '7-12个月')
      
      // 设置预期开始时间
      const today = new Date()
      const futureDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后
      await page.fill('input[type="date"]', futureDate.toISOString().split('T')[0])
      
      // 选择当前阶段
      await page.selectOption('select:has-text("选择当前阶段")', 'development')
      
      // 选择法律实体
      await page.selectOption('select:has-text("选择法律实体状态")', 'preparing')
      
      // 验证下一步按钮可用并点击
      await expect(page.locator('.btn-primary')).not.toBeDisabled()
      await page.click('.btn-primary')
    })

    // 步骤4：项目故事
    await test.step('填写项目故事', async () => {
      // 验证切换到第四步
      await expect(page.locator('.step.active .step-circle')).toHaveText('4')
      await expect(page.locator('.progress-title')).toHaveText('项目故事')
      
      // 填写项目起源故事
      await page.fill('input[placeholder="第一章标题，如：梦想的起源"]', '梦想的起源')
      await page.fill('textarea[placeholder="详细描述项目的起源故事，初始灵感，要解决的问题..."]:first-of-type',
        '项目源于团队对工作效率提升的思考，我们发现很多重复性工作可以通过AI自动化完成。')
      
      // 填写技术挑战（可选）
      await page.fill('input[placeholder="第二章标题，如：技术的挑战"]', '技术的突破')
      await page.fill('textarea[placeholder="描述在开发过程中遇到的技术挑战和解决方案..."]:nth-of-type(2)',
        '在算法优化和用户体验平衡方面遇到挑战，通过不断迭代找到了最佳解决方案。')
      
      // 验证提交按钮可用
      await expect(page.locator('.btn-primary:has-text("🚀 提交审核")')).not.toBeDisabled()
      
      // 提交项目
      await page.click('.btn-primary:has-text("🚀 提交审核")')
    })

    // 验证提交成功
    await test.step('验证提交成功', async () => {
      // 等待提交完成（这里应该有成功提示或页面跳转）
      // 根据实际实现调整验证逻辑
      await expect(page).toHaveURL('/projects')
    })
  })

  test('表单验证阻止提交测试', async ({ page }) => {
    await test.step('空表单验证', async () => {
      // 验证下一步按钮被禁用
      await expect(page.locator('.btn-primary')).toBeDisabled()
      
      // 验证显示验证提示
      await expect(page.locator('.validation-hint')).toBeVisible()
      await expect(page.locator('.validation-hint')).toContainText('请完善表单信息后继续')
    })

    await test.step('无效数据验证', async () => {
      // 填写太短的项目名称
      await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '短')
      await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
      
      // 验证错误提示
      await expect(page.locator('.error-message')).toContainText('项目名称至少5个字符')
      
      // 验证输入框有错误样式
      await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).toHaveClass(/error/)
      
      // 验证下一步按钮仍被禁用
      await expect(page.locator('.btn-primary')).toBeDisabled()
    })

    await test.step('修正错误后验证', async () => {
      // 修正项目名称
      await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '合适的项目名称')
      await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
      
      // 验证错误消失
      await expect(page.locator('.error-message')).not.toBeVisible()
      
      // 验证输入框恢复正常样式
      await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).not.toHaveClass(/error/)
    })
  })

  test('步骤导航测试', async ({ page }) => {
    // 填写第一步的必填信息
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '测试项目')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '测试概括')
    await page.selectOption('select:first-of-type', 'web')
    await page.selectOption('select:nth-of-type(2)', '10000-50000')
    await page.fill('textarea[placeholder*="详细描述你的项目"]', 
      '这是一个测试项目描述，包含了足够的字符数量来满足最少50个字符的要求，用于测试表单验证功能。')
    
    // 进入第二步
    await page.click('.btn-primary')
    await expect(page.locator('.step.active .step-circle')).toHaveText('2')
    
    // 验证上一步按钮可用
    await expect(page.locator('.btn-secondary')).toBeVisible()
    await expect(page.locator('.btn-secondary')).toContainText('上一步')
    
    // 点击上一步返回
    await page.click('.btn-secondary')
    await expect(page.locator('.step.active .step-circle')).toHaveText('1')
    
    // 验证数据保持不变
    await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).toHaveValue('测试项目')
  })

  test('响应式设计测试', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 验证步骤指示器在移动端的显示
    await expect(page.locator('.progress-steps')).toBeVisible()
    
    // 验证表单在移动端的布局
    await expect(page.locator('.form-grid')).toBeVisible()
    
    // 测试桌面端视图
    await page.setViewportSize({ width: 1200, height: 800 })
    
    // 验证布局调整
    await expect(page.locator('.wizard-container')).toBeVisible()
  })

  test('动态表单项测试', async ({ page }) => {
    // 进入第二步
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '测试项目')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '测试概括')
    await page.selectOption('select:first-of-type', 'web')
    await page.selectOption('select:nth-of-type(2)', '10000-50000')
    await page.fill('textarea[placeholder*="详细描述你的项目"]', 
      '这是一个测试项目描述，包含了足够的字符数量来满足最少50个字符的要求。')
    await page.click('.btn-primary')
    
    // 验证默认有一个团队成员
    await expect(page.locator('.dynamic-item')).toHaveCount(2) // 团队成员 + 招募需求各一个
    
    // 添加新的团队成员
    await page.click('button:has-text("+ 添加团队成员")')
    
    // 验证增加了一个团队成员项
    const teamItems = page.locator('.dynamic-item:has-text("团队成员")')
    await expect(teamItems).toHaveCount(2)
    
    // 删除团队成员
    await page.click('.remove-btn:first-of-type')
    await expect(teamItems).toHaveCount(1)
    
    // 添加招募需求
    await page.click('button:has-text("+ 添加招募岗位")')
    const recruitmentItems = page.locator('.dynamic-item:has-text("招募岗位")')
    await expect(recruitmentItems).toHaveCount(2)
  })

  test('错误处理测试', async ({ page }) => {
    // 模拟网络错误（如果需要）
    // await page.route('**/api/projects', route => route.abort())
    
    // 测试表单验证错误的视觉反馈
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '这是一个超过二十个字符限制的概括测试')
    await page.locator('input[placeholder="用一句话概括项目的核心价值"]').blur()
    
    // 验证错误样式和提示
    await expect(page.locator('.error-message')).toContainText('不能超过20个字符')
    await expect(page.locator('input[placeholder="用一句话概括项目的核心价值"]')).toHaveClass(/error/)
  })
}) 