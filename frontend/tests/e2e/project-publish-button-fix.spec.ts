import { test, expect } from '@playwright/test'

test.describe('项目发布按钮修复验证', () => {
  // 测试用户数据
  const testUser = {
    username: 'testuser' + Date.now(),
    email: 'test' + Date.now() + '@example.com',
    password: 'password123'
  }

  test.beforeEach(async ({ page }) => {
    // 1. 首先访问首页
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    // 2. 检查是否已登录，如果没有则进行注册和登录
    const isLoggedIn = await page.locator('.nav-user').isVisible().catch(() => false)
    
    if (!isLoggedIn) {
      // 注册新用户
      await page.click('a[href="/auth"]')
      await page.waitForLoadState('networkidle')
      
      // 切换到注册模式
      await page.click('button:has-text("注册")')
      
      // 填写注册表单
      await page.fill('input[placeholder="用户名"]', testUser.username)
      await page.fill('input[placeholder="邮箱"]', testUser.email)
      await page.fill('input[placeholder="密码"]', testUser.password)
      await page.fill('input[placeholder="确认密码"]', testUser.password)
      
      // 提交注册
      await page.click('button[type="submit"]')
      await page.waitForTimeout(2000) // 等待注册完成
    }
    
    // 3. 导航到项目发布页面
    await page.goto('/project-publish')
    await page.waitForLoadState('networkidle')
    
    // 4. 等待页面完全加载
    await page.waitForSelector('.wizard-container', { timeout: 10000 })
  })

  test('第一步：验证所有必填字段填写完成后，下一步按钮应该可点击', async ({ page }) => {
    // 使用更具体的选择器，避免匹配导航栏的按钮
    const nextButton = page.locator('.form-actions .btn-primary')
    
    // 验证页面正确加载
    await expect(page.locator('.progress-title')).toHaveText('项目基本信息')
    
    // 检查初始状态 - 下一步按钮应该被禁用
    await expect(nextButton).toBeDisabled()

    // 填写项目名称
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', 'DevMatch项目发布功能测试')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    
    // 检查按钮仍然禁用（因为其他字段未填写）
    await expect(nextButton).toBeDisabled()

    // 填写一句话概括
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '项目发布测试平台')
    await page.locator('input[placeholder="用一句话概括项目的核心价值"]').blur()

    // 选择项目分类
    await page.selectOption('select >> nth=0', { value: 'web' })

    // 选择预算范围
    await page.selectOption('select >> nth=1', { value: '10000-50000' })

    // 检查按钮仍然禁用（描述未填写）
    await expect(nextButton).toBeDisabled()

    // 填写项目描述（至少50字符）
    const description = '这是一个用于验证DevMatch项目发布功能完整性的测试项目。包含表单验证、UI样式、用户交互、数据提交等核心功能。项目采用Vue3+TypeScript技术栈，实现了完整的项目发布流程。'
    await page.fill('textarea[placeholder*="详细描述你的项目"]', description)
    await page.locator('textarea[placeholder*="详细描述你的项目"]').blur()

    // 等待一下让计算属性更新
    await page.waitForTimeout(500)

    // 现在所有必填字段都填写完成，下一步按钮应该可点击
    await expect(nextButton).toBeEnabled()

    // 点击下一步按钮进入第二步
    await nextButton.click()
    await page.waitForTimeout(1000)

    // 验证进入了第二步
    await expect(page.locator('.step.active .step-circle')).toHaveText('2')
  })

  test('验证字段错误状态和错误提示', async ({ page }) => {
    const nextButton = page.locator('.form-actions .btn-primary')
    
    // 填写过短的项目名称
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '短')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    
    // 验证错误提示存在
    await expect(page.locator('.error-message').first()).toBeVisible()
    
    // 验证输入框有错误样式
    await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).toHaveClass(/error/)
    
    // 验证按钮仍被禁用
    await expect(nextButton).toBeDisabled()

    // 修正项目名称
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '正确长度的项目名称')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    await page.waitForTimeout(200)
    
    // 验证错误提示消失
    await expect(page.locator('.error-message')).not.toBeVisible()
    
    // 验证输入框恢复正常样式
    await expect(page.locator('input[placeholder="给你的项目起一个吸引人的名字"]')).not.toHaveClass(/error/)
  })

  test('验证所有字段的验证规则', async ({ page }) => {
    // 测试项目名称验证
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', '短')
    await page.locator('input[placeholder="给你的项目起一个吸引人的名字"]').blur()
    await expect(page.locator('.error-message').first()).toContainText('至少5个字符')
    
    // 测试概括验证
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '这是一个超过二十个字符限制的过长概括文本')
    await page.locator('input[placeholder="用一句话概括项目的核心价值"]').blur()
    await expect(page.locator('.error-message').first()).toContainText('最多20个字符')
    
    // 测试描述验证
    await page.fill('textarea[placeholder*="详细描述你的项目"]', '太短的描述')
    await page.locator('textarea[placeholder*="详细描述你的项目"]').blur()
    await expect(page.locator('.error-message').first()).toContainText('至少50个字符')
  })

  test('填写有效数据后验证按钮状态变化', async ({ page }) => {
    const nextButton = page.locator('.form-actions .btn-primary')
    
    // 逐步填写有效数据，观察按钮状态变化
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', 'DevMatch创新项目测试')
    await page.waitForTimeout(100)
    await expect(nextButton).toBeDisabled()
    
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '创新项目测试平台')
    await page.waitForTimeout(100)
    await expect(nextButton).toBeDisabled()
    
    await page.selectOption('select >> nth=0', { value: 'web' })
    await page.waitForTimeout(100)
    await expect(nextButton).toBeDisabled()
    
    await page.selectOption('select >> nth=1', { value: '50000-100000' })
    await page.waitForTimeout(100)
    await expect(nextButton).toBeDisabled()
    
    // 填写符合要求的描述
    const validDescription = '这是一个完整的DevMatch项目发布功能测试。项目包含了完整的表单验证、UI样式检查、用户交互测试等功能。通过这个测试可以验证修复后的下一步按钮功能是否正常工作，确保用户能够正常进行项目发布流程。'
    await page.fill('textarea[placeholder*="详细描述你的项目"]', validDescription)
    await page.locator('textarea[placeholder*="详细描述你的项目"]').blur()
    
    // 等待验证完成
    await page.waitForTimeout(500)
    
    // 现在按钮应该可用
    await expect(nextButton).toBeEnabled()
  })

  test('完整流程测试：从用户注册到项目发布第一步完成', async ({ page }) => {
    // 验证用户已登录并在项目发布页面
    await expect(page.locator('.progress-title')).toHaveText('项目基本信息')
    
    const nextButton = page.locator('.form-actions .btn-primary')
    await expect(nextButton).toBeDisabled()

    // 按顺序填写所有字段，模拟真实用户操作
    console.log('填写项目名称...')
    await page.fill('input[placeholder="给你的项目起一个吸引人的名字"]', 'DevMatch完整测试项目')
    await page.waitForTimeout(500)
    
    console.log('填写项目概括...')
    await page.fill('input[placeholder="用一句话概括项目的核心价值"]', '完整测试项目')
    await page.waitForTimeout(500)
    
    console.log('选择项目分类...')
    await page.selectOption('select >> nth=0', { value: 'web' })
    await page.waitForTimeout(500)
    
    console.log('选择预算范围...')
    await page.selectOption('select >> nth=1', { value: '50000-100000' })
    await page.waitForTimeout(500)
    
    console.log('填写项目描述...')
    const longDescription = '这是一个完整的DevMatch项目发布功能测试。项目包含了完整的表单验证、UI样式检查、用户交互测试等功能。通过这个测试可以验证修复后的下一步按钮功能是否正常工作，确保用户能够正常进行项目发布流程，为用户提供良好的项目发布体验。'
    await page.fill('textarea[placeholder*="详细描述你的项目"]', longDescription)

    // 触发最后一个字段的blur事件
    await page.locator('textarea[placeholder*="详细描述你的项目"]').blur()
    await page.waitForTimeout(1000)

    // 验证按钮现在可以点击
    await expect(nextButton).toBeEnabled()

    // 成功点击下一步
    console.log('点击下一步按钮...')
    await nextButton.click()
    await page.waitForTimeout(1000)

    // 验证进入第二步
    await expect(page.locator('.step.active .step-circle')).toHaveText('2')
    await expect(page.locator('.progress-title')).toHaveText('团队和需求信息')
    
    console.log('✅ 成功进入第二步！')
  })
}) 