import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display homepage correctly', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/小概率/)

    // 检查导航栏
    await expect(page.locator('.navbar')).toBeVisible()
    await expect(page.locator('.logo')).toContainText('小概率')

    // 检查英雄区块
    await expect(page.locator('.hero-section')).toBeVisible()
    await expect(page.locator('.hero-title')).toBeVisible()
    await expect(page.locator('.hero-subtitle')).toBeVisible()

    // 检查CTA按钮
    await expect(page.locator('text=立即开始')).toBeVisible()
    await expect(page.locator('text=浏览项目')).toBeVisible()
  })

  test('should navigate to auth page when clicking login', async ({ page }) => {
    // 点击登录按钮
    await page.locator('[data-testid="login-button"]').click()
    
    // 验证跳转到登录页面
    await expect(page).toHaveURL(/.*auth/)
    await expect(page.locator('.auth-container')).toBeVisible()
  })

  test('should navigate to projects page', async ({ page }) => {
    // 点击项目广场链接
    await page.locator('text=项目广场').first().click()
    
    // 验证跳转到项目页面
    await expect(page).toHaveURL(/.*projects/)
  })

  test('should show features section', async ({ page }) => {
    // 检查功能区块
    await expect(page.locator('.features-section')).toBeVisible()
    await expect(page.locator('.feature-card')).toHaveCount(4)

    // 检查功能卡片内容
    await expect(page.locator('text=智能匹配')).toBeVisible()
    await expect(page.locator('text=技能学院')).toBeVisible()
    await expect(page.locator('text=投资生态')).toBeVisible()
    await expect(page.locator('text=DAO治理')).toBeVisible()
  })

  test('should show success cases', async ({ page }) => {
    // 检查成功案例区块
    await expect(page.locator('.success-section')).toBeVisible()
    await expect(page.locator('.success-card')).toHaveCount(2)

    // 检查案例标题
    await expect(page.locator('text=智能健身App')).toBeVisible()
    await expect(page.locator('text=区块链社交平台')).toBeVisible()
  })

  test('should have responsive design', async ({ page }) => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 检查移动端导航
    await expect(page.locator('.navbar')).toBeVisible()
    await expect(page.locator('.hero-section')).toBeVisible()
    
    // 检查功能卡片在移动端的显示
    await expect(page.locator('.features-grid')).toBeVisible()
  })

  test('should have proper SEO elements', async ({ page }) => {
    // 检查meta标签
    await expect(page.locator('meta[name="description"]')).toHaveAttribute(
      'content', 
      /小概率.*项目全生命周期生态平台/
    )

    // 检查页面结构
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.locator('main')).toBeVisible()
    await expect(page.locator('footer')).toBeVisible()
  })

  test('should load images properly', async ({ page }) => {
    // 检查成功案例图片是否加载
    const images = page.locator('.success-image img')
    const count = await images.count()
    
    for (let i = 0; i < count; i++) {
      await expect(images.nth(i)).toBeVisible()
      await expect(images.nth(i)).toHaveAttribute('src', /.+/)
    }
  })

  test('should have working footer links', async ({ page }) => {
    // 检查页脚
    await expect(page.locator('.footer')).toBeVisible()
    
    // 检查页脚链接（注意这些可能是内部链接）
    const footerLinks = page.locator('.footer-links a')
    const linkCount = await footerLinks.count()
    expect(linkCount).toBeGreaterThan(0)
  })
}) 