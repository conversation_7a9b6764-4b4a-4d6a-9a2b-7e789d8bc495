{"root": true, "env": {"node": true, "browser": true, "es2021": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended"], "parserOptions": {"ecmaVersion": 2021, "sourceType": "module"}, "plugins": ["vue"], "rules": {"no-console": "off", "no-debugger": "off", "vue/multi-word-component-names": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "vue/no-unused-vars": "warn"}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"jest": true, "vitest": true}}]}