name: 🎨 UI还原测试

on:
  push:
    branches: [ main, master, develop ]
    paths:
      - 'frontend/**'
      - '.github/workflows/ui-restoration-test.yml'
  pull_request:
    branches: [ main, master, develop ]
    paths:
      - 'frontend/**'
  schedule:
    # 每日定时检查 (UTC 02:00 = 北京时间 10:00)
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      browser:
        description: '选择测试浏览器'
        required: false
        default: 'all'
        type: choice
        options:
        - all
        - chromium
        - firefox
        - webkit
      update_snapshots:
        description: '是否更新基准截图'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/pw-browsers

jobs:
  # 前置检查任务
  pre-checks:
    runs-on: ubuntu-latest
    outputs:
      should-run: ${{ steps.changes.outputs.frontend }}
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 🔍 检测变更文件
      uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          frontend:
            - 'frontend/**'
            - '.github/workflows/ui-restoration-test.yml'

    - name: 📊 输出检查结果
      run: |
        echo "前端文件变更: ${{ steps.changes.outputs.frontend }}"
        echo "是否需要运行UI测试: ${{ steps.changes.outputs.frontend }}"

  # 主要UI测试任务
  ui-restoration-test:
    needs: pre-checks
    if: needs.pre-checks.outputs.should-run == 'true' || github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    
    strategy:
      fail-fast: false
      matrix:
        browser: 
          - ${{ github.event.inputs.browser == 'all' && 'chromium' || github.event.inputs.browser || 'chromium' }}
          - ${{ github.event.inputs.browser == 'all' && 'firefox' || '' }}
          - ${{ github.event.inputs.browser == 'all' && 'webkit' || '' }}
        exclude:
          - browser: ''
        
    defaults:
      run:
        working-directory: frontend

    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4

    - name: 📦 设置Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: 📋 显示环境信息
      run: |
        echo "🔧 Node版本: $(node --version)"
        echo "📦 NPM版本: $(npm --version)"
        echo "🌐 浏览器: ${{ matrix.browser }}"
        echo "🔄 更新截图: ${{ github.event.inputs.update_snapshots }}"
        echo "📅 运行时间: $(date)"

    - name: 🛠️ 安装依赖
      run: |
        npm ci --prefer-offline --no-audit
        echo "依赖安装完成"

    - name: 🎭 缓存Playwright浏览器
      uses: actions/cache@v3
      id: playwright-cache
      with:
        path: ${{ env.PLAYWRIGHT_BROWSERS_PATH }}
        key: ${{ runner.os }}-playwright-${{ matrix.browser }}-${{ hashFiles('frontend/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-playwright-${{ matrix.browser }}-
          ${{ runner.os }}-playwright-

    - name: 🔧 安装Playwright浏览器
      run: |
        if [ "${{ steps.playwright-cache.outputs.cache-hit }}" = "true" ]; then
          echo "从缓存恢复Playwright浏览器"
        else
          echo "下载并安装Playwright浏览器"
        fi
        npx playwright install --with-deps ${{ matrix.browser }}

    - name: 🏗️ 构建项目
      run: |
        echo "开始构建前端项目..."
        npm run build
        echo "构建完成"

    - name: 📝 代码质量检查
      run: |
        echo "运行ESLint检查..."
        npm run lint
        echo "运行TypeScript类型检查..."
        npm run type-check

    - name: 🚀 启动开发服务器
      run: |
        echo "启动开发服务器..."
        npm run dev &
        
        # 等待服务器启动
        timeout=60
        while ! curl -f -s http://localhost:3001 > /dev/null; do
          if [ $timeout -le 0 ]; then
            echo "❌ 服务器启动超时"
            exit 1
          fi
          echo "等待服务器启动... (剩余${timeout}秒)"
          sleep 2
          timeout=$((timeout - 2))
        done
        echo "✅ 服务器启动成功"

    - name: 🎨 运行UI还原测试
      run: |
        echo "开始运行UI还原测试..."
        if [ "${{ github.event.inputs.update_snapshots }}" = "true" ]; then
          echo "更新基准截图模式"
          npx playwright test --config=tests/ui-restoration/ui-test.config.ts --project="${{ matrix.browser }}" --update-snapshots
        else
          echo "常规测试模式"
          npx playwright test --config=tests/ui-restoration/ui-test.config.ts --project="${{ matrix.browser }}"
        fi
      env:
        CI: true

    - name: 📊 生成测试报告
      if: always()
      run: |
        echo "生成自定义测试报告..."
        node scripts/generate-test-report.js || echo "报告生成失败，继续执行"

    - name: 📤 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: ui-test-report-${{ matrix.browser }}-${{ github.run_number }}
        path: |
          frontend/tests/ui-restoration/ui-restoration-report/
          frontend/tests/ui-restoration/test-results/
          frontend/tests/ui-restoration/reports/
        retention-days: 30

    - name: 📤 上传失败截图
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: failed-screenshots-${{ matrix.browser }}-${{ github.run_number }}
        path: |
          frontend/tests/ui-restoration/test-results/
          frontend/tests/ui-restoration/**/*-actual.png
          frontend/tests/ui-restoration/**/*-diff.png
        retention-days: 7

    - name: 📤 上传更新的基准截图
      uses: actions/upload-artifact@v3
      if: github.event.inputs.update_snapshots == 'true'
      with:
        name: updated-baselines-${{ matrix.browser }}-${{ github.run_number }}
        path: frontend/tests/ui-restoration/**/*-expected.png
        retention-days: 7

  # 汇总测试结果
  test-summary:
    needs: [pre-checks, ui-restoration-test]
    if: always() && needs.pre-checks.outputs.should-run == 'true'
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4

    - name: 📊 汇总测试结果
      run: |
        echo "## 🎨 UI还原测试结果汇总" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [ "${{ needs.ui-restoration-test.result }}" = "success" ]; then
          echo "✅ **测试状态**: 全部通过" >> $GITHUB_STEP_SUMMARY
          echo "🎯 **质量保证**: UI实现与设计原型100%一致" >> $GITHUB_STEP_SUMMARY
        elif [ "${{ needs.ui-restoration-test.result }}" = "failure" ]; then
          echo "❌ **测试状态**: 存在失败项" >> $GITHUB_STEP_SUMMARY
          echo "🔧 **需要修复**: 请查看测试报告了解详情" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ **测试状态**: 未完成或被跳过" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 📋 测试详情" >> $GITHUB_STEP_SUMMARY
        echo "- **测试页面**: 7个主要页面" >> $GITHUB_STEP_SUMMARY
        echo "- **测试用例**: 111个详细测试" >> $GITHUB_STEP_SUMMARY
        echo "- **浏览器**: ${{ strategy.matrix.browser }}" >> $GITHUB_STEP_SUMMARY
        echo "- **运行时间**: $(date)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔗 相关链接" >> $GITHUB_STEP_SUMMARY
        echo "- [查看测试报告](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})" >> $GITHUB_STEP_SUMMARY
        echo "- [UI测试文档](./UI-RESTORATION-TESTING-COMPREHENSIVE.md)" >> $GITHUB_STEP_SUMMARY

    - name: 📝 创建PR评论
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const testResult = '${{ needs.ui-restoration-test.result }}';
          const emoji = testResult === 'success' ? '✅' : '❌';
          const status = testResult === 'success' ? '全部通过' : '存在失败';
          
          const comment = `## ${emoji} UI还原测试结果
          
          **测试状态**: ${status}
          **测试页面**: 7个主要页面
          **测试用例**: 111个详细测试
          **运行时间**: ${new Date().toLocaleString('zh-CN')}
          
          ${testResult === 'success' 
            ? '🎯 UI实现与设计原型100%一致，可以安全合并！' 
            : '🔧 发现UI差异，请查看测试报告进行修复'}
          
          📊 [查看详细报告](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # 通知任务
  notify:
    needs: [ui-restoration-test]
    if: always() && (github.event_name == 'schedule' || github.ref == 'refs/heads/main')
    runs-on: ubuntu-latest
    
    steps:
    - name: 📢 发送通知
      run: |
        if [ "${{ needs.ui-restoration-test.result }}" = "failure" ]; then
          echo "🚨 UI还原测试失败，需要立即处理！"
          # 这里可以集成 Slack、企业微信、邮件等通知
        else
          echo "✅ UI还原测试通过，系统运行正常"
        fi 