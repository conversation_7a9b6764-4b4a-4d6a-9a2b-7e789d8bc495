#!/bin/bash

# Git pre-commit hook for UI restoration tests
# 在每次提交前自动运行UI还原测试

set -e

echo "🔍 执行提交前检查..."

# 检查是否在frontend目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在frontend目录下运行"
    exit 1
fi

# 检查是否有前端文件变更
frontend_files_changed=$(git diff --cached --name-only | grep -E '\.(vue|ts|js|css|scss)$' | wc -l)

if [ "$frontend_files_changed" -eq 0 ]; then
    echo "✅ 没有前端文件变更，跳过UI测试"
    exit 0
fi

echo "📝 检测到 $frontend_files_changed 个前端文件变更"

# 运行代码检查
echo "🔧 运行代码检查..."
npm run lint

# 运行类型检查
echo "🔍 运行类型检查..."
npm run type-check

# 运行单元测试
echo "🧪 运行单元测试..."
npm run test:unit

# 检查开发服务器是否运行
if ! curl -f -s http://localhost:3001 > /dev/null; then
    echo "⚠️  开发服务器未运行，跳过UI还原测试"
    echo "💡 提示：运行 'npm run dev' 启动开发服务器后再提交"
    exit 0
fi

# 运行UI还原测试
echo "🎨 运行UI还原测试..."
if npm run test:ui-restoration; then
    echo "✅ 所有测试通过，可以提交"
else
    echo "❌ UI还原测试失败，请修复后再提交"
    echo "📄 查看详细报告: npm run test:ui-restoration:report"
    exit 1
fi

echo "🎉 提交前检查完成！" 