/**
 * 微信支付后端接口示例配置
 * 这是前端对接后端API需要的接口规范
 */

// ===== 后端API接口设计 =====

/**
 * 1. 创建支付订单
 * POST /api/payment/wechat/create
 */
interface CreateOrderRequest {
  amount: number        // 支付金额（分）
  description: string   // 商品描述
  orderType: string    // 订单类型：certification, contact_view, other
  userId: number       // 用户ID
  metadata?: object    // 附加数据
  notifyUrl: string    // 支付结果通知地址
  merchantId: string   // 商户号
}

interface CreateOrderResponse {
  success: boolean
  data: {
    orderId: string      // 系统订单号
    prepayId: string     // 微信预支付交易会话标识
    codeUrl: string      // 二维码链接（用于生成支付二维码）
    qrCodeUrl?: string   // 可选：后端生成的二维码图片URL
    orderStatus: string  // 订单状态
    expiresAt: string    // 订单过期时间
  }
  message?: string
}

/**
 * 2. 查询支付状态
 * GET /api/payment/wechat/query/:orderId
 */
interface QueryPaymentResponse {
  success: boolean
  data: {
    orderId: string        // 系统订单号
    status: string         // 支付状态
    transactionId?: string // 微信交易号
    paidAt?: string       // 支付完成时间
    amount: number        // 支付金额（分）
  }
  message?: string
}

/**
 * 3. 取消订单
 * POST /api/payment/wechat/cancel/:orderId
 */
interface CancelOrderResponse {
  success: boolean
  message?: string
}

/**
 * 4. 支付结果通知（微信回调）
 * POST /api/wechat/notify
 * Content-Type: application/json
 */
interface WechatNotifyRequest {
  // 微信支付结果通知的请求体
  // 需要验证签名和解密
}

// ===== 后端实现需要的配置信息 =====

/**
 * 微信支付配置（需要在后端设置）
 */
const WECHAT_PAY_CONFIG = {
  // 基础配置
  merchantId: '1624570874',     // 商户号（已提供）
  appId: '',                    // 应用ID（需要申请）
  apiKey: '',                   // API密钥（需要申请）
  apiV3Key: '',                 // APIv3密钥（需要申请）
  
  // 证书配置
  certSerialNo: '',             // 证书序列号（需要申请）
  privateKeyPath: '',           // 商户私钥文件路径（需要申请）
  certificatePath: '',          // 商户证书文件路径（需要申请）
  
  // API地址
  baseUrl: 'https://api.mch.weixin.qq.com',
  
  // 回调地址
  notifyUrl: 'https://yourdomain.com/api/wechat/notify',
  
  // 其他配置
  signType: 'RSA',             // 签名类型
  tradeType: 'NATIVE',         // 交易类型（扫码支付）
}

// ===== Node.js 后端实现示例 =====

/**
 * 使用 Express + 微信支付官方SDK 的实现示例
 */

// 安装依赖
// npm install wechatpay-node-v3 express body-parser

/*
const express = require('express')
const { Payment } = require('wechatpay-node-v3')
const fs = require('fs')

const app = express()
app.use(express.json())

// 初始化微信支付
const payment = new Payment({
  appid: process.env.WECHAT_APPID,
  mchid: process.env.WECHAT_MCHID || '1624570874',
  serial_no: process.env.WECHAT_CERT_SERIAL_NO,
  publicKey: fs.readFileSync(process.env.WECHAT_PUBLIC_KEY_PATH),
  privateKey: fs.readFileSync(process.env.WECHAT_PRIVATE_KEY_PATH),
  key: process.env.WECHAT_API_V3_KEY,
  sandbox: process.env.NODE_ENV !== 'production'
})

// 1. 创建支付订单
app.post('/api/payment/wechat/create', async (req, res) => {
  try {
    const { amount, description, orderType, userId, metadata } = req.body
    
    // 生成系统订单号
    const orderId = generateOrderId()
    
    // 调用微信支付API
    const params = {
      appid: process.env.WECHAT_APPID,
      mchid: process.env.WECHAT_MCHID,
      description: description,
      out_trade_no: orderId,
      notify_url: `${process.env.BASE_URL}/api/wechat/notify`,
      amount: {
        total: amount,
        currency: 'CNY'
      }
    }
    
    const result = await payment.transactions_native(params)
    
    // 保存订单到数据库
    await saveOrder({
      orderId,
      userId,
      amount,
      description,
      orderType,
      metadata,
      status: 'NOTPAY',
      wechatOrderId: result.prepay_id,
      createdAt: new Date()
    })
    
    res.json({
      success: true,
      data: {
        orderId,
        prepayId: result.prepay_id,
        codeUrl: result.code_url,
        orderStatus: 'NOTPAY',
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString()
      }
    })
  } catch (error) {
    console.error('创建支付订单失败:', error)
    res.status(500).json({
      success: false,
      message: '创建订单失败'
    })
  }
})

// 2. 查询支付状态
app.get('/api/payment/wechat/query/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params
    
    // 查询微信支付状态
    const result = await payment.query({ out_trade_no: orderId })
    
    // 更新数据库订单状态
    await updateOrderStatus(orderId, result.trade_state)
    
    res.json({
      success: true,
      data: {
        orderId,
        status: result.trade_state,
        transactionId: result.transaction_id,
        paidAt: result.success_time,
        amount: result.amount.total
      }
    })
  } catch (error) {
    console.error('查询支付状态失败:', error)
    res.status(500).json({
      success: false,
      message: '查询失败'
    })
  }
})

// 3. 取消订单
app.post('/api/payment/wechat/cancel/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params
    
    // 调用微信关闭订单API
    await payment.close({ out_trade_no: orderId })
    
    // 更新数据库状态
    await updateOrderStatus(orderId, 'CLOSED')
    
    res.json({
      success: true,
      message: '订单已取消'
    })
  } catch (error) {
    console.error('取消订单失败:', error)
    res.status(500).json({
      success: false,
      message: '取消订单失败'
    })
  }
})

// 4. 微信支付结果通知
app.post('/api/wechat/notify', async (req, res) => {
  try {
    // 验证签名
    const { headers, body } = req
    const signature = headers['wechatpay-signature']
    const timestamp = headers['wechatpay-timestamp']
    const nonce = headers['wechatpay-nonce']
    const serial = headers['wechatpay-serial']
    
    // 解密通知数据
    const decrypted = payment.decipher_gcm(
      body.resource.ciphertext,
      body.resource.associated_data,
      body.resource.nonce,
      process.env.WECHAT_API_V3_KEY
    )
    
    const paymentData = JSON.parse(decrypted)
    
    // 处理支付结果
    if (paymentData.trade_state === 'SUCCESS') {
      // 支付成功，更新订单状态
      await updateOrderStatus(paymentData.out_trade_no, 'SUCCESS')
      
      // 执行业务逻辑（如开通认证服务）
      await handlePaymentSuccess(paymentData.out_trade_no, paymentData)
    }
    
    // 返回成功响应
    res.json({ code: 'SUCCESS', message: '成功' })
  } catch (error) {
    console.error('处理微信通知失败:', error)
    res.status(500).json({ code: 'FAIL', message: '失败' })
  }
})

// 辅助函数
function generateOrderId() {
  return `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

async function saveOrder(orderData) {
  // 保存订单到数据库的实现
}

async function updateOrderStatus(orderId, status) {
  // 更新订单状态的实现
}

async function handlePaymentSuccess(orderId, paymentData) {
  // 处理支付成功后的业务逻辑
  // 如：为用户开通简历认证服务
}

app.listen(3000, () => {
  console.log('服务器启动在端口 3000')
})
*/

// ===== 数据库表结构 =====

/**
 * 支付订单表（MySQL示例）
 */
/*
CREATE TABLE payment_orders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id VARCHAR(64) UNIQUE NOT NULL,
  user_id INT NOT NULL,
  amount INT NOT NULL COMMENT '金额（分）',
  description VARCHAR(255) NOT NULL,
  order_type ENUM('certification', 'contact_view', 'other') NOT NULL,
  status ENUM('NOTPAY', 'SUCCESS', 'REFUND', 'CLOSED', 'REVOKED', 'USERPAYING', 'PAYERROR') DEFAULT 'NOTPAY',
  wechat_order_id VARCHAR(64),
  wechat_transaction_id VARCHAR(64),
  metadata JSON,
  paid_at TIMESTAMP NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_order_id (order_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
*/

export default {
  // 导出配置供参考
  WECHAT_PAY_CONFIG,
  
  // API接口类型定义
  CreateOrderRequest,
  CreateOrderResponse,
  QueryPaymentResponse,
  CancelOrderResponse,
  WechatNotifyRequest
}