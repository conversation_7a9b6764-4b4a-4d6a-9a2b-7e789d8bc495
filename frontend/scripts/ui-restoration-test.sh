#!/bin/bash

# UI还原测试自动化脚本
# 用于在前端修改后自动执行所有UI还原测试

set -e

echo "🚀 开始UI还原测试自动化流程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查开发服务器是否运行
check_dev_server() {
    log_info "检查开发服务器状态..."
    
    if curl -f -s http://localhost:3001 > /dev/null; then
        log_success "开发服务器正在运行 (http://localhost:3001)"
        return 0
    else
        log_warning "开发服务器未运行，尝试启动..."
        
        # 启动开发服务器（后台运行）
        npm run dev &
        DEV_SERVER_PID=$!
        
        # 等待服务器启动
        echo "等待开发服务器启动..."
        for i in {1..30}; do
            if curl -f -s http://localhost:3001 > /dev/null; then
                log_success "开发服务器已启动"
                return 0
            fi
            sleep 2
        done
        
        log_error "开发服务器启动失败"
        return 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    if [ ! -d "node_modules" ]; then
        log_info "安装项目依赖..."
        npm ci
    fi
    
    # 检查Playwright是否安装
    if ! npx playwright --version > /dev/null 2>&1; then
        log_info "安装Playwright..."
        npm install @playwright/test
    fi
    
    # 安装浏览器
    log_info "安装Playwright浏览器..."
    npx playwright install --with-deps
    
    log_success "依赖安装完成"
}

# 运行UI还原测试
run_ui_tests() {
    log_info "开始运行UI还原测试..."
    
    # 创建测试报告目录
    mkdir -p tests/ui-restoration/reports
    
    # 运行测试
    if npx playwright test --config=tests/ui-restoration/ui-test.config.ts --reporter=html --reporter=json; then
        log_success "UI还原测试全部通过！"
        return 0
    else
        log_error "UI还原测试失败"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    log_info "生成测试报告..."
    
    # 生成HTML报告
    if [ -f "tests/ui-restoration/ui-restoration-results.json" ]; then
        log_success "测试报告已生成"
        
        # 计算测试统计
        TOTAL_TESTS=$(cat tests/ui-restoration/ui-restoration-results.json | jq '.suites[].specs | length' 2>/dev/null || echo "0")
        PASSED_TESTS=$(cat tests/ui-restoration/ui-restoration-results.json | jq '.suites[].specs[] | select(.ok == true) | .title' 2>/dev/null | wc -l || echo "0")
        FAILED_TESTS=$(cat tests/ui-restoration/ui-restoration-results.json | jq '.suites[].specs[] | select(.ok == false) | .title' 2>/dev/null | wc -l || echo "0")
        
        echo ""
        echo "📊 测试统计："
        echo "   总测试数：$TOTAL_TESTS"
        echo "   通过数：$PASSED_TESTS"
        echo "   失败数：$FAILED_TESTS"
        echo ""
        
        if [ "$FAILED_TESTS" -gt 0 ]; then
            log_warning "有 $FAILED_TESTS 个测试失败，请查看详细报告"
            echo "📄 查看详细报告: npx playwright show-report tests/ui-restoration/ui-restoration-report"
        else
            log_success "所有测试通过！🎉"
        fi
    else
        log_warning "测试报告文件不存在"
    fi
}

# 清理函数
cleanup() {
    if [ ! -z "$DEV_SERVER_PID" ]; then
        log_info "清理开发服务器进程..."
        kill $DEV_SERVER_PID 2>/dev/null || true
    fi
}

# 设置清理陷阱
trap cleanup EXIT

# 主执行流程
main() {
    log_info "UI还原测试自动化开始..."
    
    # 检查当前目录
    if [ ! -f "package.json" ]; then
        log_error "请在frontend目录下运行此脚本"
        exit 1
    fi
    
    # 执行步骤
    install_dependencies
    check_dev_server
    run_ui_tests
    generate_report
    
    log_success "UI还原测试自动化完成！"
}

# 参数处理
case "${1:-}" in
    "help"|"-h"|"--help")
        echo "UI还原测试自动化脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  help, -h, --help    显示帮助信息"
        echo "  report             只生成测试报告"
        echo "  install            只安装依赖"
        echo ""
        echo "示例:"
        echo "  $0                 运行完整的测试流程"
        echo "  $0 report          只生成测试报告"
        echo "  $0 install         只安装依赖"
        ;;
    "report")
        generate_report
        ;;
    "install")
        install_dependencies
        ;;
    *)
        main
        ;;
esac 