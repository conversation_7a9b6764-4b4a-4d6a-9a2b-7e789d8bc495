#!/usr/bin/env node

/**
 * UI还原测试报告生成器
 * 生成美观的HTML测试报告和统计信息
 */

const fs = require('fs')
const path = require('path')

// 报告配置
const config = {
  reportsDir: 'tests/ui-restoration/reports',
  resultsFile: 'tests/ui-restoration/ui-restoration-results.json',
  outputFile: 'tests/ui-restoration/reports/summary.html'
}

// 确保报告目录存在
function ensureReportsDir() {
  if (!fs.existsSync(config.reportsDir)) {
    fs.mkdirSync(config.reportsDir, { recursive: true })
  }
}

// 读取测试结果
function readTestResults() {
  try {
    if (!fs.existsSync(config.resultsFile)) {
      console.warn(`⚠️  测试结果文件不存在: ${config.resultsFile}`)
      return null
    }
    
    const data = fs.readFileSync(config.resultsFile, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    console.error('❌ 读取测试结果失败:', error.message)
    return null
  }
}

// 生成测试统计
function generateStats(results) {
  if (!results || !results.suites) {
    return {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    }
  }

  let stats = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    duration: 0
  }

  results.suites.forEach(suite => {
    if (suite.specs) {
      suite.specs.forEach(spec => {
        stats.total++
        if (spec.ok) {
          stats.passed++
        } else {
          stats.failed++
        }
      })
    }
  })

  stats.duration = results.stats?.duration || 0

  return stats
}

// 生成HTML报告
function generateHTMLReport(results, stats) {
  const timestamp = new Date().toLocaleString('zh-CN')
  const passRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0

  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI还原测试报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
            border-left: 4px solid;
        }
        
        .stat-card.total { border-left-color: #3b82f6; }
        .stat-card.passed { border-left-color: #10b981; }
        .stat-card.failed { border-left-color: #ef4444; }
        .stat-card.rate { border-left-color: #8b5cf6; }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .results-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
        }
        
        .results-section h2 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #1e293b;
        }
        
        .test-suite {
            margin-bottom: 25px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .suite-header {
            background: #f8fafc;
            padding: 15px 20px;
            font-weight: 600;
            color: #475569;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .test-spec {
            padding: 12px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .test-spec:last-child {
            border-bottom: none;
        }
        
        .test-name {
            flex: 1;
        }
        
        .test-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-passed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .footer {
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 40px;
        }
        
        .pass-rate {
            font-size: 2rem;
        }
        
        .pass-rate.good { color: #10b981; }
        .pass-rate.warn { color: #f59e0b; }
        .pass-rate.bad { color: #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 UI还原测试报告</h1>
            <p>生成时间: ${timestamp}</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-number">${stats.total}</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-card passed">
                <div class="stat-number">${stats.passed}</div>
                <div class="stat-label">通过数</div>
            </div>
            <div class="stat-card failed">
                <div class="stat-number">${stats.failed}</div>
                <div class="stat-label">失败数</div>
            </div>
            <div class="stat-card rate">
                <div class="stat-number pass-rate ${passRate >= 90 ? 'good' : passRate >= 70 ? 'warn' : 'bad'}">${passRate}%</div>
                <div class="stat-label">通过率</div>
            </div>
        </div>
        
        <div class="results-section">
            <h2>📋 测试结果详情</h2>
            ${generateTestSuites(results)}
        </div>
        
        <div class="footer">
            <p>由 小概率UI还原测试系统 自动生成</p>
            <p>更多信息请查看详细的Playwright报告</p>
        </div>
    </div>
</body>
</html>
  `
}

// 生成测试套件HTML
function generateTestSuites(results) {
  if (!results || !results.suites) {
    return '<p>没有找到测试结果</p>'
  }

  return results.suites.map(suite => {
    const specs = suite.specs || []
    
    return `
      <div class="test-suite">
        <div class="suite-header">
          📁 ${suite.title || '未知测试套件'}
        </div>
        ${specs.map(spec => `
          <div class="test-spec">
            <div class="test-name">${spec.title}</div>
            <div class="test-status ${spec.ok ? 'status-passed' : 'status-failed'}">
              ${spec.ok ? '✅ PASSED' : '❌ FAILED'}
            </div>
          </div>
        `).join('')}
      </div>
    `
  }).join('')
}

// 主函数
function main() {
  console.log('🎨 开始生成UI还原测试报告...')

  ensureReportsDir()
  
  const results = readTestResults()
  const stats = generateStats(results)
  
  console.log('📊 测试统计:')
  console.log(`   总测试数: ${stats.total}`)
  console.log(`   通过数: ${stats.passed}`)
  console.log(`   失败数: ${stats.failed}`)
  console.log(`   通过率: ${stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : 0}%`)

  const htmlReport = generateHTMLReport(results, stats)
  
  try {
    fs.writeFileSync(config.outputFile, htmlReport, 'utf8')
    console.log(`✅ 报告已生成: ${config.outputFile}`)
  } catch (error) {
    console.error('❌ 生成报告失败:', error.message)
    process.exit(1)
  }
}

// 如果直接执行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  generateStats,
  generateHTMLReport,
  readTestResults
} 