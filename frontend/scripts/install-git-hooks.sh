#!/bin/bash

# Git hooks安装脚本
# 自动设置项目的Git hooks

set -e

echo "🔧 安装Git hooks..."

# 检查是否在Git仓库中
if [ ! -d "../.git" ]; then
    echo "❌ 当前目录不在Git仓库中"
    exit 1
fi

# 创建hooks目录（如果不存在）
mkdir -p ../.git/hooks

# 复制pre-commit hook
if [ -f ".githooks/pre-commit" ]; then
    cp .githooks/pre-commit ../.git/hooks/pre-commit
    chmod +x ../.git/hooks/pre-commit
    echo "✅ pre-commit hook已安装"
else
    echo "❌ .githooks/pre-commit 文件不存在"
    exit 1
fi

# 验证安装
if [ -x "../.git/hooks/pre-commit" ]; then
    echo "🎉 Git hooks安装成功！"
    echo ""
    echo "现在每次提交时都会自动运行："
    echo "  - 代码检查 (ESLint)"
    echo "  - 类型检查 (TypeScript)"
    echo "  - 单元测试 (Vitest)"
    echo "  - UI还原测试 (Playwright)"
    echo ""
    echo "如需跳过hooks: git commit -m '消息' --no-verify"
else
    echo "❌ Git hooks安装失败"
    exit 1
fi 