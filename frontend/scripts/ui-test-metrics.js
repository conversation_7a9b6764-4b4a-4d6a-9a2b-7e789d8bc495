#!/usr/bin/env node

/**
 * UI测试状态监控和度量脚本
 * 收集测试数据，生成趋势报告，监控测试健康度
 */

const fs = require('fs')
const path = require('path')

// 配置信息
const config = {
  metricsFile: 'tests/ui-restoration/metrics/test-metrics.json',
  trendsFile: 'tests/ui-restoration/metrics/trends.json',
  reportFile: 'tests/ui-restoration/metrics/health-report.html',
  maxHistoryEntries: 100
}

// 确保度量目录存在
function ensureMetricsDir() {
  const metricsDir = path.dirname(config.metricsFile)
  if (!fs.existsSync(metricsDir)) {
    fs.mkdirSync(metricsDir, { recursive: true })
  }
}

// 收集当前测试度量
function collectCurrentMetrics() {
  const metrics = {
    timestamp: new Date().toISOString(),
    date: new Date().toLocaleDateString('zh-CN'),
    time: new Date().toLocaleTimeString('zh-CN'),
    
    // 测试覆盖信息
    coverage: {
      totalPages: 7,
      testedPages: 7,
      totalTests: 111,
      coveragePercentage: 100
    },
    
    // 页面测试详情
    pages: [
      { name: '首页', file: 'homepage.ui.spec.ts', tests: 12, status: 'active' },
      { name: '认证页面', file: 'auth.ui.spec.ts', tests: 15, status: 'active' },
      { name: '项目列表', file: 'projects.ui.spec.ts', tests: 17, status: 'active' },
      { name: '项目详情', file: 'project-detail.ui.spec.ts', tests: 16, status: 'active' },
      { name: '技能学院', file: 'academy.ui.spec.ts', tests: 18, status: 'active' },
      { name: '投资生态', file: 'investment.ui.spec.ts', tests: 16, status: 'active' },
      { name: '咨询建议', file: 'advice.ui.spec.ts', tests: 17, status: 'active' }
    ],
    
    // 技术栈信息
    techStack: {
      testFramework: 'Playwright',
      frontend: 'Vue3 + TypeScript + Tailwind CSS',
      automation: 'GitHub Actions + Git Hooks',
      reporting: 'HTML + JSON + Custom'
    }
  }
  
  // 尝试读取最近的测试结果
  try {
    const resultsFile = 'tests/ui-restoration/test-results/results.json'
    if (fs.existsSync(resultsFile)) {
      const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'))
      metrics.lastRun = {
        timestamp: results.timestamp || new Date().toISOString(),
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        success: results.stats?.failed === 0
      }
    }
  } catch (error) {
    console.warn('无法读取测试结果:', error.message)
  }
  
  return metrics
}

// 保存度量数据
function saveMetrics(metrics) {
  ensureMetricsDir()
  
  // 保存当前度量
  fs.writeFileSync(config.metricsFile, JSON.stringify(metrics, null, 2))
  
  // 更新历史趋势
  updateTrends(metrics)
  
  console.log('✅ 度量数据已保存:', config.metricsFile)
}

// 更新趋势数据
function updateTrends(currentMetrics) {
  let trends = { history: [] }
  
  // 读取现有趋势数据
  if (fs.existsSync(config.trendsFile)) {
    try {
      trends = JSON.parse(fs.readFileSync(config.trendsFile, 'utf8'))
    } catch (error) {
      console.warn('无法读取趋势数据，创建新的:', error.message)
    }
  }
  
  // 添加当前数据点
  trends.history.push({
    date: currentMetrics.date,
    timestamp: currentMetrics.timestamp,
    totalTests: currentMetrics.coverage.totalTests,
    passed: currentMetrics.lastRun?.passed || 0,
    failed: currentMetrics.lastRun?.failed || 0,
    success: currentMetrics.lastRun?.success || false,
    duration: currentMetrics.lastRun?.duration || 0
  })
  
  // 保持历史记录在限制范围内
  if (trends.history.length > config.maxHistoryEntries) {
    trends.history = trends.history.slice(-config.maxHistoryEntries)
  }
  
  // 计算统计信息
  trends.stats = calculateTrendStats(trends.history)
  
  fs.writeFileSync(config.trendsFile, JSON.stringify(trends, null, 2))
  console.log('📈 趋势数据已更新:', config.trendsFile)
}

// 计算趋势统计
function calculateTrendStats(history) {
  if (history.length === 0) return {}
  
  const recent = history.slice(-30) // 最近30次
  const successCount = recent.filter(run => run.success).length
  const totalRuns = recent.length
  
  return {
    recentSuccessRate: Math.round((successCount / totalRuns) * 100),
    averageDuration: Math.round(recent.reduce((sum, run) => sum + run.duration, 0) / totalRuns),
    totalRuns: history.length,
    lastSuccess: history.filter(run => run.success).pop()?.timestamp,
    lastFailure: history.filter(run => !run.success).pop()?.timestamp
  }
}

// 生成健康报告
function generateHealthReport(metrics, trends) {
  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI测试健康度报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: #f5f7fa; color: #2d3748; line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 40px; }
        .title { font-size: 2.5rem; font-weight: 700; color: #1a365d; margin-bottom: 10px; }
        .subtitle { font-size: 1.1rem; color: #718096; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 40px; }
        .metric-card { 
            background: white; border-radius: 12px; padding: 24px; 
            box-shadow: 0 4px 6px rgba(0,0,0,0.05); border: 1px solid #e2e8f0;
        }
        .metric-title { font-size: 0.9rem; color: #718096; margin-bottom: 8px; text-transform: uppercase; letter-spacing: 0.5px; }
        .metric-value { font-size: 2rem; font-weight: 700; color: #2d3748; }
        .metric-trend { font-size: 0.85rem; margin-top: 8px; }
        .trend-up { color: #38a169; }
        .trend-down { color: #e53e3e; }
        .chart-container { background: white; border-radius: 12px; padding: 24px; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); }
        .status-good { color: #38a169; }
        .status-warning { color: #d69e2e; }
        .status-error { color: #e53e3e; }
        .page-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; }
        .page-item { background: white; border-radius: 8px; padding: 16px; border-left: 4px solid #38a169; }
        .footer { text-align: center; margin-top: 40px; color: #718096; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎨 UI测试健康度报告</h1>
            <p class="subtitle">生成时间: ${metrics.date} ${metrics.time}</p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">测试覆盖率</div>
                <div class="metric-value status-good">${metrics.coverage.coveragePercentage}%</div>
                <div class="metric-trend">${metrics.coverage.totalTests} 个测试用例</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">页面覆盖</div>
                <div class="metric-value status-good">${metrics.coverage.testedPages}/${metrics.coverage.totalPages}</div>
                <div class="metric-trend">100% 页面覆盖</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">最近成功率</div>
                <div class="metric-value ${trends.stats?.recentSuccessRate >= 90 ? 'status-good' : trends.stats?.recentSuccessRate >= 70 ? 'status-warning' : 'status-error'}">${trends.stats?.recentSuccessRate || 'N/A'}%</div>
                <div class="metric-trend">近30次运行</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">平均执行时间</div>
                <div class="metric-value">${Math.round((trends.stats?.averageDuration || 0) / 1000)}s</div>
                <div class="metric-trend">平均测试时长</div>
            </div>
        </div>

        <div class="chart-container">
            <h3 style="margin-bottom: 20px;">📈 测试成功率趋势</h3>
            <canvas id="trendChart" height="100"></canvas>
        </div>

        <div class="chart-container">
            <h3 style="margin-bottom: 20px;">📋 页面测试详情</h3>
            <div class="page-list">
                ${metrics.pages.map(page => `
                    <div class="page-item">
                        <strong>${page.name}</strong><br>
                        <small>${page.file}</small><br>
                        <span style="color: #38a169;">${page.tests} 个测试</span>
                    </div>
                `).join('')}
            </div>
        </div>

        <div class="footer">
            <p>🚀 小概率UI还原测试系统 - 确保每一个像素都完美！</p>
        </div>
    </div>

    <script>
        // 绘制趋势图表
        const ctx = document.getElementById('trendChart').getContext('2d');
        const trendData = ${JSON.stringify(trends.history || [])};
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.slice(-20).map(item => item.date),
                datasets: [{
                    label: '成功率 (%)',
                    data: trendData.slice(-20).map(item => item.success ? 100 : 0),
                    borderColor: '#38a169',
                    backgroundColor: 'rgba(56, 161, 105, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>`

  ensureMetricsDir()
  fs.writeFileSync(config.reportFile, html)
  console.log('📊 健康报告已生成:', config.reportFile)
}

// 主函数
function main() {
  console.log('🔍 开始收集UI测试度量数据...')
  
  try {
    // 收集当前度量
    const currentMetrics = collectCurrentMetrics()
    
    // 保存度量数据
    saveMetrics(currentMetrics)
    
    // 读取趋势数据
    let trends = { history: [], stats: {} }
    if (fs.existsSync(config.trendsFile)) {
      trends = JSON.parse(fs.readFileSync(config.trendsFile, 'utf8'))
    }
    
    // 生成健康报告
    generateHealthReport(currentMetrics, trends)
    
    console.log('')
    console.log('📊 UI测试度量摘要:')
    console.log(`   测试覆盖率: ${currentMetrics.coverage.coveragePercentage}%`)
    console.log(`   页面覆盖: ${currentMetrics.coverage.testedPages}/${currentMetrics.coverage.totalPages}`)
    console.log(`   总测试数: ${currentMetrics.coverage.totalTests}`)
    if (trends.stats.recentSuccessRate) {
      console.log(`   近期成功率: ${trends.stats.recentSuccessRate}%`)
    }
    console.log('')
    console.log('✅ 度量数据收集完成!')
    
  } catch (error) {
    console.error('❌ 度量数据收集失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  collectCurrentMetrics,
  saveMetrics,
  generateHealthReport
} 