<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #5b21b6;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .project-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .project-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .project-summary {
            color: #666;
            margin-bottom: 8px;
        }
        .project-meta {
            font-size: 0.9em;
            color: #888;
        }
        .loading {
            text-align: center;
            color: #6366f1;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 项目API测试页面</h1>
        <p>用于调试项目列表API和数据显示问题</p>
    </div>

    <div class="test-section">
        <h2>1. API连接测试</h2>
        <button class="button" onclick="testAPIConnection()">测试API连接</button>
        <button class="button" onclick="testProjectsAPI()">测试项目API</button>
        <button class="button" onclick="testProjectsWithTransform()">测试数据转换</button>
        <div id="api-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. 项目数据展示</h2>
        <button class="button" onclick="loadAndDisplayProjects()">加载并显示项目</button>
        <div id="loading" class="loading" style="display: none;">正在加载项目数据...</div>
        <div id="projects-display"></div>
    </div>

    <div class="test-section">
        <h2>3. 诊断信息</h2>
        <div id="diagnostic-info"></div>
    </div>

    <script>
        // 显示结果的辅助函数
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
        }

        // 测试API连接
        async function testAPIConnection() {
            try {
                console.log('🔄 测试API连接...');
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                showResult('api-result', `✅ API连接成功！
状态码: ${response.status}
响应: ${JSON.stringify(data, null, 2)}`);
                
            } catch (error) {
                console.error('❌ API连接失败:', error);
                showResult('api-result', `❌ API连接失败: ${error.message}`, true);
            }
        }

        // 测试项目API
        async function testProjectsAPI() {
            try {
                console.log('🔄 测试项目API...');
                const response = await fetch('/api/projects');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('📊 项目API原始响应:', data);
                
                // 检查响应结构
                const approved = data.data?.projects?.filter(p => p.reviewStatus === 'approved') || [];
                
                showResult('api-result', `✅ 项目API测试成功！
总项目数: ${data.data?.projects?.length || 0}
已审核项目数: ${approved.length}
项目结构: ${JSON.stringify(approved.slice(0, 1), null, 2)}`);
                
            } catch (error) {
                console.error('❌ 项目API测试失败:', error);
                showResult('api-result', `❌ 项目API测试失败: ${error.message}`, true);
            }
        }

        // 数据转换函数（从Vue组件复制）
        function transformProjectData(apiProjects) {
            console.log('🔄 开始转换项目数据:', apiProjects);
            
            return apiProjects.map((project) => {
                // 提取技能标签
                const skills = project.requirements?.flatMap((req) => {
                    if (Array.isArray(req.skillName)) {
                        return req.skillName;
                    } else if (typeof req.skillName === 'string') {
                        return req.skillName.split(',').map((s) => s.trim());
                    }
                    return [];
                }).filter(Boolean) || [];
                
                // 提取职位
                const positions = project.requirements?.map((req) => req.role).filter(Boolean) || [];
                
                // 判断是否为付费项目
                const isPaid = project.requirements?.some((req) => 
                    req.cooperation === 'salary' && req.salaryAmount
                ) || false;
                
                // 计算进度阶段
                const getProgressStage = (milestone) => {
                    const stages = {
                        'team-building': { progress: 1, stage: '组建团队' },
                        'requirement-design': { progress: 1.5, stage: '需求设计' },
                        'development': { progress: 2, stage: '开发阶段' },
                        'testing-launch': { progress: 2.5, stage: '测试阶段' },
                        'completed': { progress: 3, stage: '已完成' }
                    };
                    return stages[milestone] || { progress: 1, stage: '设计阶段' };
                };
                
                const progressInfo = getProgressStage(project.currentMilestone);
                
                const transformedProject = {
                    id: project.id,
                    title: project.title,
                    summary: project.tagline || (project.description?.substring(0, 50) + '...'),
                    type: isPaid ? 'paid' : 'free',
                    isAiRecommended: Boolean(project.featured),
                    workType: project.workArrangement === 'fullTime' ? '全职' : '兼职',
                    workTime: project.workArrangement === 'fullTime' ? '标准工时' : '灵活安排',
                    location: project.location || '远程协作',
                    stage: progressInfo.stage,
                    positions: positions,
                    skills: skills,
                    progress: progressInfo.progress,
                    currentStage: progressInfo.stage,
                    timeline: project.timeframe || '预计3个月完成',
                    publishDate: new Date(project.createdAt).toLocaleDateString('zh-CN'),
                    applicants: project.applicants?.length || 0,
                    originalData: project
                };
                
                console.log('✅ 转换项目:', transformedProject);
                return transformedProject;
            });
        }

        // 测试数据转换
        async function testProjectsWithTransform() {
            try {
                console.log('🔄 测试数据转换...');
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                const approvedProjects = data.data?.projects?.filter(p => p.reviewStatus === 'approved') || [];
                const transformedProjects = transformProjectData(approvedProjects);
                
                const freeProjects = transformedProjects.filter(p => p.type === 'free');
                const paidProjects = transformedProjects.filter(p => p.type === 'paid');
                
                showResult('api-result', `✅ 数据转换测试成功！
原始项目数: ${approvedProjects.length}
转换后项目数: ${transformedProjects.length}
免费项目: ${freeProjects.length}
付费项目: ${paidProjects.length}
示例转换结果: ${JSON.stringify(transformedProjects.slice(0, 1), null, 2)}`);
                
            } catch (error) {
                console.error('❌ 数据转换测试失败:', error);
                showResult('api-result', `❌ 数据转换测试失败: ${error.message}`, true);
            }
        }

        // 加载并显示项目
        async function loadAndDisplayProjects() {
            const loadingDiv = document.getElementById('loading');
            const displayDiv = document.getElementById('projects-display');
            
            try {
                loadingDiv.style.display = 'block';
                displayDiv.innerHTML = '';
                
                console.log('🔄 加载项目数据...');
                const response = await fetch('/api/projects');
                const data = await response.json();
                
                const approvedProjects = data.data?.projects?.filter(p => p.reviewStatus === 'approved') || [];
                const transformedProjects = transformProjectData(approvedProjects);
                
                loadingDiv.style.display = 'none';
                
                if (transformedProjects.length === 0) {
                    displayDiv.innerHTML = '<p style="text-align: center; color: #666;">暂无已审核的项目</p>';
                    return;
                }
                
                // 显示项目卡片
                transformedProjects.forEach(project => {
                    const projectCard = document.createElement('div');
                    projectCard.className = 'project-card';
                    projectCard.innerHTML = `
                        <div class="project-title">${project.title}</div>
                        <div class="project-summary">${project.summary}</div>
                        <div class="project-meta">
                            类型: ${project.type === 'free' ? '免费' : '付费'} | 
                            工作性质: ${project.workType} | 
                            地点: ${project.location} | 
                            阶段: ${project.stage} |
                            技能: ${project.skills.slice(0, 3).join(', ')}
                        </div>
                    `;
                    displayDiv.appendChild(projectCard);
                });
                
                console.log('✅ 项目显示完成');
                
            } catch (error) {
                loadingDiv.style.display = 'none';
                console.error('❌ 加载项目失败:', error);
                displayDiv.innerHTML = `<div class="result error">❌ 加载项目失败: ${error.message}</div>`;
            }
        }

        // 显示诊断信息
        function showDiagnosticInfo() {
            const diagnosticDiv = document.getElementById('diagnostic-info');
            const info = {
                当前URL: window.location.href,
                用户代理: navigator.userAgent,
                当前时间: new Date().toLocaleString(),
                localStorage项目: Object.keys(localStorage),
                sessionStorage项目: Object.keys(sessionStorage)
            };
            
            diagnosticDiv.innerHTML = `
                <div class="result">
                    <h4>🔍 诊断信息</h4>
                    ${Object.entries(info).map(([key, value]) => 
                        `<div><strong>${key}:</strong> ${Array.isArray(value) ? value.join(', ') : value}</div>`
                    ).join('')}
                </div>
            `;
        }

        // 页面加载时显示诊断信息
        window.onload = function() {
            showDiagnosticInfo();
            console.log('🚀 测试页面已加载');
        };
    </script>
</body>
</html> 