<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 20px; cursor: pointer; margin: 5px; }
        .result { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin-top: 10px; }
        .error { background: #f8d7da; }
        .success { background: #d4edda; }
        .project { border: 1px solid #ccc; margin: 10px 0; padding: 15px; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>🔧 API调试页面</h1>
    
    <div class="section">
        <h2>1. 基础API测试</h2>
        <button class="button" onclick="testAPI()">测试项目API</button>
        <div id="api-result"></div>
    </div>

    <div class="section">
        <h2>2. 数据转换测试</h2>
        <button class="button" onclick="transformData()">转换并显示项目</button>
        <div id="transform-result"></div>
    </div>

    <div class="section">
        <h2>3. 项目卡片显示</h2>
        <div id="projects-display"></div>
    </div>

    <script>
        console.log('🚀 调试页面已加载');

        // 显示结果
        function showResult(id, content, isError = false) {
            const element = document.getElementById(id);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.innerHTML = content;
        }

        // 测试API
        async function testAPI() {
            try {
                console.log('📡 测试API调用...');
                const response = await fetch('/api/projects');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('✅ API响应:', data);
                
                const approved = data.data?.projects?.filter(p => p.reviewStatus === 'approved') || [];
                
                showResult('api-result', `
                    <strong>✅ API调用成功</strong><br>
                    总项目数: ${data.data?.projects?.length || 0}<br>
                    已审核项目数: ${approved.length}<br>
                    <details>
                        <summary>查看项目详情</summary>
                        <pre>${JSON.stringify(approved.slice(0, 2), null, 2)}</pre>
                    </details>
                `);
                
                return approved;
                
            } catch (error) {
                console.error('❌ API调用失败:', error);
                showResult('api-result', `❌ API调用失败: ${error.message}`, true);
                throw error;
            }
        }

        // 数据转换
        function transformProjects(apiProjects) {
            console.log('🔄 开始转换项目数据...');
            
            return apiProjects.map(project => {
                // 提取技能
                const skills = [];
                if (project.requirements) {
                    project.requirements.forEach(req => {
                        if (req.skillName) {
                            if (Array.isArray(req.skillName)) {
                                skills.push(...req.skillName);
                            } else {
                                skills.push(...req.skillName.split(',').map(s => s.trim()));
                            }
                        }
                    });
                }
                
                // 判断是否付费
                const isPaid = project.requirements?.some(req => 
                    req.cooperation === 'salary' && req.salaryAmount
                ) || false;
                
                const transformed = {
                    id: project.id,
                    title: project.title,
                    summary: project.tagline || project.description?.substring(0, 100),
                    type: isPaid ? '付费' : '免费',
                    skills: skills.filter(Boolean),
                    stage: project.currentMilestone || '未知',
                    location: project.location || '远程',
                    originalData: project
                };
                
                console.log('✅ 转换项目:', project.title, '->', transformed);
                return transformed;
            });
        }

        // 转换并显示数据
        async function transformData() {
            try {
                const projects = await testAPI();
                const transformed = transformProjects(projects);
                
                const freeProjects = transformed.filter(p => p.type === '免费');
                const paidProjects = transformed.filter(p => p.type === '付费');
                
                showResult('transform-result', `
                    <strong>✅ 数据转换成功</strong><br>
                    总数: ${transformed.length}<br>
                    免费项目: ${freeProjects.length}<br>
                    付费项目: ${paidProjects.length}<br>
                    <details>
                        <summary>查看转换后数据</summary>
                        <pre>${JSON.stringify(transformed.slice(0, 2), null, 2)}</pre>
                    </details>
                `);
                
                // 显示项目卡片
                displayProjects(transformed);
                
            } catch (error) {
                showResult('transform-result', `❌ 数据转换失败: ${error.message}`, true);
            }
        }

        // 显示项目卡片
        function displayProjects(projects) {
            const container = document.getElementById('projects-display');
            
            if (projects.length === 0) {
                container.innerHTML = '<p>暂无项目</p>';
                return;
            }
            
            container.innerHTML = projects.map(project => `
                <div class="project">
                    <h3>${project.title}</h3>
                    <p>${project.summary}</p>
                    <div><strong>类型:</strong> ${project.type}</div>
                    <div><strong>阶段:</strong> ${project.stage}</div>
                    <div><strong>地点:</strong> ${project.location}</div>
                    <div><strong>技能:</strong> ${project.skills.slice(0, 3).join(', ')}</div>
                    <button onclick="applyProject(${project.id}, '${project.title}')">申请项目</button>
                </div>
            `).join('');
            
            console.log('✅ 项目卡片显示完成');
        }

        // 申请项目
        async function applyProject(projectId, projectTitle) {
            try {
                console.log('📝 申请项目:', projectId, projectTitle);
                
                const message = prompt(`申请项目"${projectTitle}"，请输入申请理由:`) || '我对这个项目很感兴趣';
                
                const response = await fetch(`/api/projects/${projectId}/apply`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: message,
                        expectedSalary: 15000,
                        coverLetter: '感谢考虑我的申请'
                    })
                });
                
                if (response.ok) {
                    alert('✅ 申请提交成功！');
                } else {
                    const error = await response.text();
                    throw new Error(error);
                }
                
            } catch (error) {
                console.error('❌ 申请失败:', error);
                alert(`❌ 申请失败: ${error.message}`);
            }
        }
    </script>
</body>
</html> 