<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f7f9fc;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2d3748;
            margin-bottom: 10px;
        }
        .message-button {
            position: relative;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .message-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
        }
        .message-badge {
            position: absolute;
            top: -2px;
            right: -2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            animation: pulse-badge 2s infinite;
        }
        @keyframes pulse-badge {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
        }
        .test-section h3 {
            color: #4a5568;
            margin-bottom: 15px;
        }
        .api-test {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .api-result {
            margin-top: 10px;
            padding: 10px;
            background: #e6fffa;
            border-radius: 6px;
            color: #065f46;
            font-family: monospace;
            font-size: 14px;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }
        button:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>消息功能演示</h1>
            <p>模拟导航栏中的消息按钮和API功能</p>
            
            <!-- 模拟消息按钮 -->
            <div style="margin: 20px 0;">
                <a href="#" class="message-button" onclick="window.open('http://localhost:3000/messages', '_blank')">
                    <i class="fas fa-bell"></i>
                    <div class="message-badge" id="unread-count">6</div>
                </a>
            </div>
            <p><small>点击消息按钮访问消息页面（需要前端运行在3000端口）</small></p>
        </div>

        <div class="test-section">
            <h3>API功能测试</h3>
            
            <div class="api-test">
                <h4>📬 获取消息列表</h4>
                <button onclick="testGetMessages()">测试获取消息</button>
                <div id="messages-result" class="api-result" style="display: none;"></div>
            </div>
            
            <div class="api-test">
                <h4>✅ 标记消息已读</h4>
                <button onclick="testMarkAsRead()">测试标记已读</button>
                <div id="mark-read-result" class="api-result" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 消息数据结构说明</h3>
            <div style="background: white; padding: 15px; border-radius: 8px;">
                <h4>消息字段设计：</h4>
                <ul>
                    <li><strong>基础字段：</strong>id, title, content, createdAt</li>
                    <li><strong>分类状态：</strong>type, isRead, priority</li>
                    <li><strong>关联信息：</strong>senderId, sender, relatedId, relatedType</li>
                    <li><strong>操作字段：</strong>actionUrl, actionText</li>
                </ul>
                
                <h4>支持的消息类型：</h4>
                <ul>
                    <li><code>system</code> - 系统通知</li>
                    <li><code>project_application</code> - 项目申请</li>
                    <li><code>project_invitation</code> - 项目邀请</li>
                    <li><code>project_update</code> - 项目更新</li>
                    <li><code>payment</code> - 支付相关</li>
                    <li><code>achievement</code> - 成就奖励</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        async function testGetMessages() {
            const resultDiv = document.getElementById('messages-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在获取消息...';
            
            try {
                const response = await fetch('http://localhost:8000/api/messages');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'api-result';
                    resultDiv.innerHTML = `
                        <strong>获取成功！</strong><br>
                        总消息数: ${data.data.total}<br>
                        未读消息: ${data.data.unreadCount}<br>
                        消息列表: ${data.data.messages.length}条<br>
                        <details>
                            <summary>查看详细数据</summary>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </details>
                    `;
                    
                    // 更新未读数量显示
                    document.getElementById('unread-count').textContent = data.data.unreadCount;
                } else {
                    throw new Error(data.message || '获取失败');
                }
            } catch (error) {
                resultDiv.className = 'api-result error';
                resultDiv.innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        }

        async function testMarkAsRead() {
            const resultDiv = document.getElementById('mark-read-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '正在标记消息已读...';
            
            try {
                const response = await fetch('http://localhost:8000/api/messages/1/read', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'api-result';
                    resultDiv.innerHTML = `<strong>标记成功！</strong><br>消息已标记为已读`;
                    
                    // 重新获取消息数量
                    setTimeout(testGetMessages, 500);
                } else {
                    throw new Error(data.message || '标记失败');
                }
            } catch (error) {
                resultDiv.className = 'api-result error';
                resultDiv.innerHTML = `<strong>错误:</strong> ${error.message}`;
            }
        }

        // 页面加载时自动测试一次
        window.onload = function() {
            testGetMessages();
        };
    </script>
</body>
</html>