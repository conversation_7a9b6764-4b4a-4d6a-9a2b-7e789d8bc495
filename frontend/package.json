{"name": "xiaogailu-frontend", "version": "1.0.0", "description": "小概率 - 项目全生命周期生态平台", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "test": "vitest", "test:unit": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:ui-restoration": "playwright test --config=tests/ui-restoration/ui-test.config.ts", "test:ui-restoration:ui": "playwright test --config=tests/ui-restoration/ui-test.config.ts --ui", "test:ui-restoration:headed": "playwright test --config=tests/ui-restoration/ui-test.config.ts --headed", "test:ui-restoration:report": "playwright show-report tests/ui-restoration/ui-restoration-report", "test:ui-auto": "./scripts/ui-restoration-test.sh", "test:ui-auto:report": "./scripts/ui-restoration-test.sh report", "test:report:generate": "node scripts/generate-test-report.js", "test:metrics:collect": "node scripts/ui-test-metrics.js", "test:metrics:report": "node scripts/ui-test-metrics.js && open tests/ui-restoration/metrics/health-report.html", "test:health:check": "npm run test:metrics:collect && npm run test:ui-restoration", "setup:hooks": "./scripts/install-git-hooks.sh", "test:all": "npm run test:unit && npm run test:e2e && npm run test:ui-restoration", "test:watch": "vitest", "test:ci": "npm run lint && npm run type-check && npm run test:unit && npm run build && npm run test:ui-restoration"}, "dependencies": {"@vueuse/core": "^10.7.0", "axios": "^1.6.0", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@playwright/test": "^1.53.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.19.4", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vitest/coverage-v8": "^1.6.1", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.33.0", "happy-dom": "^12.10.3", "jsdom": "^23.2.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.3.0", "vite": "^5.0.10", "vitest": "^1.6.1", "vue-tsc": "^1.8.25"}}